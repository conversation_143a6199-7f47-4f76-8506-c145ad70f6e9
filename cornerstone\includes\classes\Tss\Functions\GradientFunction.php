<?php

namespace Themeco\Cornerstone\Tss\Functions;

abstract class GradientFunction extends BuiltInFunction {
  static private $globalColors;
  static private $tssRuntime;

  static public function getGlobalColors() {
    if (empty(static::$globalColors)) {
      static::$globalColors = cornerstone('GlobalColors');
    }

    return static::$globalColors;
  }

  static public function getTSSRuntime() {
    if (empty(static::$tssRuntime)) {
      static::$tssRuntime = cornerstone('Tss')->getRuntime();
    }

    return static::$tssRuntime;
  }
}

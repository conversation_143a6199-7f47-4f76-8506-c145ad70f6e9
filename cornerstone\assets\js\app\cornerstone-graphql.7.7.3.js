(()=>{var ph=Object.create;var co=Object.defineProperty;var fh=Object.getOwnPropertyDescriptor;var mh=Object.getOwnPropertyNames;var yh=Object.getPrototypeOf,hh=Object.prototype.hasOwnProperty;var vh=e=>co(e,"__esModule",{value:!0});var bl=(e=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(e,{get:(t,n)=>(typeof require!="undefined"?require:t)[n]}):e)(function(e){if(typeof require!="undefined")return require.apply(this,arguments);throw new Error('Dynamic require of "'+e+'" is not supported')});var _=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Th=(e,t,n,r)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of mh(t))!hh.call(e,i)&&(n||i!=="default")&&co(e,i,{get:()=>t[i],enumerable:!(r=fh(t,i))||r.enumerable});return e},El=(e,t)=>Th(vh(co(e!=null?ph(yh(e)):{},"default",!t&&e&&e.__esModule?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e);var lo=_((SD,_l)=>{_l.exports=window.wp.CodeMirror});var Nl=_(Wn=>{"use strict";Object.defineProperty(Wn,"__esModule",{value:!0});Wn.versionInfo=Wn.version=void 0;var gh="16.8.0";Wn.version=gh;var bh=Object.freeze({major:16,minor:8,patch:0,preReleaseTag:null});Wn.versionInfo=bh});var et=_(po=>{"use strict";Object.defineProperty(po,"__esModule",{value:!0});po.devAssert=Eh;function Eh(e,t){if(!Boolean(e))throw new Error(t)}});var qi=_(fo=>{"use strict";Object.defineProperty(fo,"__esModule",{value:!0});fo.isPromise=_h;function _h(e){return typeof(e==null?void 0:e.then)=="function"}});var qt=_(mo=>{"use strict";Object.defineProperty(mo,"__esModule",{value:!0});mo.isObjectLike=Nh;function Nh(e){return typeof e=="object"&&e!==null}});var He=_(yo=>{"use strict";Object.defineProperty(yo,"__esModule",{value:!0});yo.invariant=Oh;function Oh(e,t){if(!Boolean(e))throw new Error(t!=null?t:"Unexpected invariant triggered.")}});var Vi=_(ho=>{"use strict";Object.defineProperty(ho,"__esModule",{value:!0});ho.getLocation=Sh;var Ih=He(),Dh=/\r\n|[\n\r]/g;function Sh(e,t){let n=0,r=1;for(let i of e.body.matchAll(Dh)){if(typeof i.index=="number"||(0,Ih.invariant)(!1),i.index>=t)break;n=i.index+i[0].length,r+=1}return{line:r,column:t+1-n}}});var vo=_(Mi=>{"use strict";Object.defineProperty(Mi,"__esModule",{value:!0});Mi.printLocation=Rh;Mi.printSourceLocation=Ol;var Lh=Vi();function Rh(e){return Ol(e.source,(0,Lh.getLocation)(e.source,e.start))}function Ol(e,t){let n=e.locationOffset.column-1,r="".padStart(n)+e.body,i=t.line-1,a=e.locationOffset.line-1,o=t.line+a,s=t.line===1?n:0,c=t.column+s,d=`${e.name}:${o}:${c}
`,f=r.split(/\r\n|[\n\r]/g),m=f[i];if(m.length>120){let g=Math.floor(c/80),T=c%80,O=[];for(let U=0;U<m.length;U+=80)O.push(m.slice(U,U+80));return d+Il([[`${o} |`,O[0]],...O.slice(1,g+1).map(U=>["|",U]),["|","^".padStart(T)],["|",O[g+1]]])}return d+Il([[`${o-1} |`,f[i-1]],[`${o} |`,m],["|","^".padStart(c)],[`${o+1} |`,f[i+1]]])}function Il(e){let t=e.filter(([r,i])=>i!==void 0),n=Math.max(...t.map(([r])=>r.length));return t.map(([r,i])=>r.padStart(n)+(i?" "+i:"")).join(`
`)}});var Q=_(Hn=>{"use strict";Object.defineProperty(Hn,"__esModule",{value:!0});Hn.GraphQLError=void 0;Hn.formatError=Fh;Hn.printError=jh;var Ah=qt(),Dl=Vi(),Sl=vo();function Ph(e){let t=e[0];return t==null||"kind"in t||"length"in t?{nodes:t,source:e[1],positions:e[2],path:e[3],originalError:e[4],extensions:e[5]}:t}var Gi=class extends Error{constructor(t,...n){var r,i,a;let{nodes:o,source:s,positions:c,path:d,originalError:f,extensions:m}=Ph(n);super(t);this.name="GraphQLError",this.path=d!=null?d:void 0,this.originalError=f!=null?f:void 0,this.nodes=Ll(Array.isArray(o)?o:o?[o]:void 0);let g=Ll((r=this.nodes)===null||r===void 0?void 0:r.map(O=>O.loc).filter(O=>O!=null));this.source=s!=null?s:g==null||(i=g[0])===null||i===void 0?void 0:i.source,this.positions=c!=null?c:g==null?void 0:g.map(O=>O.start),this.locations=c&&s?c.map(O=>(0,Dl.getLocation)(s,O)):g==null?void 0:g.map(O=>(0,Dl.getLocation)(O.source,O.start));let T=(0,Ah.isObjectLike)(f==null?void 0:f.extensions)?f==null?void 0:f.extensions:void 0;this.extensions=(a=m!=null?m:T)!==null&&a!==void 0?a:Object.create(null),Object.defineProperties(this,{message:{writable:!0,enumerable:!0},name:{enumerable:!1},nodes:{enumerable:!1},source:{enumerable:!1},positions:{enumerable:!1},originalError:{enumerable:!1}}),f!=null&&f.stack?Object.defineProperty(this,"stack",{value:f.stack,writable:!0,configurable:!0}):Error.captureStackTrace?Error.captureStackTrace(this,Gi):Object.defineProperty(this,"stack",{value:Error().stack,writable:!0,configurable:!0})}get[Symbol.toStringTag](){return"GraphQLError"}toString(){let t=this.message;if(this.nodes)for(let n of this.nodes)n.loc&&(t+=`

`+(0,Sl.printLocation)(n.loc));else if(this.source&&this.locations)for(let n of this.locations)t+=`

`+(0,Sl.printSourceLocation)(this.source,n);return t}toJSON(){let t={message:this.message};return this.locations!=null&&(t.locations=this.locations),this.path!=null&&(t.path=this.path),this.extensions!=null&&Object.keys(this.extensions).length>0&&(t.extensions=this.extensions),t}};Hn.GraphQLError=Gi;function Ll(e){return e===void 0||e.length===0?void 0:e}function jh(e){return e.toString()}function Fh(e){return e.toJSON()}});var Ui=_(To=>{"use strict";Object.defineProperty(To,"__esModule",{value:!0});To.syntaxError=wh;var kh=Q();function wh(e,t,n){return new kh.GraphQLError(`Syntax Error: ${n}`,{source:e,positions:[t]})}});var Vt=_(mt=>{"use strict";Object.defineProperty(mt,"__esModule",{value:!0});mt.Token=mt.QueryDocumentKeys=mt.OperationTypeNode=mt.Location=void 0;mt.isNode=qh;var Rl=class{constructor(t,n,r){this.start=t.start,this.end=n.end,this.startToken=t,this.endToken=n,this.source=r}get[Symbol.toStringTag](){return"Location"}toJSON(){return{start:this.start,end:this.end}}};mt.Location=Rl;var Al=class{constructor(t,n,r,i,a,o){this.kind=t,this.start=n,this.end=r,this.line=i,this.column=a,this.value=o,this.prev=null,this.next=null}get[Symbol.toStringTag](){return"Token"}toJSON(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}}};mt.Token=Al;var Pl={Name:[],Document:["definitions"],OperationDefinition:["name","variableDefinitions","directives","selectionSet"],VariableDefinition:["variable","type","defaultValue","directives"],Variable:["name"],SelectionSet:["selections"],Field:["alias","name","arguments","directives","selectionSet"],Argument:["name","value"],FragmentSpread:["name","directives"],InlineFragment:["typeCondition","directives","selectionSet"],FragmentDefinition:["name","variableDefinitions","typeCondition","directives","selectionSet"],IntValue:[],FloatValue:[],StringValue:[],BooleanValue:[],NullValue:[],EnumValue:[],ListValue:["values"],ObjectValue:["fields"],ObjectField:["name","value"],Directive:["name","arguments"],NamedType:["name"],ListType:["type"],NonNullType:["type"],SchemaDefinition:["description","directives","operationTypes"],OperationTypeDefinition:["type"],ScalarTypeDefinition:["description","name","directives"],ObjectTypeDefinition:["description","name","interfaces","directives","fields"],FieldDefinition:["description","name","arguments","type","directives"],InputValueDefinition:["description","name","type","defaultValue","directives"],InterfaceTypeDefinition:["description","name","interfaces","directives","fields"],UnionTypeDefinition:["description","name","directives","types"],EnumTypeDefinition:["description","name","directives","values"],EnumValueDefinition:["description","name","directives"],InputObjectTypeDefinition:["description","name","directives","fields"],DirectiveDefinition:["description","name","arguments","locations"],SchemaExtension:["directives","operationTypes"],ScalarTypeExtension:["name","directives"],ObjectTypeExtension:["name","interfaces","directives","fields"],InterfaceTypeExtension:["name","interfaces","directives","fields"],UnionTypeExtension:["name","directives","types"],EnumTypeExtension:["name","directives","values"],InputObjectTypeExtension:["name","directives","fields"]};mt.QueryDocumentKeys=Pl;var Ch=new Set(Object.keys(Pl));function qh(e){let t=e==null?void 0:e.kind;return typeof t=="string"&&Ch.has(t)}var go;mt.OperationTypeNode=go;(function(e){e.QUERY="query",e.MUTATION="mutation",e.SUBSCRIPTION="subscription"})(go||(mt.OperationTypeNode=go={}))});var zn=_(Or=>{"use strict";Object.defineProperty(Or,"__esModule",{value:!0});Or.DirectiveLocation=void 0;var bo;Or.DirectiveLocation=bo;(function(e){e.QUERY="QUERY",e.MUTATION="MUTATION",e.SUBSCRIPTION="SUBSCRIPTION",e.FIELD="FIELD",e.FRAGMENT_DEFINITION="FRAGMENT_DEFINITION",e.FRAGMENT_SPREAD="FRAGMENT_SPREAD",e.INLINE_FRAGMENT="INLINE_FRAGMENT",e.VARIABLE_DEFINITION="VARIABLE_DEFINITION",e.SCHEMA="SCHEMA",e.SCALAR="SCALAR",e.OBJECT="OBJECT",e.FIELD_DEFINITION="FIELD_DEFINITION",e.ARGUMENT_DEFINITION="ARGUMENT_DEFINITION",e.INTERFACE="INTERFACE",e.UNION="UNION",e.ENUM="ENUM",e.ENUM_VALUE="ENUM_VALUE",e.INPUT_OBJECT="INPUT_OBJECT",e.INPUT_FIELD_DEFINITION="INPUT_FIELD_DEFINITION"})(bo||(Or.DirectiveLocation=bo={}))});var ie=_(Ir=>{"use strict";Object.defineProperty(Ir,"__esModule",{value:!0});Ir.Kind=void 0;var Eo;Ir.Kind=Eo;(function(e){e.NAME="Name",e.DOCUMENT="Document",e.OPERATION_DEFINITION="OperationDefinition",e.VARIABLE_DEFINITION="VariableDefinition",e.SELECTION_SET="SelectionSet",e.FIELD="Field",e.ARGUMENT="Argument",e.FRAGMENT_SPREAD="FragmentSpread",e.INLINE_FRAGMENT="InlineFragment",e.FRAGMENT_DEFINITION="FragmentDefinition",e.VARIABLE="Variable",e.INT="IntValue",e.FLOAT="FloatValue",e.STRING="StringValue",e.BOOLEAN="BooleanValue",e.NULL="NullValue",e.ENUM="EnumValue",e.LIST="ListValue",e.OBJECT="ObjectValue",e.OBJECT_FIELD="ObjectField",e.DIRECTIVE="Directive",e.NAMED_TYPE="NamedType",e.LIST_TYPE="ListType",e.NON_NULL_TYPE="NonNullType",e.SCHEMA_DEFINITION="SchemaDefinition",e.OPERATION_TYPE_DEFINITION="OperationTypeDefinition",e.SCALAR_TYPE_DEFINITION="ScalarTypeDefinition",e.OBJECT_TYPE_DEFINITION="ObjectTypeDefinition",e.FIELD_DEFINITION="FieldDefinition",e.INPUT_VALUE_DEFINITION="InputValueDefinition",e.INTERFACE_TYPE_DEFINITION="InterfaceTypeDefinition",e.UNION_TYPE_DEFINITION="UnionTypeDefinition",e.ENUM_TYPE_DEFINITION="EnumTypeDefinition",e.ENUM_VALUE_DEFINITION="EnumValueDefinition",e.INPUT_OBJECT_TYPE_DEFINITION="InputObjectTypeDefinition",e.DIRECTIVE_DEFINITION="DirectiveDefinition",e.SCHEMA_EXTENSION="SchemaExtension",e.SCALAR_TYPE_EXTENSION="ScalarTypeExtension",e.OBJECT_TYPE_EXTENSION="ObjectTypeExtension",e.INTERFACE_TYPE_EXTENSION="InterfaceTypeExtension",e.UNION_TYPE_EXTENSION="UnionTypeExtension",e.ENUM_TYPE_EXTENSION="EnumTypeExtension",e.INPUT_OBJECT_TYPE_EXTENSION="InputObjectTypeExtension"})(Eo||(Ir.Kind=Eo={}))});var Qi=_(On=>{"use strict";Object.defineProperty(On,"__esModule",{value:!0});On.isDigit=jl;On.isLetter=_o;On.isNameContinue=Gh;On.isNameStart=Mh;On.isWhiteSpace=Vh;function Vh(e){return e===9||e===32}function jl(e){return e>=48&&e<=57}function _o(e){return e>=97&&e<=122||e>=65&&e<=90}function Mh(e){return _o(e)||e===95}function Gh(e){return _o(e)||jl(e)||e===95}});var Sr=_(Dr=>{"use strict";Object.defineProperty(Dr,"__esModule",{value:!0});Dr.dedentBlockStringLines=Uh;Dr.isPrintableAsBlockString=Kh;Dr.printBlockString=xh;var No=Qi();function Uh(e){var t;let n=Number.MAX_SAFE_INTEGER,r=null,i=-1;for(let o=0;o<e.length;++o){var a;let s=e[o],c=Qh(s);c!==s.length&&(r=(a=r)!==null&&a!==void 0?a:o,i=o,o!==0&&c<n&&(n=c))}return e.map((o,s)=>s===0?o:o.slice(n)).slice((t=r)!==null&&t!==void 0?t:0,i+1)}function Qh(e){let t=0;for(;t<e.length&&(0,No.isWhiteSpace)(e.charCodeAt(t));)++t;return t}function Kh(e){if(e==="")return!0;let t=!0,n=!1,r=!0,i=!1;for(let a=0;a<e.length;++a)switch(e.codePointAt(a)){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 11:case 12:case 14:case 15:return!1;case 13:return!1;case 10:if(t&&!i)return!1;i=!0,t=!0,n=!1;break;case 9:case 32:n||(n=t);break;default:r&&(r=n),t=!1}return!(t||r&&i)}function xh(e,t){let n=e.replace(/"""/g,'\\"""'),r=n.split(/\r\n|[\n\r]/g),i=r.length===1,a=r.length>1&&r.slice(1).every(T=>T.length===0||(0,No.isWhiteSpace)(T.charCodeAt(0))),o=n.endsWith('\\"""'),s=e.endsWith('"')&&!o,c=e.endsWith("\\"),d=s||c,f=!(t!=null&&t.minimize)&&(!i||e.length>70||d||a||o),m="",g=i&&(0,No.isWhiteSpace)(e.charCodeAt(0));return(f&&!g||a)&&(m+=`
`),m+=n,(f||d)&&(m+=`
`),'"""'+m+'"""'}});var Rr=_(Lr=>{"use strict";Object.defineProperty(Lr,"__esModule",{value:!0});Lr.TokenKind=void 0;var Oo;Lr.TokenKind=Oo;(function(e){e.SOF="<SOF>",e.EOF="<EOF>",e.BANG="!",e.DOLLAR="$",e.AMP="&",e.PAREN_L="(",e.PAREN_R=")",e.SPREAD="...",e.COLON=":",e.EQUALS="=",e.AT="@",e.BRACKET_L="[",e.BRACKET_R="]",e.BRACE_L="{",e.PIPE="|",e.BRACE_R="}",e.NAME="Name",e.INT="Int",e.FLOAT="Float",e.STRING="String",e.BLOCK_STRING="BlockString",e.COMMENT="Comment"})(Oo||(Lr.TokenKind=Oo={}))});var xi=_(Pr=>{"use strict";Object.defineProperty(Pr,"__esModule",{value:!0});Pr.Lexer=void 0;Pr.isPunctuatorTokenKind=Bh;var Rt=Ui(),Fl=Vt(),$h=Sr(),In=Qi(),W=Rr(),kl=class{constructor(t){let n=new Fl.Token(W.TokenKind.SOF,0,0,0,0);this.source=t,this.lastToken=n,this.token=n,this.line=1,this.lineStart=0}get[Symbol.toStringTag](){return"Lexer"}advance(){return this.lastToken=this.token,this.token=this.lookahead()}lookahead(){let t=this.token;if(t.kind!==W.TokenKind.EOF)do if(t.next)t=t.next;else{let n=Yh(this,t.end);t.next=n,n.prev=t,t=n}while(t.kind===W.TokenKind.COMMENT);return t}};Pr.Lexer=kl;function Bh(e){return e===W.TokenKind.BANG||e===W.TokenKind.DOLLAR||e===W.TokenKind.AMP||e===W.TokenKind.PAREN_L||e===W.TokenKind.PAREN_R||e===W.TokenKind.SPREAD||e===W.TokenKind.COLON||e===W.TokenKind.EQUALS||e===W.TokenKind.AT||e===W.TokenKind.BRACKET_L||e===W.TokenKind.BRACKET_R||e===W.TokenKind.BRACE_L||e===W.TokenKind.PIPE||e===W.TokenKind.BRACE_R}function Zn(e){return e>=0&&e<=55295||e>=57344&&e<=1114111}function Ki(e,t){return wl(e.charCodeAt(t))&&Cl(e.charCodeAt(t+1))}function wl(e){return e>=55296&&e<=56319}function Cl(e){return e>=56320&&e<=57343}function Dn(e,t){let n=e.source.body.codePointAt(t);if(n===void 0)return W.TokenKind.EOF;if(n>=32&&n<=126){let r=String.fromCodePoint(n);return r==='"'?`'"'`:`"${r}"`}return"U+"+n.toString(16).toUpperCase().padStart(4,"0")}function Ce(e,t,n,r,i){let a=e.line,o=1+n-e.lineStart;return new Fl.Token(t,n,r,a,o,i)}function Yh(e,t){let n=e.source.body,r=n.length,i=t;for(;i<r;){let a=n.charCodeAt(i);switch(a){case 65279:case 9:case 32:case 44:++i;continue;case 10:++i,++e.line,e.lineStart=i;continue;case 13:n.charCodeAt(i+1)===10?i+=2:++i,++e.line,e.lineStart=i;continue;case 35:return Jh(e,i);case 33:return Ce(e,W.TokenKind.BANG,i,i+1);case 36:return Ce(e,W.TokenKind.DOLLAR,i,i+1);case 38:return Ce(e,W.TokenKind.AMP,i,i+1);case 40:return Ce(e,W.TokenKind.PAREN_L,i,i+1);case 41:return Ce(e,W.TokenKind.PAREN_R,i,i+1);case 46:if(n.charCodeAt(i+1)===46&&n.charCodeAt(i+2)===46)return Ce(e,W.TokenKind.SPREAD,i,i+3);break;case 58:return Ce(e,W.TokenKind.COLON,i,i+1);case 61:return Ce(e,W.TokenKind.EQUALS,i,i+1);case 64:return Ce(e,W.TokenKind.AT,i,i+1);case 91:return Ce(e,W.TokenKind.BRACKET_L,i,i+1);case 93:return Ce(e,W.TokenKind.BRACKET_R,i,i+1);case 123:return Ce(e,W.TokenKind.BRACE_L,i,i+1);case 124:return Ce(e,W.TokenKind.PIPE,i,i+1);case 125:return Ce(e,W.TokenKind.BRACE_R,i,i+1);case 34:return n.charCodeAt(i+1)===34&&n.charCodeAt(i+2)===34?ev(e,i):Wh(e,i)}if((0,In.isDigit)(a)||a===45)return Xh(e,i,a);if((0,In.isNameStart)(a))return tv(e,i);throw(0,Rt.syntaxError)(e.source,i,a===39?`Unexpected single quote character ('), did you mean to use a double quote (")?`:Zn(a)||Ki(n,i)?`Unexpected character: ${Dn(e,i)}.`:`Invalid character: ${Dn(e,i)}.`)}return Ce(e,W.TokenKind.EOF,r,r)}function Jh(e,t){let n=e.source.body,r=n.length,i=t+1;for(;i<r;){let a=n.charCodeAt(i);if(a===10||a===13)break;if(Zn(a))++i;else if(Ki(n,i))i+=2;else break}return Ce(e,W.TokenKind.COMMENT,t,i,n.slice(t+1,i))}function Xh(e,t,n){let r=e.source.body,i=t,a=n,o=!1;if(a===45&&(a=r.charCodeAt(++i)),a===48){if(a=r.charCodeAt(++i),(0,In.isDigit)(a))throw(0,Rt.syntaxError)(e.source,i,`Invalid number, unexpected digit after 0: ${Dn(e,i)}.`)}else i=Io(e,i,a),a=r.charCodeAt(i);if(a===46&&(o=!0,a=r.charCodeAt(++i),i=Io(e,i,a),a=r.charCodeAt(i)),(a===69||a===101)&&(o=!0,a=r.charCodeAt(++i),(a===43||a===45)&&(a=r.charCodeAt(++i)),i=Io(e,i,a),a=r.charCodeAt(i)),a===46||(0,In.isNameStart)(a))throw(0,Rt.syntaxError)(e.source,i,`Invalid number, expected digit but got: ${Dn(e,i)}.`);return Ce(e,o?W.TokenKind.FLOAT:W.TokenKind.INT,t,i,r.slice(t,i))}function Io(e,t,n){if(!(0,In.isDigit)(n))throw(0,Rt.syntaxError)(e.source,t,`Invalid number, expected digit but got: ${Dn(e,t)}.`);let r=e.source.body,i=t+1;for(;(0,In.isDigit)(r.charCodeAt(i));)++i;return i}function Wh(e,t){let n=e.source.body,r=n.length,i=t+1,a=i,o="";for(;i<r;){let s=n.charCodeAt(i);if(s===34)return o+=n.slice(a,i),Ce(e,W.TokenKind.STRING,t,i+1,o);if(s===92){o+=n.slice(a,i);let c=n.charCodeAt(i+1)===117?n.charCodeAt(i+2)===123?Hh(e,i):zh(e,i):Zh(e,i);o+=c.value,i+=c.size,a=i;continue}if(s===10||s===13)break;if(Zn(s))++i;else if(Ki(n,i))i+=2;else throw(0,Rt.syntaxError)(e.source,i,`Invalid character within String: ${Dn(e,i)}.`)}throw(0,Rt.syntaxError)(e.source,i,"Unterminated string.")}function Hh(e,t){let n=e.source.body,r=0,i=3;for(;i<12;){let a=n.charCodeAt(t+i++);if(a===125){if(i<5||!Zn(r))break;return{value:String.fromCodePoint(r),size:i}}if(r=r<<4|Ar(a),r<0)break}throw(0,Rt.syntaxError)(e.source,t,`Invalid Unicode escape sequence: "${n.slice(t,t+i)}".`)}function zh(e,t){let n=e.source.body,r=ql(n,t+2);if(Zn(r))return{value:String.fromCodePoint(r),size:6};if(wl(r)&&n.charCodeAt(t+6)===92&&n.charCodeAt(t+7)===117){let i=ql(n,t+8);if(Cl(i))return{value:String.fromCodePoint(r,i),size:12}}throw(0,Rt.syntaxError)(e.source,t,`Invalid Unicode escape sequence: "${n.slice(t,t+6)}".`)}function ql(e,t){return Ar(e.charCodeAt(t))<<12|Ar(e.charCodeAt(t+1))<<8|Ar(e.charCodeAt(t+2))<<4|Ar(e.charCodeAt(t+3))}function Ar(e){return e>=48&&e<=57?e-48:e>=65&&e<=70?e-55:e>=97&&e<=102?e-87:-1}function Zh(e,t){let n=e.source.body;switch(n.charCodeAt(t+1)){case 34:return{value:'"',size:2};case 92:return{value:"\\",size:2};case 47:return{value:"/",size:2};case 98:return{value:"\b",size:2};case 102:return{value:"\f",size:2};case 110:return{value:`
`,size:2};case 114:return{value:"\r",size:2};case 116:return{value:"	",size:2}}throw(0,Rt.syntaxError)(e.source,t,`Invalid character escape sequence: "${n.slice(t,t+2)}".`)}function ev(e,t){let n=e.source.body,r=n.length,i=e.lineStart,a=t+3,o=a,s="",c=[];for(;a<r;){let d=n.charCodeAt(a);if(d===34&&n.charCodeAt(a+1)===34&&n.charCodeAt(a+2)===34){s+=n.slice(o,a),c.push(s);let f=Ce(e,W.TokenKind.BLOCK_STRING,t,a+3,(0,$h.dedentBlockStringLines)(c).join(`
`));return e.line+=c.length-1,e.lineStart=i,f}if(d===92&&n.charCodeAt(a+1)===34&&n.charCodeAt(a+2)===34&&n.charCodeAt(a+3)===34){s+=n.slice(o,a),o=a+1,a+=4;continue}if(d===10||d===13){s+=n.slice(o,a),c.push(s),d===13&&n.charCodeAt(a+1)===10?a+=2:++a,s="",o=a,i=a;continue}if(Zn(d))++a;else if(Ki(n,a))a+=2;else throw(0,Rt.syntaxError)(e.source,a,`Invalid character within String: ${Dn(e,a)}.`)}throw(0,Rt.syntaxError)(e.source,a,"Unterminated string.")}function tv(e,t){let n=e.source.body,r=n.length,i=t+1;for(;i<r;){let a=n.charCodeAt(i);if((0,In.isNameContinue)(a))++i;else break}return Ce(e,W.TokenKind.NAME,t,i,n.slice(t,i))}});var pe=_(Do=>{"use strict";Object.defineProperty(Do,"__esModule",{value:!0});Do.inspect=rv;var nv=10,Vl=2;function rv(e){return $i(e,[])}function $i(e,t){switch(typeof e){case"string":return JSON.stringify(e);case"function":return e.name?`[function ${e.name}]`:"[function]";case"object":return iv(e,t);default:return String(e)}}function iv(e,t){if(e===null)return"null";if(t.includes(e))return"[Circular]";let n=[...t,e];if(av(e)){let r=e.toJSON();if(r!==e)return typeof r=="string"?r:$i(r,n)}else if(Array.isArray(e))return sv(e,n);return ov(e,n)}function av(e){return typeof e.toJSON=="function"}function ov(e,t){let n=Object.entries(e);if(n.length===0)return"{}";if(t.length>Vl)return"["+uv(e)+"]";let r=n.map(([i,a])=>i+": "+$i(a,t));return"{ "+r.join(", ")+" }"}function sv(e,t){if(e.length===0)return"[]";if(t.length>Vl)return"[Array]";let n=Math.min(nv,e.length),r=e.length-n,i=[];for(let a=0;a<n;++a)i.push($i(e[a],t));return r===1?i.push("... 1 more item"):r>1&&i.push(`... ${r} more items`),"["+i.join(", ")+"]"}function uv(e){let t=Object.prototype.toString.call(e).replace(/^\[object /,"").replace(/]$/,"");if(t==="Object"&&typeof e.constructor=="function"){let n=e.constructor.name;if(typeof n=="string"&&n!=="")return n}return t}});var jr=_(Bi=>{"use strict";Object.defineProperty(Bi,"__esModule",{value:!0});Bi.instanceOf=void 0;var cv=pe(),lv=globalThis.process&&globalThis.process.env.NODE_ENV==="production"?function(t,n){return t instanceof n}:function(t,n){if(t instanceof n)return!0;if(typeof t=="object"&&t!==null){var r;let i=n.prototype[Symbol.toStringTag],a=Symbol.toStringTag in t?t[Symbol.toStringTag]:(r=t.constructor)===null||r===void 0?void 0:r.name;if(i===a){let o=(0,cv.inspect)(t);throw new Error(`Cannot use ${i} "${o}" from another module or realm.

Ensure that there is only one instance of "graphql" in the node_modules
directory. If different versions of "graphql" are the dependencies of other
relied on modules, use "resolutions" to ensure only one version is installed.

https://yarnpkg.com/en/docs/selective-version-resolutions

Duplicate "graphql" modules cannot be used at the same time since different
versions may have different capabilities and behavior. The data from one
version used in the function from another could produce confusing and
spurious results.`)}}return!1};Bi.instanceOf=lv});var Yi=_(Fr=>{"use strict";Object.defineProperty(Fr,"__esModule",{value:!0});Fr.Source=void 0;Fr.isSource=fv;var So=et(),dv=pe(),pv=jr(),Lo=class{constructor(t,n="GraphQL request",r={line:1,column:1}){typeof t=="string"||(0,So.devAssert)(!1,`Body must be a string. Received: ${(0,dv.inspect)(t)}.`),this.body=t,this.name=n,this.locationOffset=r,this.locationOffset.line>0||(0,So.devAssert)(!1,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||(0,So.devAssert)(!1,"column in locationOffset is 1-indexed and must be positive.")}get[Symbol.toStringTag](){return"Source"}};Fr.Source=Lo;function fv(e){return(0,pv.instanceOf)(e,Lo)}});var tr=_(dn=>{"use strict";Object.defineProperty(dn,"__esModule",{value:!0});dn.Parser=void 0;dn.parse=yv;dn.parseConstValue=vv;dn.parseType=Tv;dn.parseValue=hv;var Sn=Ui(),kr=Vt(),mv=zn(),$=ie(),Ml=xi(),Gl=Yi(),F=Rr();function yv(e,t){return new er(e,t).parseDocument()}function hv(e,t){let n=new er(e,t);n.expectToken(F.TokenKind.SOF);let r=n.parseValueLiteral(!1);return n.expectToken(F.TokenKind.EOF),r}function vv(e,t){let n=new er(e,t);n.expectToken(F.TokenKind.SOF);let r=n.parseConstValueLiteral();return n.expectToken(F.TokenKind.EOF),r}function Tv(e,t){let n=new er(e,t);n.expectToken(F.TokenKind.SOF);let r=n.parseTypeReference();return n.expectToken(F.TokenKind.EOF),r}var er=class{constructor(t,n={}){let r=(0,Gl.isSource)(t)?t:new Gl.Source(t);this._lexer=new Ml.Lexer(r),this._options=n,this._tokenCounter=0}parseName(){let t=this.expectToken(F.TokenKind.NAME);return this.node(t,{kind:$.Kind.NAME,value:t.value})}parseDocument(){return this.node(this._lexer.token,{kind:$.Kind.DOCUMENT,definitions:this.many(F.TokenKind.SOF,this.parseDefinition,F.TokenKind.EOF)})}parseDefinition(){if(this.peek(F.TokenKind.BRACE_L))return this.parseOperationDefinition();let t=this.peekDescription(),n=t?this._lexer.lookahead():this._lexer.token;if(n.kind===F.TokenKind.NAME){switch(n.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}if(t)throw(0,Sn.syntaxError)(this._lexer.source,this._lexer.token.start,"Unexpected description, descriptions are supported only on type definitions.");switch(n.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"extend":return this.parseTypeSystemExtension()}}throw this.unexpected(n)}parseOperationDefinition(){let t=this._lexer.token;if(this.peek(F.TokenKind.BRACE_L))return this.node(t,{kind:$.Kind.OPERATION_DEFINITION,operation:kr.OperationTypeNode.QUERY,name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet()});let n=this.parseOperationType(),r;return this.peek(F.TokenKind.NAME)&&(r=this.parseName()),this.node(t,{kind:$.Kind.OPERATION_DEFINITION,operation:n,name:r,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseOperationType(){let t=this.expectToken(F.TokenKind.NAME);switch(t.value){case"query":return kr.OperationTypeNode.QUERY;case"mutation":return kr.OperationTypeNode.MUTATION;case"subscription":return kr.OperationTypeNode.SUBSCRIPTION}throw this.unexpected(t)}parseVariableDefinitions(){return this.optionalMany(F.TokenKind.PAREN_L,this.parseVariableDefinition,F.TokenKind.PAREN_R)}parseVariableDefinition(){return this.node(this._lexer.token,{kind:$.Kind.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(F.TokenKind.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(F.TokenKind.EQUALS)?this.parseConstValueLiteral():void 0,directives:this.parseConstDirectives()})}parseVariable(){let t=this._lexer.token;return this.expectToken(F.TokenKind.DOLLAR),this.node(t,{kind:$.Kind.VARIABLE,name:this.parseName()})}parseSelectionSet(){return this.node(this._lexer.token,{kind:$.Kind.SELECTION_SET,selections:this.many(F.TokenKind.BRACE_L,this.parseSelection,F.TokenKind.BRACE_R)})}parseSelection(){return this.peek(F.TokenKind.SPREAD)?this.parseFragment():this.parseField()}parseField(){let t=this._lexer.token,n=this.parseName(),r,i;return this.expectOptionalToken(F.TokenKind.COLON)?(r=n,i=this.parseName()):i=n,this.node(t,{kind:$.Kind.FIELD,alias:r,name:i,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(F.TokenKind.BRACE_L)?this.parseSelectionSet():void 0})}parseArguments(t){let n=t?this.parseConstArgument:this.parseArgument;return this.optionalMany(F.TokenKind.PAREN_L,n,F.TokenKind.PAREN_R)}parseArgument(t=!1){let n=this._lexer.token,r=this.parseName();return this.expectToken(F.TokenKind.COLON),this.node(n,{kind:$.Kind.ARGUMENT,name:r,value:this.parseValueLiteral(t)})}parseConstArgument(){return this.parseArgument(!0)}parseFragment(){let t=this._lexer.token;this.expectToken(F.TokenKind.SPREAD);let n=this.expectOptionalKeyword("on");return!n&&this.peek(F.TokenKind.NAME)?this.node(t,{kind:$.Kind.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1)}):this.node(t,{kind:$.Kind.INLINE_FRAGMENT,typeCondition:n?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentDefinition(){let t=this._lexer.token;return this.expectKeyword("fragment"),this._options.allowLegacyFragmentVariables===!0?this.node(t,{kind:$.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()}):this.node(t,{kind:$.Kind.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentName(){if(this._lexer.token.value==="on")throw this.unexpected();return this.parseName()}parseValueLiteral(t){let n=this._lexer.token;switch(n.kind){case F.TokenKind.BRACKET_L:return this.parseList(t);case F.TokenKind.BRACE_L:return this.parseObject(t);case F.TokenKind.INT:return this.advanceLexer(),this.node(n,{kind:$.Kind.INT,value:n.value});case F.TokenKind.FLOAT:return this.advanceLexer(),this.node(n,{kind:$.Kind.FLOAT,value:n.value});case F.TokenKind.STRING:case F.TokenKind.BLOCK_STRING:return this.parseStringLiteral();case F.TokenKind.NAME:switch(this.advanceLexer(),n.value){case"true":return this.node(n,{kind:$.Kind.BOOLEAN,value:!0});case"false":return this.node(n,{kind:$.Kind.BOOLEAN,value:!1});case"null":return this.node(n,{kind:$.Kind.NULL});default:return this.node(n,{kind:$.Kind.ENUM,value:n.value})}case F.TokenKind.DOLLAR:if(t)if(this.expectToken(F.TokenKind.DOLLAR),this._lexer.token.kind===F.TokenKind.NAME){let r=this._lexer.token.value;throw(0,Sn.syntaxError)(this._lexer.source,n.start,`Unexpected variable "$${r}" in constant value.`)}else throw this.unexpected(n);return this.parseVariable();default:throw this.unexpected()}}parseConstValueLiteral(){return this.parseValueLiteral(!0)}parseStringLiteral(){let t=this._lexer.token;return this.advanceLexer(),this.node(t,{kind:$.Kind.STRING,value:t.value,block:t.kind===F.TokenKind.BLOCK_STRING})}parseList(t){let n=()=>this.parseValueLiteral(t);return this.node(this._lexer.token,{kind:$.Kind.LIST,values:this.any(F.TokenKind.BRACKET_L,n,F.TokenKind.BRACKET_R)})}parseObject(t){let n=()=>this.parseObjectField(t);return this.node(this._lexer.token,{kind:$.Kind.OBJECT,fields:this.any(F.TokenKind.BRACE_L,n,F.TokenKind.BRACE_R)})}parseObjectField(t){let n=this._lexer.token,r=this.parseName();return this.expectToken(F.TokenKind.COLON),this.node(n,{kind:$.Kind.OBJECT_FIELD,name:r,value:this.parseValueLiteral(t)})}parseDirectives(t){let n=[];for(;this.peek(F.TokenKind.AT);)n.push(this.parseDirective(t));return n}parseConstDirectives(){return this.parseDirectives(!0)}parseDirective(t){let n=this._lexer.token;return this.expectToken(F.TokenKind.AT),this.node(n,{kind:$.Kind.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(t)})}parseTypeReference(){let t=this._lexer.token,n;if(this.expectOptionalToken(F.TokenKind.BRACKET_L)){let r=this.parseTypeReference();this.expectToken(F.TokenKind.BRACKET_R),n=this.node(t,{kind:$.Kind.LIST_TYPE,type:r})}else n=this.parseNamedType();return this.expectOptionalToken(F.TokenKind.BANG)?this.node(t,{kind:$.Kind.NON_NULL_TYPE,type:n}):n}parseNamedType(){return this.node(this._lexer.token,{kind:$.Kind.NAMED_TYPE,name:this.parseName()})}peekDescription(){return this.peek(F.TokenKind.STRING)||this.peek(F.TokenKind.BLOCK_STRING)}parseDescription(){if(this.peekDescription())return this.parseStringLiteral()}parseSchemaDefinition(){let t=this._lexer.token,n=this.parseDescription();this.expectKeyword("schema");let r=this.parseConstDirectives(),i=this.many(F.TokenKind.BRACE_L,this.parseOperationTypeDefinition,F.TokenKind.BRACE_R);return this.node(t,{kind:$.Kind.SCHEMA_DEFINITION,description:n,directives:r,operationTypes:i})}parseOperationTypeDefinition(){let t=this._lexer.token,n=this.parseOperationType();this.expectToken(F.TokenKind.COLON);let r=this.parseNamedType();return this.node(t,{kind:$.Kind.OPERATION_TYPE_DEFINITION,operation:n,type:r})}parseScalarTypeDefinition(){let t=this._lexer.token,n=this.parseDescription();this.expectKeyword("scalar");let r=this.parseName(),i=this.parseConstDirectives();return this.node(t,{kind:$.Kind.SCALAR_TYPE_DEFINITION,description:n,name:r,directives:i})}parseObjectTypeDefinition(){let t=this._lexer.token,n=this.parseDescription();this.expectKeyword("type");let r=this.parseName(),i=this.parseImplementsInterfaces(),a=this.parseConstDirectives(),o=this.parseFieldsDefinition();return this.node(t,{kind:$.Kind.OBJECT_TYPE_DEFINITION,description:n,name:r,interfaces:i,directives:a,fields:o})}parseImplementsInterfaces(){return this.expectOptionalKeyword("implements")?this.delimitedMany(F.TokenKind.AMP,this.parseNamedType):[]}parseFieldsDefinition(){return this.optionalMany(F.TokenKind.BRACE_L,this.parseFieldDefinition,F.TokenKind.BRACE_R)}parseFieldDefinition(){let t=this._lexer.token,n=this.parseDescription(),r=this.parseName(),i=this.parseArgumentDefs();this.expectToken(F.TokenKind.COLON);let a=this.parseTypeReference(),o=this.parseConstDirectives();return this.node(t,{kind:$.Kind.FIELD_DEFINITION,description:n,name:r,arguments:i,type:a,directives:o})}parseArgumentDefs(){return this.optionalMany(F.TokenKind.PAREN_L,this.parseInputValueDef,F.TokenKind.PAREN_R)}parseInputValueDef(){let t=this._lexer.token,n=this.parseDescription(),r=this.parseName();this.expectToken(F.TokenKind.COLON);let i=this.parseTypeReference(),a;this.expectOptionalToken(F.TokenKind.EQUALS)&&(a=this.parseConstValueLiteral());let o=this.parseConstDirectives();return this.node(t,{kind:$.Kind.INPUT_VALUE_DEFINITION,description:n,name:r,type:i,defaultValue:a,directives:o})}parseInterfaceTypeDefinition(){let t=this._lexer.token,n=this.parseDescription();this.expectKeyword("interface");let r=this.parseName(),i=this.parseImplementsInterfaces(),a=this.parseConstDirectives(),o=this.parseFieldsDefinition();return this.node(t,{kind:$.Kind.INTERFACE_TYPE_DEFINITION,description:n,name:r,interfaces:i,directives:a,fields:o})}parseUnionTypeDefinition(){let t=this._lexer.token,n=this.parseDescription();this.expectKeyword("union");let r=this.parseName(),i=this.parseConstDirectives(),a=this.parseUnionMemberTypes();return this.node(t,{kind:$.Kind.UNION_TYPE_DEFINITION,description:n,name:r,directives:i,types:a})}parseUnionMemberTypes(){return this.expectOptionalToken(F.TokenKind.EQUALS)?this.delimitedMany(F.TokenKind.PIPE,this.parseNamedType):[]}parseEnumTypeDefinition(){let t=this._lexer.token,n=this.parseDescription();this.expectKeyword("enum");let r=this.parseName(),i=this.parseConstDirectives(),a=this.parseEnumValuesDefinition();return this.node(t,{kind:$.Kind.ENUM_TYPE_DEFINITION,description:n,name:r,directives:i,values:a})}parseEnumValuesDefinition(){return this.optionalMany(F.TokenKind.BRACE_L,this.parseEnumValueDefinition,F.TokenKind.BRACE_R)}parseEnumValueDefinition(){let t=this._lexer.token,n=this.parseDescription(),r=this.parseEnumValueName(),i=this.parseConstDirectives();return this.node(t,{kind:$.Kind.ENUM_VALUE_DEFINITION,description:n,name:r,directives:i})}parseEnumValueName(){if(this._lexer.token.value==="true"||this._lexer.token.value==="false"||this._lexer.token.value==="null")throw(0,Sn.syntaxError)(this._lexer.source,this._lexer.token.start,`${Ji(this._lexer.token)} is reserved and cannot be used for an enum value.`);return this.parseName()}parseInputObjectTypeDefinition(){let t=this._lexer.token,n=this.parseDescription();this.expectKeyword("input");let r=this.parseName(),i=this.parseConstDirectives(),a=this.parseInputFieldsDefinition();return this.node(t,{kind:$.Kind.INPUT_OBJECT_TYPE_DEFINITION,description:n,name:r,directives:i,fields:a})}parseInputFieldsDefinition(){return this.optionalMany(F.TokenKind.BRACE_L,this.parseInputValueDef,F.TokenKind.BRACE_R)}parseTypeSystemExtension(){let t=this._lexer.lookahead();if(t.kind===F.TokenKind.NAME)switch(t.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(t)}parseSchemaExtension(){let t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");let n=this.parseConstDirectives(),r=this.optionalMany(F.TokenKind.BRACE_L,this.parseOperationTypeDefinition,F.TokenKind.BRACE_R);if(n.length===0&&r.length===0)throw this.unexpected();return this.node(t,{kind:$.Kind.SCHEMA_EXTENSION,directives:n,operationTypes:r})}parseScalarTypeExtension(){let t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");let n=this.parseName(),r=this.parseConstDirectives();if(r.length===0)throw this.unexpected();return this.node(t,{kind:$.Kind.SCALAR_TYPE_EXTENSION,name:n,directives:r})}parseObjectTypeExtension(){let t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");let n=this.parseName(),r=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),a=this.parseFieldsDefinition();if(r.length===0&&i.length===0&&a.length===0)throw this.unexpected();return this.node(t,{kind:$.Kind.OBJECT_TYPE_EXTENSION,name:n,interfaces:r,directives:i,fields:a})}parseInterfaceTypeExtension(){let t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");let n=this.parseName(),r=this.parseImplementsInterfaces(),i=this.parseConstDirectives(),a=this.parseFieldsDefinition();if(r.length===0&&i.length===0&&a.length===0)throw this.unexpected();return this.node(t,{kind:$.Kind.INTERFACE_TYPE_EXTENSION,name:n,interfaces:r,directives:i,fields:a})}parseUnionTypeExtension(){let t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");let n=this.parseName(),r=this.parseConstDirectives(),i=this.parseUnionMemberTypes();if(r.length===0&&i.length===0)throw this.unexpected();return this.node(t,{kind:$.Kind.UNION_TYPE_EXTENSION,name:n,directives:r,types:i})}parseEnumTypeExtension(){let t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");let n=this.parseName(),r=this.parseConstDirectives(),i=this.parseEnumValuesDefinition();if(r.length===0&&i.length===0)throw this.unexpected();return this.node(t,{kind:$.Kind.ENUM_TYPE_EXTENSION,name:n,directives:r,values:i})}parseInputObjectTypeExtension(){let t=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");let n=this.parseName(),r=this.parseConstDirectives(),i=this.parseInputFieldsDefinition();if(r.length===0&&i.length===0)throw this.unexpected();return this.node(t,{kind:$.Kind.INPUT_OBJECT_TYPE_EXTENSION,name:n,directives:r,fields:i})}parseDirectiveDefinition(){let t=this._lexer.token,n=this.parseDescription();this.expectKeyword("directive"),this.expectToken(F.TokenKind.AT);let r=this.parseName(),i=this.parseArgumentDefs(),a=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");let o=this.parseDirectiveLocations();return this.node(t,{kind:$.Kind.DIRECTIVE_DEFINITION,description:n,name:r,arguments:i,repeatable:a,locations:o})}parseDirectiveLocations(){return this.delimitedMany(F.TokenKind.PIPE,this.parseDirectiveLocation)}parseDirectiveLocation(){let t=this._lexer.token,n=this.parseName();if(Object.prototype.hasOwnProperty.call(mv.DirectiveLocation,n.value))return n;throw this.unexpected(t)}node(t,n){return this._options.noLocation!==!0&&(n.loc=new kr.Location(t,this._lexer.lastToken,this._lexer.source)),n}peek(t){return this._lexer.token.kind===t}expectToken(t){let n=this._lexer.token;if(n.kind===t)return this.advanceLexer(),n;throw(0,Sn.syntaxError)(this._lexer.source,n.start,`Expected ${Ul(t)}, found ${Ji(n)}.`)}expectOptionalToken(t){return this._lexer.token.kind===t?(this.advanceLexer(),!0):!1}expectKeyword(t){let n=this._lexer.token;if(n.kind===F.TokenKind.NAME&&n.value===t)this.advanceLexer();else throw(0,Sn.syntaxError)(this._lexer.source,n.start,`Expected "${t}", found ${Ji(n)}.`)}expectOptionalKeyword(t){let n=this._lexer.token;return n.kind===F.TokenKind.NAME&&n.value===t?(this.advanceLexer(),!0):!1}unexpected(t){let n=t!=null?t:this._lexer.token;return(0,Sn.syntaxError)(this._lexer.source,n.start,`Unexpected ${Ji(n)}.`)}any(t,n,r){this.expectToken(t);let i=[];for(;!this.expectOptionalToken(r);)i.push(n.call(this));return i}optionalMany(t,n,r){if(this.expectOptionalToken(t)){let i=[];do i.push(n.call(this));while(!this.expectOptionalToken(r));return i}return[]}many(t,n,r){this.expectToken(t);let i=[];do i.push(n.call(this));while(!this.expectOptionalToken(r));return i}delimitedMany(t,n){this.expectOptionalToken(t);let r=[];do r.push(n.call(this));while(this.expectOptionalToken(t));return r}advanceLexer(){let{maxTokens:t}=this._options,n=this._lexer.advance();if(t!==void 0&&n.kind!==F.TokenKind.EOF&&(++this._tokenCounter,this._tokenCounter>t))throw(0,Sn.syntaxError)(this._lexer.source,n.start,`Document contains more that ${t} tokens. Parsing aborted.`)}};dn.Parser=er;function Ji(e){let t=e.value;return Ul(e.kind)+(t!=null?` "${t}"`:"")}function Ul(e){return(0,Ml.isPunctuatorTokenKind)(e)?`"${e}"`:e}});var pn=_(Ro=>{"use strict";Object.defineProperty(Ro,"__esModule",{value:!0});Ro.didYouMean=bv;var gv=5;function bv(e,t){let[n,r]=t?[e,t]:[void 0,e],i=" Did you mean ";n&&(i+=n+" ");let a=r.map(c=>`"${c}"`);switch(a.length){case 0:return"";case 1:return i+a[0]+"?";case 2:return i+a[0]+" or "+a[1]+"?"}let o=a.slice(0,gv),s=o.pop();return i+o.join(", ")+", or "+s+"?"}});var Ql=_(Ao=>{"use strict";Object.defineProperty(Ao,"__esModule",{value:!0});Ao.identityFunc=Ev;function Ev(e){return e}});var fn=_(Po=>{"use strict";Object.defineProperty(Po,"__esModule",{value:!0});Po.keyMap=_v;function _v(e,t){let n=Object.create(null);for(let r of e)n[t(r)]=r;return n}});var wr=_(jo=>{"use strict";Object.defineProperty(jo,"__esModule",{value:!0});jo.keyValMap=Nv;function Nv(e,t,n){let r=Object.create(null);for(let i of e)r[t(i)]=n(i);return r}});var ko=_(Fo=>{"use strict";Object.defineProperty(Fo,"__esModule",{value:!0});Fo.mapValue=Ov;function Ov(e,t){let n=Object.create(null);for(let r of Object.keys(e))n[r]=t(e[r],r);return n}});var Cr=_(Co=>{"use strict";Object.defineProperty(Co,"__esModule",{value:!0});Co.naturalCompare=Iv;function Iv(e,t){let n=0,r=0;for(;n<e.length&&r<t.length;){let i=e.charCodeAt(n),a=t.charCodeAt(r);if(Xi(i)&&Xi(a)){let o=0;do++n,o=o*10+i-wo,i=e.charCodeAt(n);while(Xi(i)&&o>0);let s=0;do++r,s=s*10+a-wo,a=t.charCodeAt(r);while(Xi(a)&&s>0);if(o<s)return-1;if(o>s)return 1}else{if(i<a)return-1;if(i>a)return 1;++n,++r}}return e.length-t.length}var wo=48,Dv=57;function Xi(e){return!isNaN(e)&&wo<=e&&e<=Dv}});var mn=_(qo=>{"use strict";Object.defineProperty(qo,"__esModule",{value:!0});qo.suggestionList=Lv;var Sv=Cr();function Lv(e,t){let n=Object.create(null),r=new Kl(e),i=Math.floor(e.length*.4)+1;for(let a of t){let o=r.measure(a,i);o!==void 0&&(n[a]=o)}return Object.keys(n).sort((a,o)=>{let s=n[a]-n[o];return s!==0?s:(0,Sv.naturalCompare)(a,o)})}var Kl=class{constructor(t){this._input=t,this._inputLowerCase=t.toLowerCase(),this._inputArray=xl(this._inputLowerCase),this._rows=[new Array(t.length+1).fill(0),new Array(t.length+1).fill(0),new Array(t.length+1).fill(0)]}measure(t,n){if(this._input===t)return 0;let r=t.toLowerCase();if(this._inputLowerCase===r)return 1;let i=xl(r),a=this._inputArray;if(i.length<a.length){let f=i;i=a,a=f}let o=i.length,s=a.length;if(o-s>n)return;let c=this._rows;for(let f=0;f<=s;f++)c[0][f]=f;for(let f=1;f<=o;f++){let m=c[(f-1)%3],g=c[f%3],T=g[0]=f;for(let O=1;O<=s;O++){let U=i[f-1]===a[O-1]?0:1,k=Math.min(m[O]+1,g[O-1]+1,m[O-1]+U);if(f>1&&O>1&&i[f-1]===a[O-2]&&i[f-2]===a[O-1]){let Y=c[(f-2)%3][O-2];k=Math.min(k,Y+1)}k<T&&(T=k),g[O]=k}if(T>n)return}let d=c[o%3][s];return d<=n?d:void 0}};function xl(e){let t=e.length,n=new Array(t);for(let r=0;r<t;++r)n[r]=e.charCodeAt(r);return n}});var Wi=_(Vo=>{"use strict";Object.defineProperty(Vo,"__esModule",{value:!0});Vo.toObjMap=Rv;function Rv(e){if(e==null)return Object.create(null);if(Object.getPrototypeOf(e)===null)return e;let t=Object.create(null);for(let[n,r]of Object.entries(e))t[n]=r;return t}});var $l=_(Mo=>{"use strict";Object.defineProperty(Mo,"__esModule",{value:!0});Mo.printString=Av;function Av(e){return`"${e.replace(Pv,jv)}"`}var Pv=/[\x00-\x1f\x22\x5c\x7f-\x9f]/g;function jv(e){return Fv[e.charCodeAt(0)]}var Fv=["\\u0000","\\u0001","\\u0002","\\u0003","\\u0004","\\u0005","\\u0006","\\u0007","\\b","\\t","\\n","\\u000B","\\f","\\r","\\u000E","\\u000F","\\u0010","\\u0011","\\u0012","\\u0013","\\u0014","\\u0015","\\u0016","\\u0017","\\u0018","\\u0019","\\u001A","\\u001B","\\u001C","\\u001D","\\u001E","\\u001F","","",'\\"',"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\\\","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\u007F","\\u0080","\\u0081","\\u0082","\\u0083","\\u0084","\\u0085","\\u0086","\\u0087","\\u0088","\\u0089","\\u008A","\\u008B","\\u008C","\\u008D","\\u008E","\\u008F","\\u0090","\\u0091","\\u0092","\\u0093","\\u0094","\\u0095","\\u0096","\\u0097","\\u0098","\\u0099","\\u009A","\\u009B","\\u009C","\\u009D","\\u009E","\\u009F"]});var Ln=_(yn=>{"use strict";Object.defineProperty(yn,"__esModule",{value:!0});yn.BREAK=void 0;yn.getEnterLeaveForKind=Hi;yn.getVisitFn=Vv;yn.visit=Cv;yn.visitInParallel=qv;var kv=et(),wv=pe(),Go=Vt(),Bl=ie(),nr=Object.freeze({});yn.BREAK=nr;function Cv(e,t,n=Go.QueryDocumentKeys){let r=new Map;for(let Y of Object.values(Bl.Kind))r.set(Y,Hi(t,Y));let i,a=Array.isArray(e),o=[e],s=-1,c=[],d=e,f,m,g=[],T=[];do{s++;let Y=s===o.length,se=Y&&c.length!==0;if(Y){if(f=T.length===0?void 0:g[g.length-1],d=m,m=T.pop(),se)if(a){d=d.slice();let Oe=0;for(let[ke,we]of c){let Te=ke-Oe;we===null?(d.splice(Te,1),Oe++):d[Te]=we}}else{d=Object.defineProperties({},Object.getOwnPropertyDescriptors(d));for(let[Oe,ke]of c)d[Oe]=ke}s=i.index,o=i.keys,c=i.edits,a=i.inArray,i=i.prev}else if(m){if(f=a?s:o[s],d=m[f],d==null)continue;g.push(f)}let z;if(!Array.isArray(d)){var O,U;(0,Go.isNode)(d)||(0,kv.devAssert)(!1,`Invalid AST Node: ${(0,wv.inspect)(d)}.`);let Oe=Y?(O=r.get(d.kind))===null||O===void 0?void 0:O.leave:(U=r.get(d.kind))===null||U===void 0?void 0:U.enter;if(z=Oe==null?void 0:Oe.call(t,d,f,m,g,T),z===nr)break;if(z===!1){if(!Y){g.pop();continue}}else if(z!==void 0&&(c.push([f,z]),!Y))if((0,Go.isNode)(z))d=z;else{g.pop();continue}}if(z===void 0&&se&&c.push([f,d]),Y)g.pop();else{var k;i={inArray:a,index:s,keys:o,edits:c,prev:i},a=Array.isArray(d),o=a?d:(k=n[d.kind])!==null&&k!==void 0?k:[],s=-1,c=[],m&&T.push(m),m=d}}while(i!==void 0);return c.length!==0?c[c.length-1][1]:e}function qv(e){let t=new Array(e.length).fill(null),n=Object.create(null);for(let r of Object.values(Bl.Kind)){let i=!1,a=new Array(e.length).fill(void 0),o=new Array(e.length).fill(void 0);for(let c=0;c<e.length;++c){let{enter:d,leave:f}=Hi(e[c],r);i||(i=d!=null||f!=null),a[c]=d,o[c]=f}if(!i)continue;let s={enter(...c){let d=c[0];for(let m=0;m<e.length;m++)if(t[m]===null){var f;let g=(f=a[m])===null||f===void 0?void 0:f.apply(e[m],c);if(g===!1)t[m]=d;else if(g===nr)t[m]=nr;else if(g!==void 0)return g}},leave(...c){let d=c[0];for(let m=0;m<e.length;m++)if(t[m]===null){var f;let g=(f=o[m])===null||f===void 0?void 0:f.apply(e[m],c);if(g===nr)t[m]=nr;else if(g!==void 0&&g!==!1)return g}else t[m]===d&&(t[m]=null)}};n[r]=s}return n}function Hi(e,t){let n=e[t];return typeof n=="object"?n:typeof n=="function"?{enter:n,leave:void 0}:{enter:e.enter,leave:e.leave}}function Vv(e,t,n){let{enter:r,leave:i}=Hi(e,t);return n?i:r}});var ct=_(Uo=>{"use strict";Object.defineProperty(Uo,"__esModule",{value:!0});Uo.print=Qv;var Mv=Sr(),Gv=$l(),Uv=Ln();function Qv(e){return(0,Uv.visit)(e,xv)}var Kv=80,xv={Name:{leave:e=>e.value},Variable:{leave:e=>"$"+e.name},Document:{leave:e=>q(e.definitions,`

`)},OperationDefinition:{leave(e){let t=Z("(",q(e.variableDefinitions,", "),")"),n=q([e.operation,q([e.name,t]),q(e.directives," ")]," ");return(n==="query"?"":n+" ")+e.selectionSet}},VariableDefinition:{leave:({variable:e,type:t,defaultValue:n,directives:r})=>e+": "+t+Z(" = ",n)+Z(" ",q(r," "))},SelectionSet:{leave:({selections:e})=>At(e)},Field:{leave({alias:e,name:t,arguments:n,directives:r,selectionSet:i}){let a=Z("",e,": ")+t,o=a+Z("(",q(n,", "),")");return o.length>Kv&&(o=a+Z(`(
`,zi(q(n,`
`)),`
)`)),q([o,q(r," "),i]," ")}},Argument:{leave:({name:e,value:t})=>e+": "+t},FragmentSpread:{leave:({name:e,directives:t})=>"..."+e+Z(" ",q(t," "))},InlineFragment:{leave:({typeCondition:e,directives:t,selectionSet:n})=>q(["...",Z("on ",e),q(t," "),n]," ")},FragmentDefinition:{leave:({name:e,typeCondition:t,variableDefinitions:n,directives:r,selectionSet:i})=>`fragment ${e}${Z("(",q(n,", "),")")} on ${t} ${Z("",q(r," ")," ")}`+i},IntValue:{leave:({value:e})=>e},FloatValue:{leave:({value:e})=>e},StringValue:{leave:({value:e,block:t})=>t?(0,Mv.printBlockString)(e):(0,Gv.printString)(e)},BooleanValue:{leave:({value:e})=>e?"true":"false"},NullValue:{leave:()=>"null"},EnumValue:{leave:({value:e})=>e},ListValue:{leave:({values:e})=>"["+q(e,", ")+"]"},ObjectValue:{leave:({fields:e})=>"{"+q(e,", ")+"}"},ObjectField:{leave:({name:e,value:t})=>e+": "+t},Directive:{leave:({name:e,arguments:t})=>"@"+e+Z("(",q(t,", "),")")},NamedType:{leave:({name:e})=>e},ListType:{leave:({type:e})=>"["+e+"]"},NonNullType:{leave:({type:e})=>e+"!"},SchemaDefinition:{leave:({description:e,directives:t,operationTypes:n})=>Z("",e,`
`)+q(["schema",q(t," "),At(n)]," ")},OperationTypeDefinition:{leave:({operation:e,type:t})=>e+": "+t},ScalarTypeDefinition:{leave:({description:e,name:t,directives:n})=>Z("",e,`
`)+q(["scalar",t,q(n," ")]," ")},ObjectTypeDefinition:{leave:({description:e,name:t,interfaces:n,directives:r,fields:i})=>Z("",e,`
`)+q(["type",t,Z("implements ",q(n," & ")),q(r," "),At(i)]," ")},FieldDefinition:{leave:({description:e,name:t,arguments:n,type:r,directives:i})=>Z("",e,`
`)+t+(Yl(n)?Z(`(
`,zi(q(n,`
`)),`
)`):Z("(",q(n,", "),")"))+": "+r+Z(" ",q(i," "))},InputValueDefinition:{leave:({description:e,name:t,type:n,defaultValue:r,directives:i})=>Z("",e,`
`)+q([t+": "+n,Z("= ",r),q(i," ")]," ")},InterfaceTypeDefinition:{leave:({description:e,name:t,interfaces:n,directives:r,fields:i})=>Z("",e,`
`)+q(["interface",t,Z("implements ",q(n," & ")),q(r," "),At(i)]," ")},UnionTypeDefinition:{leave:({description:e,name:t,directives:n,types:r})=>Z("",e,`
`)+q(["union",t,q(n," "),Z("= ",q(r," | "))]," ")},EnumTypeDefinition:{leave:({description:e,name:t,directives:n,values:r})=>Z("",e,`
`)+q(["enum",t,q(n," "),At(r)]," ")},EnumValueDefinition:{leave:({description:e,name:t,directives:n})=>Z("",e,`
`)+q([t,q(n," ")]," ")},InputObjectTypeDefinition:{leave:({description:e,name:t,directives:n,fields:r})=>Z("",e,`
`)+q(["input",t,q(n," "),At(r)]," ")},DirectiveDefinition:{leave:({description:e,name:t,arguments:n,repeatable:r,locations:i})=>Z("",e,`
`)+"directive @"+t+(Yl(n)?Z(`(
`,zi(q(n,`
`)),`
)`):Z("(",q(n,", "),")"))+(r?" repeatable":"")+" on "+q(i," | ")},SchemaExtension:{leave:({directives:e,operationTypes:t})=>q(["extend schema",q(e," "),At(t)]," ")},ScalarTypeExtension:{leave:({name:e,directives:t})=>q(["extend scalar",e,q(t," ")]," ")},ObjectTypeExtension:{leave:({name:e,interfaces:t,directives:n,fields:r})=>q(["extend type",e,Z("implements ",q(t," & ")),q(n," "),At(r)]," ")},InterfaceTypeExtension:{leave:({name:e,interfaces:t,directives:n,fields:r})=>q(["extend interface",e,Z("implements ",q(t," & ")),q(n," "),At(r)]," ")},UnionTypeExtension:{leave:({name:e,directives:t,types:n})=>q(["extend union",e,q(t," "),Z("= ",q(n," | "))]," ")},EnumTypeExtension:{leave:({name:e,directives:t,values:n})=>q(["extend enum",e,q(t," "),At(n)]," ")},InputObjectTypeExtension:{leave:({name:e,directives:t,fields:n})=>q(["extend input",e,q(t," "),At(n)]," ")}};function q(e,t=""){var n;return(n=e==null?void 0:e.filter(r=>r).join(t))!==null&&n!==void 0?n:""}function At(e){return Z(`{
`,zi(q(e,`
`)),`
}`)}function Z(e,t,n=""){return t!=null&&t!==""?e+t+n:""}function zi(e){return Z("  ",e.replace(/\n/g,`
  `))}function Yl(e){var t;return(t=e==null?void 0:e.some(n=>n.includes(`
`)))!==null&&t!==void 0?t:!1}});var xo=_(Ko=>{"use strict";Object.defineProperty(Ko,"__esModule",{value:!0});Ko.valueFromASTUntyped=Qo;var $v=wr(),Zt=ie();function Qo(e,t){switch(e.kind){case Zt.Kind.NULL:return null;case Zt.Kind.INT:return parseInt(e.value,10);case Zt.Kind.FLOAT:return parseFloat(e.value);case Zt.Kind.STRING:case Zt.Kind.ENUM:case Zt.Kind.BOOLEAN:return e.value;case Zt.Kind.LIST:return e.values.map(n=>Qo(n,t));case Zt.Kind.OBJECT:return(0,$v.keyValMap)(e.fields,n=>n.name.value,n=>Qo(n.value,t));case Zt.Kind.VARIABLE:return t==null?void 0:t[e.name.value]}}});var qr=_(ea=>{"use strict";Object.defineProperty(ea,"__esModule",{value:!0});ea.assertEnumValueName=Bv;ea.assertName=Wl;var Jl=et(),Zi=Q(),Xl=Qi();function Wl(e){if(e!=null||(0,Jl.devAssert)(!1,"Must provide name."),typeof e=="string"||(0,Jl.devAssert)(!1,"Expected name to be a string."),e.length===0)throw new Zi.GraphQLError("Expected name to be a non-empty string.");for(let t=1;t<e.length;++t)if(!(0,Xl.isNameContinue)(e.charCodeAt(t)))throw new Zi.GraphQLError(`Names must only contain [_a-zA-Z0-9] but "${e}" does not.`);if(!(0,Xl.isNameStart)(e.charCodeAt(0)))throw new Zi.GraphQLError(`Names must start with [_a-zA-Z] but "${e}" does not.`);return e}function Bv(e){if(e==="true"||e==="false"||e==="null")throw new Zi.GraphQLError(`Enum values cannot be named: ${e}`);return Wl(e)}});var te=_(G=>{"use strict";Object.defineProperty(G,"__esModule",{value:!0});G.GraphQLUnionType=G.GraphQLScalarType=G.GraphQLObjectType=G.GraphQLNonNull=G.GraphQLList=G.GraphQLInterfaceType=G.GraphQLInputObjectType=G.GraphQLEnumType=void 0;G.argsToArgsConfig=ud;G.assertAbstractType=pT;G.assertCompositeType=dT;G.assertEnumType=iT;G.assertInputObjectType=aT;G.assertInputType=uT;G.assertInterfaceType=nT;G.assertLeafType=lT;G.assertListType=oT;G.assertNamedType=hT;G.assertNonNullType=sT;G.assertNullableType=mT;G.assertObjectType=tT;G.assertOutputType=cT;G.assertScalarType=eT;G.assertType=Zv;G.assertUnionType=rT;G.assertWrappingType=fT;G.defineArguments=od;G.getNamedType=vT;G.getNullableType=yT;G.isAbstractType=nd;G.isCompositeType=td;G.isEnumType=jn;G.isInputObjectType=Mr;G.isInputType=$o;G.isInterfaceType=An;G.isLeafType=ed;G.isListType=ra;G.isNamedType=rd;G.isNonNullType=vn;G.isNullableType=Xo;G.isObjectType=rr;G.isOutputType=Bo;G.isRequiredArgument=TT;G.isRequiredInputField=_T;G.isScalarType=Rn;G.isType=na;G.isUnionType=Pn;G.isWrappingType=Gr;G.resolveObjMapThunk=Ho;G.resolveReadonlyArrayThunk=Wo;var Be=et(),Yv=pn(),Hl=Ql(),he=pe(),hn=jr(),Jv=qt(),Xv=fn(),zl=wr(),ta=ko(),Wv=mn(),Mt=Wi(),Vr=Q(),Hv=ie(),Zl=ct(),zv=xo(),Gt=qr();function na(e){return Rn(e)||rr(e)||An(e)||Pn(e)||jn(e)||Mr(e)||ra(e)||vn(e)}function Zv(e){if(!na(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL type.`);return e}function Rn(e){return(0,hn.instanceOf)(e,zo)}function eT(e){if(!Rn(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL Scalar type.`);return e}function rr(e){return(0,hn.instanceOf)(e,Zo)}function tT(e){if(!rr(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL Object type.`);return e}function An(e){return(0,hn.instanceOf)(e,es)}function nT(e){if(!An(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL Interface type.`);return e}function Pn(e){return(0,hn.instanceOf)(e,ts)}function rT(e){if(!Pn(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL Union type.`);return e}function jn(e){return(0,hn.instanceOf)(e,ns)}function iT(e){if(!jn(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL Enum type.`);return e}function Mr(e){return(0,hn.instanceOf)(e,rs)}function aT(e){if(!Mr(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL Input Object type.`);return e}function ra(e){return(0,hn.instanceOf)(e,Yo)}function oT(e){if(!ra(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL List type.`);return e}function vn(e){return(0,hn.instanceOf)(e,Jo)}function sT(e){if(!vn(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL Non-Null type.`);return e}function $o(e){return Rn(e)||jn(e)||Mr(e)||Gr(e)&&$o(e.ofType)}function uT(e){if(!$o(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL input type.`);return e}function Bo(e){return Rn(e)||rr(e)||An(e)||Pn(e)||jn(e)||Gr(e)&&Bo(e.ofType)}function cT(e){if(!Bo(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL output type.`);return e}function ed(e){return Rn(e)||jn(e)}function lT(e){if(!ed(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL leaf type.`);return e}function td(e){return rr(e)||An(e)||Pn(e)}function dT(e){if(!td(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL composite type.`);return e}function nd(e){return An(e)||Pn(e)}function pT(e){if(!nd(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL abstract type.`);return e}var Yo=class{constructor(t){na(t)||(0,Be.devAssert)(!1,`Expected ${(0,he.inspect)(t)} to be a GraphQL type.`),this.ofType=t}get[Symbol.toStringTag](){return"GraphQLList"}toString(){return"["+String(this.ofType)+"]"}toJSON(){return this.toString()}};G.GraphQLList=Yo;var Jo=class{constructor(t){Xo(t)||(0,Be.devAssert)(!1,`Expected ${(0,he.inspect)(t)} to be a GraphQL nullable type.`),this.ofType=t}get[Symbol.toStringTag](){return"GraphQLNonNull"}toString(){return String(this.ofType)+"!"}toJSON(){return this.toString()}};G.GraphQLNonNull=Jo;function Gr(e){return ra(e)||vn(e)}function fT(e){if(!Gr(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL wrapping type.`);return e}function Xo(e){return na(e)&&!vn(e)}function mT(e){if(!Xo(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL nullable type.`);return e}function yT(e){if(e)return vn(e)?e.ofType:e}function rd(e){return Rn(e)||rr(e)||An(e)||Pn(e)||jn(e)||Mr(e)}function hT(e){if(!rd(e))throw new Error(`Expected ${(0,he.inspect)(e)} to be a GraphQL named type.`);return e}function vT(e){if(e){let t=e;for(;Gr(t);)t=t.ofType;return t}}function Wo(e){return typeof e=="function"?e():e}function Ho(e){return typeof e=="function"?e():e}var zo=class{constructor(t){var n,r,i,a;let o=(n=t.parseValue)!==null&&n!==void 0?n:Hl.identityFunc;this.name=(0,Gt.assertName)(t.name),this.description=t.description,this.specifiedByURL=t.specifiedByURL,this.serialize=(r=t.serialize)!==null&&r!==void 0?r:Hl.identityFunc,this.parseValue=o,this.parseLiteral=(i=t.parseLiteral)!==null&&i!==void 0?i:(s,c)=>o((0,zv.valueFromASTUntyped)(s,c)),this.extensions=(0,Mt.toObjMap)(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=(a=t.extensionASTNodes)!==null&&a!==void 0?a:[],t.specifiedByURL==null||typeof t.specifiedByURL=="string"||(0,Be.devAssert)(!1,`${this.name} must provide "specifiedByURL" as a string, but got: ${(0,he.inspect)(t.specifiedByURL)}.`),t.serialize==null||typeof t.serialize=="function"||(0,Be.devAssert)(!1,`${this.name} must provide "serialize" function. If this custom Scalar is also used as an input type, ensure "parseValue" and "parseLiteral" functions are also provided.`),t.parseLiteral&&(typeof t.parseValue=="function"&&typeof t.parseLiteral=="function"||(0,Be.devAssert)(!1,`${this.name} must provide both "parseValue" and "parseLiteral" functions.`))}get[Symbol.toStringTag](){return"GraphQLScalarType"}toConfig(){return{name:this.name,description:this.description,specifiedByURL:this.specifiedByURL,serialize:this.serialize,parseValue:this.parseValue,parseLiteral:this.parseLiteral,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}};G.GraphQLScalarType=zo;var Zo=class{constructor(t){var n;this.name=(0,Gt.assertName)(t.name),this.description=t.description,this.isTypeOf=t.isTypeOf,this.extensions=(0,Mt.toObjMap)(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=(n=t.extensionASTNodes)!==null&&n!==void 0?n:[],this._fields=()=>ad(t),this._interfaces=()=>id(t),t.isTypeOf==null||typeof t.isTypeOf=="function"||(0,Be.devAssert)(!1,`${this.name} must provide "isTypeOf" as a function, but got: ${(0,he.inspect)(t.isTypeOf)}.`)}get[Symbol.toStringTag](){return"GraphQLObjectType"}getFields(){return typeof this._fields=="function"&&(this._fields=this._fields()),this._fields}getInterfaces(){return typeof this._interfaces=="function"&&(this._interfaces=this._interfaces()),this._interfaces}toConfig(){return{name:this.name,description:this.description,interfaces:this.getInterfaces(),fields:sd(this.getFields()),isTypeOf:this.isTypeOf,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}};G.GraphQLObjectType=Zo;function id(e){var t;let n=Wo((t=e.interfaces)!==null&&t!==void 0?t:[]);return Array.isArray(n)||(0,Be.devAssert)(!1,`${e.name} interfaces must be an Array or a function which returns an Array.`),n}function ad(e){let t=Ho(e.fields);return ir(t)||(0,Be.devAssert)(!1,`${e.name} fields must be an object with field names as keys or a function which returns such an object.`),(0,ta.mapValue)(t,(n,r)=>{var i;ir(n)||(0,Be.devAssert)(!1,`${e.name}.${r} field config must be an object.`),n.resolve==null||typeof n.resolve=="function"||(0,Be.devAssert)(!1,`${e.name}.${r} field resolver must be a function if provided, but got: ${(0,he.inspect)(n.resolve)}.`);let a=(i=n.args)!==null&&i!==void 0?i:{};return ir(a)||(0,Be.devAssert)(!1,`${e.name}.${r} args must be an object with argument names as keys.`),{name:(0,Gt.assertName)(r),description:n.description,type:n.type,args:od(a),resolve:n.resolve,subscribe:n.subscribe,deprecationReason:n.deprecationReason,extensions:(0,Mt.toObjMap)(n.extensions),astNode:n.astNode}})}function od(e){return Object.entries(e).map(([t,n])=>({name:(0,Gt.assertName)(t),description:n.description,type:n.type,defaultValue:n.defaultValue,deprecationReason:n.deprecationReason,extensions:(0,Mt.toObjMap)(n.extensions),astNode:n.astNode}))}function ir(e){return(0,Jv.isObjectLike)(e)&&!Array.isArray(e)}function sd(e){return(0,ta.mapValue)(e,t=>({description:t.description,type:t.type,args:ud(t.args),resolve:t.resolve,subscribe:t.subscribe,deprecationReason:t.deprecationReason,extensions:t.extensions,astNode:t.astNode}))}function ud(e){return(0,zl.keyValMap)(e,t=>t.name,t=>({description:t.description,type:t.type,defaultValue:t.defaultValue,deprecationReason:t.deprecationReason,extensions:t.extensions,astNode:t.astNode}))}function TT(e){return vn(e.type)&&e.defaultValue===void 0}var es=class{constructor(t){var n;this.name=(0,Gt.assertName)(t.name),this.description=t.description,this.resolveType=t.resolveType,this.extensions=(0,Mt.toObjMap)(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=(n=t.extensionASTNodes)!==null&&n!==void 0?n:[],this._fields=ad.bind(void 0,t),this._interfaces=id.bind(void 0,t),t.resolveType==null||typeof t.resolveType=="function"||(0,Be.devAssert)(!1,`${this.name} must provide "resolveType" as a function, but got: ${(0,he.inspect)(t.resolveType)}.`)}get[Symbol.toStringTag](){return"GraphQLInterfaceType"}getFields(){return typeof this._fields=="function"&&(this._fields=this._fields()),this._fields}getInterfaces(){return typeof this._interfaces=="function"&&(this._interfaces=this._interfaces()),this._interfaces}toConfig(){return{name:this.name,description:this.description,interfaces:this.getInterfaces(),fields:sd(this.getFields()),resolveType:this.resolveType,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}};G.GraphQLInterfaceType=es;var ts=class{constructor(t){var n;this.name=(0,Gt.assertName)(t.name),this.description=t.description,this.resolveType=t.resolveType,this.extensions=(0,Mt.toObjMap)(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=(n=t.extensionASTNodes)!==null&&n!==void 0?n:[],this._types=gT.bind(void 0,t),t.resolveType==null||typeof t.resolveType=="function"||(0,Be.devAssert)(!1,`${this.name} must provide "resolveType" as a function, but got: ${(0,he.inspect)(t.resolveType)}.`)}get[Symbol.toStringTag](){return"GraphQLUnionType"}getTypes(){return typeof this._types=="function"&&(this._types=this._types()),this._types}toConfig(){return{name:this.name,description:this.description,types:this.getTypes(),resolveType:this.resolveType,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}};G.GraphQLUnionType=ts;function gT(e){let t=Wo(e.types);return Array.isArray(t)||(0,Be.devAssert)(!1,`Must provide Array of types or a function which returns such an array for Union ${e.name}.`),t}var ns=class{constructor(t){var n;this.name=(0,Gt.assertName)(t.name),this.description=t.description,this.extensions=(0,Mt.toObjMap)(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=(n=t.extensionASTNodes)!==null&&n!==void 0?n:[],this._values=bT(this.name,t.values),this._valueLookup=new Map(this._values.map(r=>[r.value,r])),this._nameLookup=(0,Xv.keyMap)(this._values,r=>r.name)}get[Symbol.toStringTag](){return"GraphQLEnumType"}getValues(){return this._values}getValue(t){return this._nameLookup[t]}serialize(t){let n=this._valueLookup.get(t);if(n===void 0)throw new Vr.GraphQLError(`Enum "${this.name}" cannot represent value: ${(0,he.inspect)(t)}`);return n.name}parseValue(t){if(typeof t!="string"){let r=(0,he.inspect)(t);throw new Vr.GraphQLError(`Enum "${this.name}" cannot represent non-string value: ${r}.`+ia(this,r))}let n=this.getValue(t);if(n==null)throw new Vr.GraphQLError(`Value "${t}" does not exist in "${this.name}" enum.`+ia(this,t));return n.value}parseLiteral(t,n){if(t.kind!==Hv.Kind.ENUM){let i=(0,Zl.print)(t);throw new Vr.GraphQLError(`Enum "${this.name}" cannot represent non-enum value: ${i}.`+ia(this,i),{nodes:t})}let r=this.getValue(t.value);if(r==null){let i=(0,Zl.print)(t);throw new Vr.GraphQLError(`Value "${i}" does not exist in "${this.name}" enum.`+ia(this,i),{nodes:t})}return r.value}toConfig(){let t=(0,zl.keyValMap)(this.getValues(),n=>n.name,n=>({description:n.description,value:n.value,deprecationReason:n.deprecationReason,extensions:n.extensions,astNode:n.astNode}));return{name:this.name,description:this.description,values:t,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}};G.GraphQLEnumType=ns;function ia(e,t){let n=e.getValues().map(i=>i.name),r=(0,Wv.suggestionList)(t,n);return(0,Yv.didYouMean)("the enum value",r)}function bT(e,t){return ir(t)||(0,Be.devAssert)(!1,`${e} values must be an object with value names as keys.`),Object.entries(t).map(([n,r])=>(ir(r)||(0,Be.devAssert)(!1,`${e}.${n} must refer to an object with a "value" key representing an internal value but got: ${(0,he.inspect)(r)}.`),{name:(0,Gt.assertEnumValueName)(n),description:r.description,value:r.value!==void 0?r.value:n,deprecationReason:r.deprecationReason,extensions:(0,Mt.toObjMap)(r.extensions),astNode:r.astNode}))}var rs=class{constructor(t){var n;this.name=(0,Gt.assertName)(t.name),this.description=t.description,this.extensions=(0,Mt.toObjMap)(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=(n=t.extensionASTNodes)!==null&&n!==void 0?n:[],this._fields=ET.bind(void 0,t)}get[Symbol.toStringTag](){return"GraphQLInputObjectType"}getFields(){return typeof this._fields=="function"&&(this._fields=this._fields()),this._fields}toConfig(){let t=(0,ta.mapValue)(this.getFields(),n=>({description:n.description,type:n.type,defaultValue:n.defaultValue,deprecationReason:n.deprecationReason,extensions:n.extensions,astNode:n.astNode}));return{name:this.name,description:this.description,fields:t,extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes}}toString(){return this.name}toJSON(){return this.toString()}};G.GraphQLInputObjectType=rs;function ET(e){let t=Ho(e.fields);return ir(t)||(0,Be.devAssert)(!1,`${e.name} fields must be an object with field names as keys or a function which returns such an object.`),(0,ta.mapValue)(t,(n,r)=>(!("resolve"in n)||(0,Be.devAssert)(!1,`${e.name}.${r} field has a resolve property, but Input Types cannot define resolvers.`),{name:(0,Gt.assertName)(r),description:n.description,type:n.type,defaultValue:n.defaultValue,deprecationReason:n.deprecationReason,extensions:(0,Mt.toObjMap)(n.extensions),astNode:n.astNode}))}function _T(e){return vn(e.type)&&e.defaultValue===void 0}});var Qr=_(Ur=>{"use strict";Object.defineProperty(Ur,"__esModule",{value:!0});Ur.doTypesOverlap=NT;Ur.isEqualType=is;Ur.isTypeSubTypeOf=aa;var ze=te();function is(e,t){return e===t?!0:(0,ze.isNonNullType)(e)&&(0,ze.isNonNullType)(t)||(0,ze.isListType)(e)&&(0,ze.isListType)(t)?is(e.ofType,t.ofType):!1}function aa(e,t,n){return t===n?!0:(0,ze.isNonNullType)(n)?(0,ze.isNonNullType)(t)?aa(e,t.ofType,n.ofType):!1:(0,ze.isNonNullType)(t)?aa(e,t.ofType,n):(0,ze.isListType)(n)?(0,ze.isListType)(t)?aa(e,t.ofType,n.ofType):!1:(0,ze.isListType)(t)?!1:(0,ze.isAbstractType)(n)&&((0,ze.isInterfaceType)(t)||(0,ze.isObjectType)(t))&&e.isSubType(n,t)}function NT(e,t,n){return t===n?!0:(0,ze.isAbstractType)(t)?(0,ze.isAbstractType)(n)?e.getPossibleTypes(t).some(r=>e.isSubType(n,r)):e.isSubType(t,n):(0,ze.isAbstractType)(n)?e.isSubType(n,t):!1}});var Ut=_(Me=>{"use strict";Object.defineProperty(Me,"__esModule",{value:!0});Me.GraphQLString=Me.GraphQLInt=Me.GraphQLID=Me.GraphQLFloat=Me.GraphQLBoolean=Me.GRAPHQL_MIN_INT=Me.GRAPHQL_MAX_INT=void 0;Me.isSpecifiedScalarType=OT;Me.specifiedScalarTypes=void 0;var Pt=pe(),cd=qt(),Ye=Q(),Fn=ie(),Kr=ct(),xr=te(),oa=2147483647;Me.GRAPHQL_MAX_INT=oa;var sa=-2147483648;Me.GRAPHQL_MIN_INT=sa;var ld=new xr.GraphQLScalarType({name:"Int",description:"The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.",serialize(e){let t=$r(e);if(typeof t=="boolean")return t?1:0;let n=t;if(typeof t=="string"&&t!==""&&(n=Number(t)),typeof n!="number"||!Number.isInteger(n))throw new Ye.GraphQLError(`Int cannot represent non-integer value: ${(0,Pt.inspect)(t)}`);if(n>oa||n<sa)throw new Ye.GraphQLError("Int cannot represent non 32-bit signed integer value: "+(0,Pt.inspect)(t));return n},parseValue(e){if(typeof e!="number"||!Number.isInteger(e))throw new Ye.GraphQLError(`Int cannot represent non-integer value: ${(0,Pt.inspect)(e)}`);if(e>oa||e<sa)throw new Ye.GraphQLError(`Int cannot represent non 32-bit signed integer value: ${e}`);return e},parseLiteral(e){if(e.kind!==Fn.Kind.INT)throw new Ye.GraphQLError(`Int cannot represent non-integer value: ${(0,Kr.print)(e)}`,{nodes:e});let t=parseInt(e.value,10);if(t>oa||t<sa)throw new Ye.GraphQLError(`Int cannot represent non 32-bit signed integer value: ${e.value}`,{nodes:e});return t}});Me.GraphQLInt=ld;var dd=new xr.GraphQLScalarType({name:"Float",description:"The `Float` scalar type represents signed double-precision fractional values as specified by [IEEE 754](https://en.wikipedia.org/wiki/IEEE_floating_point).",serialize(e){let t=$r(e);if(typeof t=="boolean")return t?1:0;let n=t;if(typeof t=="string"&&t!==""&&(n=Number(t)),typeof n!="number"||!Number.isFinite(n))throw new Ye.GraphQLError(`Float cannot represent non numeric value: ${(0,Pt.inspect)(t)}`);return n},parseValue(e){if(typeof e!="number"||!Number.isFinite(e))throw new Ye.GraphQLError(`Float cannot represent non numeric value: ${(0,Pt.inspect)(e)}`);return e},parseLiteral(e){if(e.kind!==Fn.Kind.FLOAT&&e.kind!==Fn.Kind.INT)throw new Ye.GraphQLError(`Float cannot represent non numeric value: ${(0,Kr.print)(e)}`,e);return parseFloat(e.value)}});Me.GraphQLFloat=dd;var pd=new xr.GraphQLScalarType({name:"String",description:"The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.",serialize(e){let t=$r(e);if(typeof t=="string")return t;if(typeof t=="boolean")return t?"true":"false";if(typeof t=="number"&&Number.isFinite(t))return t.toString();throw new Ye.GraphQLError(`String cannot represent value: ${(0,Pt.inspect)(e)}`)},parseValue(e){if(typeof e!="string")throw new Ye.GraphQLError(`String cannot represent a non string value: ${(0,Pt.inspect)(e)}`);return e},parseLiteral(e){if(e.kind!==Fn.Kind.STRING)throw new Ye.GraphQLError(`String cannot represent a non string value: ${(0,Kr.print)(e)}`,{nodes:e});return e.value}});Me.GraphQLString=pd;var fd=new xr.GraphQLScalarType({name:"Boolean",description:"The `Boolean` scalar type represents `true` or `false`.",serialize(e){let t=$r(e);if(typeof t=="boolean")return t;if(Number.isFinite(t))return t!==0;throw new Ye.GraphQLError(`Boolean cannot represent a non boolean value: ${(0,Pt.inspect)(t)}`)},parseValue(e){if(typeof e!="boolean")throw new Ye.GraphQLError(`Boolean cannot represent a non boolean value: ${(0,Pt.inspect)(e)}`);return e},parseLiteral(e){if(e.kind!==Fn.Kind.BOOLEAN)throw new Ye.GraphQLError(`Boolean cannot represent a non boolean value: ${(0,Kr.print)(e)}`,{nodes:e});return e.value}});Me.GraphQLBoolean=fd;var md=new xr.GraphQLScalarType({name:"ID",description:'The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `"4"`) or integer (such as `4`) input value will be accepted as an ID.',serialize(e){let t=$r(e);if(typeof t=="string")return t;if(Number.isInteger(t))return String(t);throw new Ye.GraphQLError(`ID cannot represent value: ${(0,Pt.inspect)(e)}`)},parseValue(e){if(typeof e=="string")return e;if(typeof e=="number"&&Number.isInteger(e))return e.toString();throw new Ye.GraphQLError(`ID cannot represent value: ${(0,Pt.inspect)(e)}`)},parseLiteral(e){if(e.kind!==Fn.Kind.STRING&&e.kind!==Fn.Kind.INT)throw new Ye.GraphQLError("ID cannot represent a non-string and non-integer value: "+(0,Kr.print)(e),{nodes:e});return e.value}});Me.GraphQLID=md;var yd=Object.freeze([pd,ld,dd,fd,md]);Me.specifiedScalarTypes=yd;function OT(e){return yd.some(({name:t})=>e.name===t)}function $r(e){if((0,cd.isObjectLike)(e)){if(typeof e.valueOf=="function"){let t=e.valueOf();if(!(0,cd.isObjectLike)(t))return t}if(typeof e.toJSON=="function")return e.toJSON()}return e}});var it=_(Ge=>{"use strict";Object.defineProperty(Ge,"__esModule",{value:!0});Ge.GraphQLSpecifiedByDirective=Ge.GraphQLSkipDirective=Ge.GraphQLIncludeDirective=Ge.GraphQLDirective=Ge.GraphQLDeprecatedDirective=Ge.DEFAULT_DEPRECATION_REASON=void 0;Ge.assertDirective=AT;Ge.isDirective=vd;Ge.isSpecifiedDirective=PT;Ge.specifiedDirectives=void 0;var hd=et(),IT=pe(),DT=jr(),ST=qt(),LT=Wi(),jt=zn(),RT=qr(),Br=te(),ua=Ut();function vd(e){return(0,DT.instanceOf)(e,kn)}function AT(e){if(!vd(e))throw new Error(`Expected ${(0,IT.inspect)(e)} to be a GraphQL directive.`);return e}var kn=class{constructor(t){var n,r;this.name=(0,RT.assertName)(t.name),this.description=t.description,this.locations=t.locations,this.isRepeatable=(n=t.isRepeatable)!==null&&n!==void 0?n:!1,this.extensions=(0,LT.toObjMap)(t.extensions),this.astNode=t.astNode,Array.isArray(t.locations)||(0,hd.devAssert)(!1,`@${t.name} locations must be an Array.`);let i=(r=t.args)!==null&&r!==void 0?r:{};(0,ST.isObjectLike)(i)&&!Array.isArray(i)||(0,hd.devAssert)(!1,`@${t.name} args must be an object with argument names as keys.`),this.args=(0,Br.defineArguments)(i)}get[Symbol.toStringTag](){return"GraphQLDirective"}toConfig(){return{name:this.name,description:this.description,locations:this.locations,args:(0,Br.argsToArgsConfig)(this.args),isRepeatable:this.isRepeatable,extensions:this.extensions,astNode:this.astNode}}toString(){return"@"+this.name}toJSON(){return this.toString()}};Ge.GraphQLDirective=kn;var Td=new kn({name:"include",description:"Directs the executor to include this field or fragment only when the `if` argument is true.",locations:[jt.DirectiveLocation.FIELD,jt.DirectiveLocation.FRAGMENT_SPREAD,jt.DirectiveLocation.INLINE_FRAGMENT],args:{if:{type:new Br.GraphQLNonNull(ua.GraphQLBoolean),description:"Included when true."}}});Ge.GraphQLIncludeDirective=Td;var gd=new kn({name:"skip",description:"Directs the executor to skip this field or fragment when the `if` argument is true.",locations:[jt.DirectiveLocation.FIELD,jt.DirectiveLocation.FRAGMENT_SPREAD,jt.DirectiveLocation.INLINE_FRAGMENT],args:{if:{type:new Br.GraphQLNonNull(ua.GraphQLBoolean),description:"Skipped when true."}}});Ge.GraphQLSkipDirective=gd;var bd="No longer supported";Ge.DEFAULT_DEPRECATION_REASON=bd;var Ed=new kn({name:"deprecated",description:"Marks an element of a GraphQL schema as no longer supported.",locations:[jt.DirectiveLocation.FIELD_DEFINITION,jt.DirectiveLocation.ARGUMENT_DEFINITION,jt.DirectiveLocation.INPUT_FIELD_DEFINITION,jt.DirectiveLocation.ENUM_VALUE],args:{reason:{type:ua.GraphQLString,description:"Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).",defaultValue:bd}}});Ge.GraphQLDeprecatedDirective=Ed;var _d=new kn({name:"specifiedBy",description:"Exposes a URL that specifies the behavior of this scalar.",locations:[jt.DirectiveLocation.SCALAR],args:{url:{type:new Br.GraphQLNonNull(ua.GraphQLString),description:"The URL that specifies the behavior of this scalar."}}});Ge.GraphQLSpecifiedByDirective=_d;var Nd=Object.freeze([Td,gd,Ed,_d]);Ge.specifiedDirectives=Nd;function PT(e){return Nd.some(({name:t})=>t===e.name)}});var ca=_(as=>{"use strict";Object.defineProperty(as,"__esModule",{value:!0});as.isIterableObject=jT;function jT(e){return typeof e=="object"&&typeof(e==null?void 0:e[Symbol.iterator])=="function"}});var Xr=_(os=>{"use strict";Object.defineProperty(os,"__esModule",{value:!0});os.astFromValue=Jr;var Od=pe(),FT=He(),kT=ca(),wT=qt(),yt=ie(),Yr=te(),CT=Ut();function Jr(e,t){if((0,Yr.isNonNullType)(t)){let n=Jr(e,t.ofType);return(n==null?void 0:n.kind)===yt.Kind.NULL?null:n}if(e===null)return{kind:yt.Kind.NULL};if(e===void 0)return null;if((0,Yr.isListType)(t)){let n=t.ofType;if((0,kT.isIterableObject)(e)){let r=[];for(let i of e){let a=Jr(i,n);a!=null&&r.push(a)}return{kind:yt.Kind.LIST,values:r}}return Jr(e,n)}if((0,Yr.isInputObjectType)(t)){if(!(0,wT.isObjectLike)(e))return null;let n=[];for(let r of Object.values(t.getFields())){let i=Jr(e[r.name],r.type);i&&n.push({kind:yt.Kind.OBJECT_FIELD,name:{kind:yt.Kind.NAME,value:r.name},value:i})}return{kind:yt.Kind.OBJECT,fields:n}}if((0,Yr.isLeafType)(t)){let n=t.serialize(e);if(n==null)return null;if(typeof n=="boolean")return{kind:yt.Kind.BOOLEAN,value:n};if(typeof n=="number"&&Number.isFinite(n)){let r=String(n);return Id.test(r)?{kind:yt.Kind.INT,value:r}:{kind:yt.Kind.FLOAT,value:r}}if(typeof n=="string")return(0,Yr.isEnumType)(t)?{kind:yt.Kind.ENUM,value:n}:t===CT.GraphQLID&&Id.test(n)?{kind:yt.Kind.INT,value:n}:{kind:yt.Kind.STRING,value:n};throw new TypeError(`Cannot convert value to AST: ${(0,Od.inspect)(n)}.`)}(0,FT.invariant)(!1,"Unexpected input type: "+(0,Od.inspect)(t))}var Id=/^-?(?:0|[1-9][0-9]*)$/});var lt=_(fe=>{"use strict";Object.defineProperty(fe,"__esModule",{value:!0});fe.introspectionTypes=fe.__TypeKind=fe.__Type=fe.__Schema=fe.__InputValue=fe.__Field=fe.__EnumValue=fe.__DirectiveLocation=fe.__Directive=fe.TypeNameMetaFieldDef=fe.TypeMetaFieldDef=fe.TypeKind=fe.SchemaMetaFieldDef=void 0;fe.isIntrospectionType=xT;var qT=pe(),VT=He(),Ue=zn(),MT=ct(),GT=Xr(),V=te(),ve=Ut(),ss=new V.GraphQLObjectType({name:"__Schema",description:"A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.",fields:()=>({description:{type:ve.GraphQLString,resolve:e=>e.description},types:{description:"A list of all types supported by this server.",type:new V.GraphQLNonNull(new V.GraphQLList(new V.GraphQLNonNull(ht))),resolve(e){return Object.values(e.getTypeMap())}},queryType:{description:"The type that query operations will be rooted at.",type:new V.GraphQLNonNull(ht),resolve:e=>e.getQueryType()},mutationType:{description:"If this server supports mutation, the type that mutation operations will be rooted at.",type:ht,resolve:e=>e.getMutationType()},subscriptionType:{description:"If this server support subscription, the type that subscription operations will be rooted at.",type:ht,resolve:e=>e.getSubscriptionType()},directives:{description:"A list of all directives supported by this server.",type:new V.GraphQLNonNull(new V.GraphQLList(new V.GraphQLNonNull(us))),resolve:e=>e.getDirectives()}})});fe.__Schema=ss;var us=new V.GraphQLObjectType({name:"__Directive",description:`A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.

In some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.`,fields:()=>({name:{type:new V.GraphQLNonNull(ve.GraphQLString),resolve:e=>e.name},description:{type:ve.GraphQLString,resolve:e=>e.description},isRepeatable:{type:new V.GraphQLNonNull(ve.GraphQLBoolean),resolve:e=>e.isRepeatable},locations:{type:new V.GraphQLNonNull(new V.GraphQLList(new V.GraphQLNonNull(cs))),resolve:e=>e.locations},args:{type:new V.GraphQLNonNull(new V.GraphQLList(new V.GraphQLNonNull(Wr))),args:{includeDeprecated:{type:ve.GraphQLBoolean,defaultValue:!1}},resolve(e,{includeDeprecated:t}){return t?e.args:e.args.filter(n=>n.deprecationReason==null)}}})});fe.__Directive=us;var cs=new V.GraphQLEnumType({name:"__DirectiveLocation",description:"A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.",values:{QUERY:{value:Ue.DirectiveLocation.QUERY,description:"Location adjacent to a query operation."},MUTATION:{value:Ue.DirectiveLocation.MUTATION,description:"Location adjacent to a mutation operation."},SUBSCRIPTION:{value:Ue.DirectiveLocation.SUBSCRIPTION,description:"Location adjacent to a subscription operation."},FIELD:{value:Ue.DirectiveLocation.FIELD,description:"Location adjacent to a field."},FRAGMENT_DEFINITION:{value:Ue.DirectiveLocation.FRAGMENT_DEFINITION,description:"Location adjacent to a fragment definition."},FRAGMENT_SPREAD:{value:Ue.DirectiveLocation.FRAGMENT_SPREAD,description:"Location adjacent to a fragment spread."},INLINE_FRAGMENT:{value:Ue.DirectiveLocation.INLINE_FRAGMENT,description:"Location adjacent to an inline fragment."},VARIABLE_DEFINITION:{value:Ue.DirectiveLocation.VARIABLE_DEFINITION,description:"Location adjacent to a variable definition."},SCHEMA:{value:Ue.DirectiveLocation.SCHEMA,description:"Location adjacent to a schema definition."},SCALAR:{value:Ue.DirectiveLocation.SCALAR,description:"Location adjacent to a scalar definition."},OBJECT:{value:Ue.DirectiveLocation.OBJECT,description:"Location adjacent to an object type definition."},FIELD_DEFINITION:{value:Ue.DirectiveLocation.FIELD_DEFINITION,description:"Location adjacent to a field definition."},ARGUMENT_DEFINITION:{value:Ue.DirectiveLocation.ARGUMENT_DEFINITION,description:"Location adjacent to an argument definition."},INTERFACE:{value:Ue.DirectiveLocation.INTERFACE,description:"Location adjacent to an interface definition."},UNION:{value:Ue.DirectiveLocation.UNION,description:"Location adjacent to a union definition."},ENUM:{value:Ue.DirectiveLocation.ENUM,description:"Location adjacent to an enum definition."},ENUM_VALUE:{value:Ue.DirectiveLocation.ENUM_VALUE,description:"Location adjacent to an enum value definition."},INPUT_OBJECT:{value:Ue.DirectiveLocation.INPUT_OBJECT,description:"Location adjacent to an input object type definition."},INPUT_FIELD_DEFINITION:{value:Ue.DirectiveLocation.INPUT_FIELD_DEFINITION,description:"Location adjacent to an input object field definition."}}});fe.__DirectiveLocation=cs;var ht=new V.GraphQLObjectType({name:"__Type",description:"The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.",fields:()=>({kind:{type:new V.GraphQLNonNull(ps),resolve(e){if((0,V.isScalarType)(e))return Qe.SCALAR;if((0,V.isObjectType)(e))return Qe.OBJECT;if((0,V.isInterfaceType)(e))return Qe.INTERFACE;if((0,V.isUnionType)(e))return Qe.UNION;if((0,V.isEnumType)(e))return Qe.ENUM;if((0,V.isInputObjectType)(e))return Qe.INPUT_OBJECT;if((0,V.isListType)(e))return Qe.LIST;if((0,V.isNonNullType)(e))return Qe.NON_NULL;(0,VT.invariant)(!1,`Unexpected type: "${(0,qT.inspect)(e)}".`)}},name:{type:ve.GraphQLString,resolve:e=>"name"in e?e.name:void 0},description:{type:ve.GraphQLString,resolve:e=>"description"in e?e.description:void 0},specifiedByURL:{type:ve.GraphQLString,resolve:e=>"specifiedByURL"in e?e.specifiedByURL:void 0},fields:{type:new V.GraphQLList(new V.GraphQLNonNull(ls)),args:{includeDeprecated:{type:ve.GraphQLBoolean,defaultValue:!1}},resolve(e,{includeDeprecated:t}){if((0,V.isObjectType)(e)||(0,V.isInterfaceType)(e)){let n=Object.values(e.getFields());return t?n:n.filter(r=>r.deprecationReason==null)}}},interfaces:{type:new V.GraphQLList(new V.GraphQLNonNull(ht)),resolve(e){if((0,V.isObjectType)(e)||(0,V.isInterfaceType)(e))return e.getInterfaces()}},possibleTypes:{type:new V.GraphQLList(new V.GraphQLNonNull(ht)),resolve(e,t,n,{schema:r}){if((0,V.isAbstractType)(e))return r.getPossibleTypes(e)}},enumValues:{type:new V.GraphQLList(new V.GraphQLNonNull(ds)),args:{includeDeprecated:{type:ve.GraphQLBoolean,defaultValue:!1}},resolve(e,{includeDeprecated:t}){if((0,V.isEnumType)(e)){let n=e.getValues();return t?n:n.filter(r=>r.deprecationReason==null)}}},inputFields:{type:new V.GraphQLList(new V.GraphQLNonNull(Wr)),args:{includeDeprecated:{type:ve.GraphQLBoolean,defaultValue:!1}},resolve(e,{includeDeprecated:t}){if((0,V.isInputObjectType)(e)){let n=Object.values(e.getFields());return t?n:n.filter(r=>r.deprecationReason==null)}}},ofType:{type:ht,resolve:e=>"ofType"in e?e.ofType:void 0}})});fe.__Type=ht;var ls=new V.GraphQLObjectType({name:"__Field",description:"Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.",fields:()=>({name:{type:new V.GraphQLNonNull(ve.GraphQLString),resolve:e=>e.name},description:{type:ve.GraphQLString,resolve:e=>e.description},args:{type:new V.GraphQLNonNull(new V.GraphQLList(new V.GraphQLNonNull(Wr))),args:{includeDeprecated:{type:ve.GraphQLBoolean,defaultValue:!1}},resolve(e,{includeDeprecated:t}){return t?e.args:e.args.filter(n=>n.deprecationReason==null)}},type:{type:new V.GraphQLNonNull(ht),resolve:e=>e.type},isDeprecated:{type:new V.GraphQLNonNull(ve.GraphQLBoolean),resolve:e=>e.deprecationReason!=null},deprecationReason:{type:ve.GraphQLString,resolve:e=>e.deprecationReason}})});fe.__Field=ls;var Wr=new V.GraphQLObjectType({name:"__InputValue",description:"Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.",fields:()=>({name:{type:new V.GraphQLNonNull(ve.GraphQLString),resolve:e=>e.name},description:{type:ve.GraphQLString,resolve:e=>e.description},type:{type:new V.GraphQLNonNull(ht),resolve:e=>e.type},defaultValue:{type:ve.GraphQLString,description:"A GraphQL-formatted string representing the default value for this input value.",resolve(e){let{type:t,defaultValue:n}=e,r=(0,GT.astFromValue)(n,t);return r?(0,MT.print)(r):null}},isDeprecated:{type:new V.GraphQLNonNull(ve.GraphQLBoolean),resolve:e=>e.deprecationReason!=null},deprecationReason:{type:ve.GraphQLString,resolve:e=>e.deprecationReason}})});fe.__InputValue=Wr;var ds=new V.GraphQLObjectType({name:"__EnumValue",description:"One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.",fields:()=>({name:{type:new V.GraphQLNonNull(ve.GraphQLString),resolve:e=>e.name},description:{type:ve.GraphQLString,resolve:e=>e.description},isDeprecated:{type:new V.GraphQLNonNull(ve.GraphQLBoolean),resolve:e=>e.deprecationReason!=null},deprecationReason:{type:ve.GraphQLString,resolve:e=>e.deprecationReason}})});fe.__EnumValue=ds;var Qe;fe.TypeKind=Qe;(function(e){e.SCALAR="SCALAR",e.OBJECT="OBJECT",e.INTERFACE="INTERFACE",e.UNION="UNION",e.ENUM="ENUM",e.INPUT_OBJECT="INPUT_OBJECT",e.LIST="LIST",e.NON_NULL="NON_NULL"})(Qe||(fe.TypeKind=Qe={}));var ps=new V.GraphQLEnumType({name:"__TypeKind",description:"An enum describing what kind of type a given `__Type` is.",values:{SCALAR:{value:Qe.SCALAR,description:"Indicates this type is a scalar."},OBJECT:{value:Qe.OBJECT,description:"Indicates this type is an object. `fields` and `interfaces` are valid fields."},INTERFACE:{value:Qe.INTERFACE,description:"Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields."},UNION:{value:Qe.UNION,description:"Indicates this type is a union. `possibleTypes` is a valid field."},ENUM:{value:Qe.ENUM,description:"Indicates this type is an enum. `enumValues` is a valid field."},INPUT_OBJECT:{value:Qe.INPUT_OBJECT,description:"Indicates this type is an input object. `inputFields` is a valid field."},LIST:{value:Qe.LIST,description:"Indicates this type is a list. `ofType` is a valid field."},NON_NULL:{value:Qe.NON_NULL,description:"Indicates this type is a non-null. `ofType` is a valid field."}}});fe.__TypeKind=ps;var UT={name:"__schema",type:new V.GraphQLNonNull(ss),description:"Access the current type schema of this server.",args:[],resolve:(e,t,n,{schema:r})=>r,deprecationReason:void 0,extensions:Object.create(null),astNode:void 0};fe.SchemaMetaFieldDef=UT;var QT={name:"__type",type:ht,description:"Request the type information of a single type.",args:[{name:"name",description:void 0,type:new V.GraphQLNonNull(ve.GraphQLString),defaultValue:void 0,deprecationReason:void 0,extensions:Object.create(null),astNode:void 0}],resolve:(e,{name:t},n,{schema:r})=>r.getType(t),deprecationReason:void 0,extensions:Object.create(null),astNode:void 0};fe.TypeMetaFieldDef=QT;var KT={name:"__typename",type:new V.GraphQLNonNull(ve.GraphQLString),description:"The name of the current Object type at runtime.",args:[],resolve:(e,t,n,{parentType:r})=>r.name,deprecationReason:void 0,extensions:Object.create(null),astNode:void 0};fe.TypeNameMetaFieldDef=KT;var Dd=Object.freeze([ss,us,cs,ht,ls,Wr,ds,ps]);fe.introspectionTypes=Dd;function xT(e){return Dd.some(({name:t})=>e.name===t)}});var wn=_(ar=>{"use strict";Object.defineProperty(ar,"__esModule",{value:!0});ar.GraphQLSchema=void 0;ar.assertSchema=XT;ar.isSchema=Ld;var la=et(),fs=pe(),$T=jr(),BT=qt(),YT=Wi(),ms=Vt(),Ft=te(),Sd=it(),JT=lt();function Ld(e){return(0,$T.instanceOf)(e,ys)}function XT(e){if(!Ld(e))throw new Error(`Expected ${(0,fs.inspect)(e)} to be a GraphQL schema.`);return e}var ys=class{constructor(t){var n,r;this.__validationErrors=t.assumeValid===!0?[]:void 0,(0,BT.isObjectLike)(t)||(0,la.devAssert)(!1,"Must provide configuration object."),!t.types||Array.isArray(t.types)||(0,la.devAssert)(!1,`"types" must be Array if provided but got: ${(0,fs.inspect)(t.types)}.`),!t.directives||Array.isArray(t.directives)||(0,la.devAssert)(!1,`"directives" must be Array if provided but got: ${(0,fs.inspect)(t.directives)}.`),this.description=t.description,this.extensions=(0,YT.toObjMap)(t.extensions),this.astNode=t.astNode,this.extensionASTNodes=(n=t.extensionASTNodes)!==null&&n!==void 0?n:[],this._queryType=t.query,this._mutationType=t.mutation,this._subscriptionType=t.subscription,this._directives=(r=t.directives)!==null&&r!==void 0?r:Sd.specifiedDirectives;let i=new Set(t.types);if(t.types!=null)for(let a of t.types)i.delete(a),kt(a,i);this._queryType!=null&&kt(this._queryType,i),this._mutationType!=null&&kt(this._mutationType,i),this._subscriptionType!=null&&kt(this._subscriptionType,i);for(let a of this._directives)if((0,Sd.isDirective)(a))for(let o of a.args)kt(o.type,i);kt(JT.__Schema,i),this._typeMap=Object.create(null),this._subTypeMap=Object.create(null),this._implementationsMap=Object.create(null);for(let a of i){if(a==null)continue;let o=a.name;if(o||(0,la.devAssert)(!1,"One of the provided types for building the Schema is missing a name."),this._typeMap[o]!==void 0)throw new Error(`Schema must contain uniquely named types but contains multiple types named "${o}".`);if(this._typeMap[o]=a,(0,Ft.isInterfaceType)(a)){for(let s of a.getInterfaces())if((0,Ft.isInterfaceType)(s)){let c=this._implementationsMap[s.name];c===void 0&&(c=this._implementationsMap[s.name]={objects:[],interfaces:[]}),c.interfaces.push(a)}}else if((0,Ft.isObjectType)(a)){for(let s of a.getInterfaces())if((0,Ft.isInterfaceType)(s)){let c=this._implementationsMap[s.name];c===void 0&&(c=this._implementationsMap[s.name]={objects:[],interfaces:[]}),c.objects.push(a)}}}}get[Symbol.toStringTag](){return"GraphQLSchema"}getQueryType(){return this._queryType}getMutationType(){return this._mutationType}getSubscriptionType(){return this._subscriptionType}getRootType(t){switch(t){case ms.OperationTypeNode.QUERY:return this.getQueryType();case ms.OperationTypeNode.MUTATION:return this.getMutationType();case ms.OperationTypeNode.SUBSCRIPTION:return this.getSubscriptionType()}}getTypeMap(){return this._typeMap}getType(t){return this.getTypeMap()[t]}getPossibleTypes(t){return(0,Ft.isUnionType)(t)?t.getTypes():this.getImplementations(t).objects}getImplementations(t){let n=this._implementationsMap[t.name];return n!=null?n:{objects:[],interfaces:[]}}isSubType(t,n){let r=this._subTypeMap[t.name];if(r===void 0){if(r=Object.create(null),(0,Ft.isUnionType)(t))for(let i of t.getTypes())r[i.name]=!0;else{let i=this.getImplementations(t);for(let a of i.objects)r[a.name]=!0;for(let a of i.interfaces)r[a.name]=!0}this._subTypeMap[t.name]=r}return r[n.name]!==void 0}getDirectives(){return this._directives}getDirective(t){return this.getDirectives().find(n=>n.name===t)}toConfig(){return{description:this.description,query:this.getQueryType(),mutation:this.getMutationType(),subscription:this.getSubscriptionType(),types:Object.values(this.getTypeMap()),directives:this.getDirectives(),extensions:this.extensions,astNode:this.astNode,extensionASTNodes:this.extensionASTNodes,assumeValid:this.__validationErrors!==void 0}}};ar.GraphQLSchema=ys;function kt(e,t){let n=(0,Ft.getNamedType)(e);if(!t.has(n)){if(t.add(n),(0,Ft.isUnionType)(n))for(let r of n.getTypes())kt(r,t);else if((0,Ft.isObjectType)(n)||(0,Ft.isInterfaceType)(n)){for(let r of n.getInterfaces())kt(r,t);for(let r of Object.values(n.getFields())){kt(r.type,t);for(let i of r.args)kt(i.type,t)}}else if((0,Ft.isInputObjectType)(n))for(let r of Object.values(n.getFields()))kt(r.type,t)}return t}});var zr=_(da=>{"use strict";Object.defineProperty(da,"__esModule",{value:!0});da.assertValidSchema=ZT;da.validateSchema=Pd;var Ze=pe(),WT=Q(),hs=Vt(),Rd=Qr(),Fe=te(),Ad=it(),HT=lt(),zT=wn();function Pd(e){if((0,zT.assertSchema)(e),e.__validationErrors)return e.__validationErrors;let t=new jd(e);eg(t),tg(t),ng(t);let n=t.getErrors();return e.__validationErrors=n,n}function ZT(e){let t=Pd(e);if(t.length!==0)throw new Error(t.map(n=>n.message).join(`

`))}var jd=class{constructor(t){this._errors=[],this.schema=t}reportError(t,n){let r=Array.isArray(n)?n.filter(Boolean):n;this._errors.push(new WT.GraphQLError(t,{nodes:r}))}getErrors(){return this._errors}};function eg(e){let t=e.schema,n=t.getQueryType();if(!n)e.reportError("Query root type must be provided.",t.astNode);else if(!(0,Fe.isObjectType)(n)){var r;e.reportError(`Query root type must be Object type, it cannot be ${(0,Ze.inspect)(n)}.`,(r=vs(t,hs.OperationTypeNode.QUERY))!==null&&r!==void 0?r:n.astNode)}let i=t.getMutationType();if(i&&!(0,Fe.isObjectType)(i)){var a;e.reportError(`Mutation root type must be Object type if provided, it cannot be ${(0,Ze.inspect)(i)}.`,(a=vs(t,hs.OperationTypeNode.MUTATION))!==null&&a!==void 0?a:i.astNode)}let o=t.getSubscriptionType();if(o&&!(0,Fe.isObjectType)(o)){var s;e.reportError(`Subscription root type must be Object type if provided, it cannot be ${(0,Ze.inspect)(o)}.`,(s=vs(t,hs.OperationTypeNode.SUBSCRIPTION))!==null&&s!==void 0?s:o.astNode)}}function vs(e,t){var n;return(n=[e.astNode,...e.extensionASTNodes].flatMap(r=>{var i;return(i=r==null?void 0:r.operationTypes)!==null&&i!==void 0?i:[]}).find(r=>r.operation===t))===null||n===void 0?void 0:n.type}function tg(e){for(let n of e.schema.getDirectives()){if(!(0,Ad.isDirective)(n)){e.reportError(`Expected directive but got: ${(0,Ze.inspect)(n)}.`,n==null?void 0:n.astNode);continue}Cn(e,n);for(let r of n.args)if(Cn(e,r),(0,Fe.isInputType)(r.type)||e.reportError(`The type of @${n.name}(${r.name}:) must be Input Type but got: ${(0,Ze.inspect)(r.type)}.`,r.astNode),(0,Fe.isRequiredArgument)(r)&&r.deprecationReason!=null){var t;e.reportError(`Required argument @${n.name}(${r.name}:) cannot be deprecated.`,[Ts(r.astNode),(t=r.astNode)===null||t===void 0?void 0:t.type])}}}function Cn(e,t){t.name.startsWith("__")&&e.reportError(`Name "${t.name}" must not begin with "__", which is reserved by GraphQL introspection.`,t.astNode)}function ng(e){let t=ug(e),n=e.schema.getTypeMap();for(let r of Object.values(n)){if(!(0,Fe.isNamedType)(r)){e.reportError(`Expected GraphQL named type but got: ${(0,Ze.inspect)(r)}.`,r.astNode);continue}(0,HT.isIntrospectionType)(r)||Cn(e,r),(0,Fe.isObjectType)(r)||(0,Fe.isInterfaceType)(r)?(Fd(e,r),kd(e,r)):(0,Fe.isUnionType)(r)?ag(e,r):(0,Fe.isEnumType)(r)?og(e,r):(0,Fe.isInputObjectType)(r)&&(sg(e,r),t(r))}}function Fd(e,t){let n=Object.values(t.getFields());n.length===0&&e.reportError(`Type ${t.name} must define one or more fields.`,[t.astNode,...t.extensionASTNodes]);for(let o of n){if(Cn(e,o),!(0,Fe.isOutputType)(o.type)){var r;e.reportError(`The type of ${t.name}.${o.name} must be Output Type but got: ${(0,Ze.inspect)(o.type)}.`,(r=o.astNode)===null||r===void 0?void 0:r.type)}for(let s of o.args){let c=s.name;if(Cn(e,s),!(0,Fe.isInputType)(s.type)){var i;e.reportError(`The type of ${t.name}.${o.name}(${c}:) must be Input Type but got: ${(0,Ze.inspect)(s.type)}.`,(i=s.astNode)===null||i===void 0?void 0:i.type)}if((0,Fe.isRequiredArgument)(s)&&s.deprecationReason!=null){var a;e.reportError(`Required argument ${t.name}.${o.name}(${c}:) cannot be deprecated.`,[Ts(s.astNode),(a=s.astNode)===null||a===void 0?void 0:a.type])}}}}function kd(e,t){let n=Object.create(null);for(let r of t.getInterfaces()){if(!(0,Fe.isInterfaceType)(r)){e.reportError(`Type ${(0,Ze.inspect)(t)} must only implement Interface types, it cannot implement ${(0,Ze.inspect)(r)}.`,Hr(t,r));continue}if(t===r){e.reportError(`Type ${t.name} cannot implement itself because it would create a circular reference.`,Hr(t,r));continue}if(n[r.name]){e.reportError(`Type ${t.name} can only implement ${r.name} once.`,Hr(t,r));continue}n[r.name]=!0,ig(e,t,r),rg(e,t,r)}}function rg(e,t,n){let r=t.getFields();for(let c of Object.values(n.getFields())){let d=c.name,f=r[d];if(!f){e.reportError(`Interface field ${n.name}.${d} expected but ${t.name} does not provide it.`,[c.astNode,t.astNode,...t.extensionASTNodes]);continue}if(!(0,Rd.isTypeSubTypeOf)(e.schema,f.type,c.type)){var i,a;e.reportError(`Interface field ${n.name}.${d} expects type ${(0,Ze.inspect)(c.type)} but ${t.name}.${d} is type ${(0,Ze.inspect)(f.type)}.`,[(i=c.astNode)===null||i===void 0?void 0:i.type,(a=f.astNode)===null||a===void 0?void 0:a.type])}for(let m of c.args){let g=m.name,T=f.args.find(O=>O.name===g);if(!T){e.reportError(`Interface field argument ${n.name}.${d}(${g}:) expected but ${t.name}.${d} does not provide it.`,[m.astNode,f.astNode]);continue}if(!(0,Rd.isEqualType)(m.type,T.type)){var o,s;e.reportError(`Interface field argument ${n.name}.${d}(${g}:) expects type ${(0,Ze.inspect)(m.type)} but ${t.name}.${d}(${g}:) is type ${(0,Ze.inspect)(T.type)}.`,[(o=m.astNode)===null||o===void 0?void 0:o.type,(s=T.astNode)===null||s===void 0?void 0:s.type])}}for(let m of f.args){let g=m.name;!c.args.find(O=>O.name===g)&&(0,Fe.isRequiredArgument)(m)&&e.reportError(`Object field ${t.name}.${d} includes required argument ${g} that is missing from the Interface field ${n.name}.${d}.`,[m.astNode,c.astNode])}}}function ig(e,t,n){let r=t.getInterfaces();for(let i of n.getInterfaces())r.includes(i)||e.reportError(i===t?`Type ${t.name} cannot implement ${n.name} because it would create a circular reference.`:`Type ${t.name} must implement ${i.name} because it is implemented by ${n.name}.`,[...Hr(n,i),...Hr(t,n)])}function ag(e,t){let n=t.getTypes();n.length===0&&e.reportError(`Union type ${t.name} must define one or more member types.`,[t.astNode,...t.extensionASTNodes]);let r=Object.create(null);for(let i of n){if(r[i.name]){e.reportError(`Union type ${t.name} can only include type ${i.name} once.`,wd(t,i.name));continue}r[i.name]=!0,(0,Fe.isObjectType)(i)||e.reportError(`Union type ${t.name} can only include Object types, it cannot include ${(0,Ze.inspect)(i)}.`,wd(t,String(i)))}}function og(e,t){let n=t.getValues();n.length===0&&e.reportError(`Enum type ${t.name} must define one or more values.`,[t.astNode,...t.extensionASTNodes]);for(let r of n)Cn(e,r)}function sg(e,t){let n=Object.values(t.getFields());n.length===0&&e.reportError(`Input Object type ${t.name} must define one or more fields.`,[t.astNode,...t.extensionASTNodes]);for(let a of n){if(Cn(e,a),!(0,Fe.isInputType)(a.type)){var r;e.reportError(`The type of ${t.name}.${a.name} must be Input Type but got: ${(0,Ze.inspect)(a.type)}.`,(r=a.astNode)===null||r===void 0?void 0:r.type)}if((0,Fe.isRequiredInputField)(a)&&a.deprecationReason!=null){var i;e.reportError(`Required input field ${t.name}.${a.name} cannot be deprecated.`,[Ts(a.astNode),(i=a.astNode)===null||i===void 0?void 0:i.type])}}}function ug(e){let t=Object.create(null),n=[],r=Object.create(null);return i;function i(a){if(t[a.name])return;t[a.name]=!0,r[a.name]=n.length;let o=Object.values(a.getFields());for(let s of o)if((0,Fe.isNonNullType)(s.type)&&(0,Fe.isInputObjectType)(s.type.ofType)){let c=s.type.ofType,d=r[c.name];if(n.push(s),d===void 0)i(c);else{let f=n.slice(d),m=f.map(g=>g.name).join(".");e.reportError(`Cannot reference Input Object "${c.name}" within itself through a series of non-null fields: "${m}".`,f.map(g=>g.astNode))}n.pop()}r[a.name]=void 0}}function Hr(e,t){let{astNode:n,extensionASTNodes:r}=e;return(n!=null?[n,...r]:r).flatMap(a=>{var o;return(o=a.interfaces)!==null&&o!==void 0?o:[]}).filter(a=>a.name.value===t.name)}function wd(e,t){let{astNode:n,extensionASTNodes:r}=e;return(n!=null?[n,...r]:r).flatMap(a=>{var o;return(o=a.types)!==null&&o!==void 0?o:[]}).filter(a=>a.name.value===t)}function Ts(e){var t;return e==null||(t=e.directives)===null||t===void 0?void 0:t.find(n=>n.name.value===Ad.GraphQLDeprecatedDirective.name)}});var Qt=_(Es=>{"use strict";Object.defineProperty(Es,"__esModule",{value:!0});Es.typeFromAST=bs;var gs=ie(),Cd=te();function bs(e,t){switch(t.kind){case gs.Kind.LIST_TYPE:{let n=bs(e,t.type);return n&&new Cd.GraphQLList(n)}case gs.Kind.NON_NULL_TYPE:{let n=bs(e,t.type);return n&&new Cd.GraphQLNonNull(n)}case gs.Kind.NAMED_TYPE:return e.getType(t.name.value)}}});var pa=_(Zr=>{"use strict";Object.defineProperty(Zr,"__esModule",{value:!0});Zr.TypeInfo=void 0;Zr.visitWithTypeInfo=dg;var cg=Vt(),Re=ie(),qd=Ln(),Ae=te(),or=lt(),Vd=Qt(),Md=class{constructor(t,n,r){this._schema=t,this._typeStack=[],this._parentTypeStack=[],this._inputTypeStack=[],this._fieldDefStack=[],this._defaultValueStack=[],this._directive=null,this._argument=null,this._enumValue=null,this._getFieldDef=r!=null?r:lg,n&&((0,Ae.isInputType)(n)&&this._inputTypeStack.push(n),(0,Ae.isCompositeType)(n)&&this._parentTypeStack.push(n),(0,Ae.isOutputType)(n)&&this._typeStack.push(n))}get[Symbol.toStringTag](){return"TypeInfo"}getType(){if(this._typeStack.length>0)return this._typeStack[this._typeStack.length-1]}getParentType(){if(this._parentTypeStack.length>0)return this._parentTypeStack[this._parentTypeStack.length-1]}getInputType(){if(this._inputTypeStack.length>0)return this._inputTypeStack[this._inputTypeStack.length-1]}getParentInputType(){if(this._inputTypeStack.length>1)return this._inputTypeStack[this._inputTypeStack.length-2]}getFieldDef(){if(this._fieldDefStack.length>0)return this._fieldDefStack[this._fieldDefStack.length-1]}getDefaultValue(){if(this._defaultValueStack.length>0)return this._defaultValueStack[this._defaultValueStack.length-1]}getDirective(){return this._directive}getArgument(){return this._argument}getEnumValue(){return this._enumValue}enter(t){let n=this._schema;switch(t.kind){case Re.Kind.SELECTION_SET:{let i=(0,Ae.getNamedType)(this.getType());this._parentTypeStack.push((0,Ae.isCompositeType)(i)?i:void 0);break}case Re.Kind.FIELD:{let i=this.getParentType(),a,o;i&&(a=this._getFieldDef(n,i,t),a&&(o=a.type)),this._fieldDefStack.push(a),this._typeStack.push((0,Ae.isOutputType)(o)?o:void 0);break}case Re.Kind.DIRECTIVE:this._directive=n.getDirective(t.name.value);break;case Re.Kind.OPERATION_DEFINITION:{let i=n.getRootType(t.operation);this._typeStack.push((0,Ae.isObjectType)(i)?i:void 0);break}case Re.Kind.INLINE_FRAGMENT:case Re.Kind.FRAGMENT_DEFINITION:{let i=t.typeCondition,a=i?(0,Vd.typeFromAST)(n,i):(0,Ae.getNamedType)(this.getType());this._typeStack.push((0,Ae.isOutputType)(a)?a:void 0);break}case Re.Kind.VARIABLE_DEFINITION:{let i=(0,Vd.typeFromAST)(n,t.type);this._inputTypeStack.push((0,Ae.isInputType)(i)?i:void 0);break}case Re.Kind.ARGUMENT:{var r;let i,a,o=(r=this.getDirective())!==null&&r!==void 0?r:this.getFieldDef();o&&(i=o.args.find(s=>s.name===t.name.value),i&&(a=i.type)),this._argument=i,this._defaultValueStack.push(i?i.defaultValue:void 0),this._inputTypeStack.push((0,Ae.isInputType)(a)?a:void 0);break}case Re.Kind.LIST:{let i=(0,Ae.getNullableType)(this.getInputType()),a=(0,Ae.isListType)(i)?i.ofType:i;this._defaultValueStack.push(void 0),this._inputTypeStack.push((0,Ae.isInputType)(a)?a:void 0);break}case Re.Kind.OBJECT_FIELD:{let i=(0,Ae.getNamedType)(this.getInputType()),a,o;(0,Ae.isInputObjectType)(i)&&(o=i.getFields()[t.name.value],o&&(a=o.type)),this._defaultValueStack.push(o?o.defaultValue:void 0),this._inputTypeStack.push((0,Ae.isInputType)(a)?a:void 0);break}case Re.Kind.ENUM:{let i=(0,Ae.getNamedType)(this.getInputType()),a;(0,Ae.isEnumType)(i)&&(a=i.getValue(t.value)),this._enumValue=a;break}default:}}leave(t){switch(t.kind){case Re.Kind.SELECTION_SET:this._parentTypeStack.pop();break;case Re.Kind.FIELD:this._fieldDefStack.pop(),this._typeStack.pop();break;case Re.Kind.DIRECTIVE:this._directive=null;break;case Re.Kind.OPERATION_DEFINITION:case Re.Kind.INLINE_FRAGMENT:case Re.Kind.FRAGMENT_DEFINITION:this._typeStack.pop();break;case Re.Kind.VARIABLE_DEFINITION:this._inputTypeStack.pop();break;case Re.Kind.ARGUMENT:this._argument=null,this._defaultValueStack.pop(),this._inputTypeStack.pop();break;case Re.Kind.LIST:case Re.Kind.OBJECT_FIELD:this._defaultValueStack.pop(),this._inputTypeStack.pop();break;case Re.Kind.ENUM:this._enumValue=null;break;default:}}};Zr.TypeInfo=Md;function lg(e,t,n){let r=n.name.value;if(r===or.SchemaMetaFieldDef.name&&e.getQueryType()===t)return or.SchemaMetaFieldDef;if(r===or.TypeMetaFieldDef.name&&e.getQueryType()===t)return or.TypeMetaFieldDef;if(r===or.TypeNameMetaFieldDef.name&&(0,Ae.isCompositeType)(t))return or.TypeNameMetaFieldDef;if((0,Ae.isObjectType)(t)||(0,Ae.isInterfaceType)(t))return t.getFields()[r]}function dg(e,t){return{enter(...n){let r=n[0];e.enter(r);let i=(0,qd.getEnterLeaveForKind)(t,r.kind).enter;if(i){let a=i.apply(t,n);return a!==void 0&&(e.leave(r),(0,cg.isNode)(a)&&e.enter(a)),a}},leave(...n){let r=n[0],i=(0,qd.getEnterLeaveForKind)(t,r.kind).leave,a;return i&&(a=i.apply(t,n)),e.leave(r),a}}}});var qn=_(vt=>{"use strict";Object.defineProperty(vt,"__esModule",{value:!0});vt.isConstValueNode=_s;vt.isDefinitionNode=pg;vt.isExecutableDefinitionNode=Gd;vt.isSelectionNode=fg;vt.isTypeDefinitionNode=Kd;vt.isTypeExtensionNode=$d;vt.isTypeNode=mg;vt.isTypeSystemDefinitionNode=Qd;vt.isTypeSystemExtensionNode=xd;vt.isValueNode=Ud;var ne=ie();function pg(e){return Gd(e)||Qd(e)||xd(e)}function Gd(e){return e.kind===ne.Kind.OPERATION_DEFINITION||e.kind===ne.Kind.FRAGMENT_DEFINITION}function fg(e){return e.kind===ne.Kind.FIELD||e.kind===ne.Kind.FRAGMENT_SPREAD||e.kind===ne.Kind.INLINE_FRAGMENT}function Ud(e){return e.kind===ne.Kind.VARIABLE||e.kind===ne.Kind.INT||e.kind===ne.Kind.FLOAT||e.kind===ne.Kind.STRING||e.kind===ne.Kind.BOOLEAN||e.kind===ne.Kind.NULL||e.kind===ne.Kind.ENUM||e.kind===ne.Kind.LIST||e.kind===ne.Kind.OBJECT}function _s(e){return Ud(e)&&(e.kind===ne.Kind.LIST?e.values.some(_s):e.kind===ne.Kind.OBJECT?e.fields.some(t=>_s(t.value)):e.kind!==ne.Kind.VARIABLE)}function mg(e){return e.kind===ne.Kind.NAMED_TYPE||e.kind===ne.Kind.LIST_TYPE||e.kind===ne.Kind.NON_NULL_TYPE}function Qd(e){return e.kind===ne.Kind.SCHEMA_DEFINITION||Kd(e)||e.kind===ne.Kind.DIRECTIVE_DEFINITION}function Kd(e){return e.kind===ne.Kind.SCALAR_TYPE_DEFINITION||e.kind===ne.Kind.OBJECT_TYPE_DEFINITION||e.kind===ne.Kind.INTERFACE_TYPE_DEFINITION||e.kind===ne.Kind.UNION_TYPE_DEFINITION||e.kind===ne.Kind.ENUM_TYPE_DEFINITION||e.kind===ne.Kind.INPUT_OBJECT_TYPE_DEFINITION}function xd(e){return e.kind===ne.Kind.SCHEMA_EXTENSION||$d(e)}function $d(e){return e.kind===ne.Kind.SCALAR_TYPE_EXTENSION||e.kind===ne.Kind.OBJECT_TYPE_EXTENSION||e.kind===ne.Kind.INTERFACE_TYPE_EXTENSION||e.kind===ne.Kind.UNION_TYPE_EXTENSION||e.kind===ne.Kind.ENUM_TYPE_EXTENSION||e.kind===ne.Kind.INPUT_OBJECT_TYPE_EXTENSION}});var Os=_(Ns=>{"use strict";Object.defineProperty(Ns,"__esModule",{value:!0});Ns.ExecutableDefinitionsRule=vg;var yg=Q(),Bd=ie(),hg=qn();function vg(e){return{Document(t){for(let n of t.definitions)if(!(0,hg.isExecutableDefinitionNode)(n)){let r=n.kind===Bd.Kind.SCHEMA_DEFINITION||n.kind===Bd.Kind.SCHEMA_EXTENSION?"schema":'"'+n.name.value+'"';e.reportError(new yg.GraphQLError(`The ${r} definition is not executable.`,{nodes:n}))}return!1}}}});var Ds=_(Is=>{"use strict";Object.defineProperty(Is,"__esModule",{value:!0});Is.FieldsOnCorrectTypeRule=Eg;var Yd=pn(),Tg=Cr(),gg=mn(),bg=Q(),ei=te();function Eg(e){return{Field(t){let n=e.getParentType();if(n&&!e.getFieldDef()){let i=e.getSchema(),a=t.name.value,o=(0,Yd.didYouMean)("to use an inline fragment on",_g(i,n,a));o===""&&(o=(0,Yd.didYouMean)(Ng(n,a))),e.reportError(new bg.GraphQLError(`Cannot query field "${a}" on type "${n.name}".`+o,{nodes:t}))}}}}function _g(e,t,n){if(!(0,ei.isAbstractType)(t))return[];let r=new Set,i=Object.create(null);for(let o of e.getPossibleTypes(t))if(!!o.getFields()[n]){r.add(o),i[o.name]=1;for(let s of o.getInterfaces()){var a;!s.getFields()[n]||(r.add(s),i[s.name]=((a=i[s.name])!==null&&a!==void 0?a:0)+1)}}return[...r].sort((o,s)=>{let c=i[s.name]-i[o.name];return c!==0?c:(0,ei.isInterfaceType)(o)&&e.isSubType(o,s)?-1:(0,ei.isInterfaceType)(s)&&e.isSubType(s,o)?1:(0,Tg.naturalCompare)(o.name,s.name)}).map(o=>o.name)}function Ng(e,t){if((0,ei.isObjectType)(e)||(0,ei.isInterfaceType)(e)){let n=Object.keys(e.getFields());return(0,gg.suggestionList)(t,n)}return[]}});var Ls=_(Ss=>{"use strict";Object.defineProperty(Ss,"__esModule",{value:!0});Ss.FragmentsOnCompositeTypesRule=Og;var Jd=Q(),Xd=ct(),Wd=te(),Hd=Qt();function Og(e){return{InlineFragment(t){let n=t.typeCondition;if(n){let r=(0,Hd.typeFromAST)(e.getSchema(),n);if(r&&!(0,Wd.isCompositeType)(r)){let i=(0,Xd.print)(n);e.reportError(new Jd.GraphQLError(`Fragment cannot condition on non composite type "${i}".`,{nodes:n}))}}},FragmentDefinition(t){let n=(0,Hd.typeFromAST)(e.getSchema(),t.typeCondition);if(n&&!(0,Wd.isCompositeType)(n)){let r=(0,Xd.print)(t.typeCondition);e.reportError(new Jd.GraphQLError(`Fragment "${t.name.value}" cannot condition on non composite type "${r}".`,{nodes:t.typeCondition}))}}}}});var Rs=_(fa=>{"use strict";Object.defineProperty(fa,"__esModule",{value:!0});fa.KnownArgumentNamesOnDirectivesRule=tp;fa.KnownArgumentNamesRule=Sg;var zd=pn(),Zd=mn(),ep=Q(),Ig=ie(),Dg=it();function Sg(e){return{...tp(e),Argument(t){let n=e.getArgument(),r=e.getFieldDef(),i=e.getParentType();if(!n&&r&&i){let a=t.name.value,o=r.args.map(c=>c.name),s=(0,Zd.suggestionList)(a,o);e.reportError(new ep.GraphQLError(`Unknown argument "${a}" on field "${i.name}.${r.name}".`+(0,zd.didYouMean)(s),{nodes:t}))}}}}function tp(e){let t=Object.create(null),n=e.getSchema(),r=n?n.getDirectives():Dg.specifiedDirectives;for(let o of r)t[o.name]=o.args.map(s=>s.name);let i=e.getDocument().definitions;for(let o of i)if(o.kind===Ig.Kind.DIRECTIVE_DEFINITION){var a;let s=(a=o.arguments)!==null&&a!==void 0?a:[];t[o.name.value]=s.map(c=>c.name.value)}return{Directive(o){let s=o.name.value,c=t[s];if(o.arguments&&c)for(let d of o.arguments){let f=d.name.value;if(!c.includes(f)){let m=(0,Zd.suggestionList)(f,c);e.reportError(new ep.GraphQLError(`Unknown argument "${f}" on directive "@${s}".`+(0,zd.didYouMean)(m),{nodes:d}))}}return!1}}}});var Fs=_(js=>{"use strict";Object.defineProperty(js,"__esModule",{value:!0});js.KnownDirectivesRule=Ag;var Lg=pe(),As=He(),np=Q(),Ps=Vt(),Ke=zn(),Ne=ie(),Rg=it();function Ag(e){let t=Object.create(null),n=e.getSchema(),r=n?n.getDirectives():Rg.specifiedDirectives;for(let a of r)t[a.name]=a.locations;let i=e.getDocument().definitions;for(let a of i)a.kind===Ne.Kind.DIRECTIVE_DEFINITION&&(t[a.name.value]=a.locations.map(o=>o.value));return{Directive(a,o,s,c,d){let f=a.name.value,m=t[f];if(!m){e.reportError(new np.GraphQLError(`Unknown directive "@${f}".`,{nodes:a}));return}let g=Pg(d);g&&!m.includes(g)&&e.reportError(new np.GraphQLError(`Directive "@${f}" may not be used on ${g}.`,{nodes:a}))}}}function Pg(e){let t=e[e.length-1];switch("kind"in t||(0,As.invariant)(!1),t.kind){case Ne.Kind.OPERATION_DEFINITION:return jg(t.operation);case Ne.Kind.FIELD:return Ke.DirectiveLocation.FIELD;case Ne.Kind.FRAGMENT_SPREAD:return Ke.DirectiveLocation.FRAGMENT_SPREAD;case Ne.Kind.INLINE_FRAGMENT:return Ke.DirectiveLocation.INLINE_FRAGMENT;case Ne.Kind.FRAGMENT_DEFINITION:return Ke.DirectiveLocation.FRAGMENT_DEFINITION;case Ne.Kind.VARIABLE_DEFINITION:return Ke.DirectiveLocation.VARIABLE_DEFINITION;case Ne.Kind.SCHEMA_DEFINITION:case Ne.Kind.SCHEMA_EXTENSION:return Ke.DirectiveLocation.SCHEMA;case Ne.Kind.SCALAR_TYPE_DEFINITION:case Ne.Kind.SCALAR_TYPE_EXTENSION:return Ke.DirectiveLocation.SCALAR;case Ne.Kind.OBJECT_TYPE_DEFINITION:case Ne.Kind.OBJECT_TYPE_EXTENSION:return Ke.DirectiveLocation.OBJECT;case Ne.Kind.FIELD_DEFINITION:return Ke.DirectiveLocation.FIELD_DEFINITION;case Ne.Kind.INTERFACE_TYPE_DEFINITION:case Ne.Kind.INTERFACE_TYPE_EXTENSION:return Ke.DirectiveLocation.INTERFACE;case Ne.Kind.UNION_TYPE_DEFINITION:case Ne.Kind.UNION_TYPE_EXTENSION:return Ke.DirectiveLocation.UNION;case Ne.Kind.ENUM_TYPE_DEFINITION:case Ne.Kind.ENUM_TYPE_EXTENSION:return Ke.DirectiveLocation.ENUM;case Ne.Kind.ENUM_VALUE_DEFINITION:return Ke.DirectiveLocation.ENUM_VALUE;case Ne.Kind.INPUT_OBJECT_TYPE_DEFINITION:case Ne.Kind.INPUT_OBJECT_TYPE_EXTENSION:return Ke.DirectiveLocation.INPUT_OBJECT;case Ne.Kind.INPUT_VALUE_DEFINITION:{let n=e[e.length-3];return"kind"in n||(0,As.invariant)(!1),n.kind===Ne.Kind.INPUT_OBJECT_TYPE_DEFINITION?Ke.DirectiveLocation.INPUT_FIELD_DEFINITION:Ke.DirectiveLocation.ARGUMENT_DEFINITION}default:(0,As.invariant)(!1,"Unexpected kind: "+(0,Lg.inspect)(t.kind))}}function jg(e){switch(e){case Ps.OperationTypeNode.QUERY:return Ke.DirectiveLocation.QUERY;case Ps.OperationTypeNode.MUTATION:return Ke.DirectiveLocation.MUTATION;case Ps.OperationTypeNode.SUBSCRIPTION:return Ke.DirectiveLocation.SUBSCRIPTION}}});var ws=_(ks=>{"use strict";Object.defineProperty(ks,"__esModule",{value:!0});ks.KnownFragmentNamesRule=kg;var Fg=Q();function kg(e){return{FragmentSpread(t){let n=t.name.value;e.getFragment(n)||e.reportError(new Fg.GraphQLError(`Unknown fragment "${n}".`,{nodes:t.name}))}}}});var Vs=_(qs=>{"use strict";Object.defineProperty(qs,"__esModule",{value:!0});qs.KnownTypeNamesRule=Gg;var wg=pn(),Cg=mn(),qg=Q(),Cs=qn(),Vg=lt(),Mg=Ut();function Gg(e){let t=e.getSchema(),n=t?t.getTypeMap():Object.create(null),r=Object.create(null);for(let a of e.getDocument().definitions)(0,Cs.isTypeDefinitionNode)(a)&&(r[a.name.value]=!0);let i=[...Object.keys(n),...Object.keys(r)];return{NamedType(a,o,s,c,d){let f=a.name.value;if(!n[f]&&!r[f]){var m;let g=(m=d[2])!==null&&m!==void 0?m:s,T=g!=null&&Ug(g);if(T&&rp.includes(f))return;let O=(0,Cg.suggestionList)(f,T?rp.concat(i):i);e.reportError(new qg.GraphQLError(`Unknown type "${f}".`+(0,wg.didYouMean)(O),{nodes:a}))}}}}var rp=[...Mg.specifiedScalarTypes,...Vg.introspectionTypes].map(e=>e.name);function Ug(e){return"kind"in e&&((0,Cs.isTypeSystemDefinitionNode)(e)||(0,Cs.isTypeSystemExtensionNode)(e))}});var Gs=_(Ms=>{"use strict";Object.defineProperty(Ms,"__esModule",{value:!0});Ms.LoneAnonymousOperationRule=xg;var Qg=Q(),Kg=ie();function xg(e){let t=0;return{Document(n){t=n.definitions.filter(r=>r.kind===Kg.Kind.OPERATION_DEFINITION).length},OperationDefinition(n){!n.name&&t>1&&e.reportError(new Qg.GraphQLError("This anonymous operation must be the only defined operation.",{nodes:n}))}}}});var Qs=_(Us=>{"use strict";Object.defineProperty(Us,"__esModule",{value:!0});Us.LoneSchemaDefinitionRule=$g;var ip=Q();function $g(e){var t,n,r;let i=e.getSchema(),a=(t=(n=(r=i==null?void 0:i.astNode)!==null&&r!==void 0?r:i==null?void 0:i.getQueryType())!==null&&n!==void 0?n:i==null?void 0:i.getMutationType())!==null&&t!==void 0?t:i==null?void 0:i.getSubscriptionType(),o=0;return{SchemaDefinition(s){if(a){e.reportError(new ip.GraphQLError("Cannot define a new schema within a schema extension.",{nodes:s}));return}o>0&&e.reportError(new ip.GraphQLError("Must provide only one schema definition.",{nodes:s})),++o}}}});var xs=_(Ks=>{"use strict";Object.defineProperty(Ks,"__esModule",{value:!0});Ks.NoFragmentCyclesRule=Yg;var Bg=Q();function Yg(e){let t=Object.create(null),n=[],r=Object.create(null);return{OperationDefinition:()=>!1,FragmentDefinition(a){return i(a),!1}};function i(a){if(t[a.name.value])return;let o=a.name.value;t[o]=!0;let s=e.getFragmentSpreads(a.selectionSet);if(s.length!==0){r[o]=n.length;for(let c of s){let d=c.name.value,f=r[d];if(n.push(c),f===void 0){let m=e.getFragment(d);m&&i(m)}else{let m=n.slice(f),g=m.slice(0,-1).map(T=>'"'+T.name.value+'"').join(", ");e.reportError(new Bg.GraphQLError(`Cannot spread fragment "${d}" within itself`+(g!==""?` via ${g}.`:"."),{nodes:m}))}n.pop()}r[o]=void 0}}}});var Bs=_($s=>{"use strict";Object.defineProperty($s,"__esModule",{value:!0});$s.NoUndefinedVariablesRule=Xg;var Jg=Q();function Xg(e){let t=Object.create(null);return{OperationDefinition:{enter(){t=Object.create(null)},leave(n){let r=e.getRecursiveVariableUsages(n);for(let{node:i}of r){let a=i.name.value;t[a]!==!0&&e.reportError(new Jg.GraphQLError(n.name?`Variable "$${a}" is not defined by operation "${n.name.value}".`:`Variable "$${a}" is not defined.`,{nodes:[i,n]}))}}},VariableDefinition(n){t[n.variable.name.value]=!0}}}});var Js=_(Ys=>{"use strict";Object.defineProperty(Ys,"__esModule",{value:!0});Ys.NoUnusedFragmentsRule=Hg;var Wg=Q();function Hg(e){let t=[],n=[];return{OperationDefinition(r){return t.push(r),!1},FragmentDefinition(r){return n.push(r),!1},Document:{leave(){let r=Object.create(null);for(let i of t)for(let a of e.getRecursivelyReferencedFragments(i))r[a.name.value]=!0;for(let i of n){let a=i.name.value;r[a]!==!0&&e.reportError(new Wg.GraphQLError(`Fragment "${a}" is never used.`,{nodes:i}))}}}}}});var Ws=_(Xs=>{"use strict";Object.defineProperty(Xs,"__esModule",{value:!0});Xs.NoUnusedVariablesRule=Zg;var zg=Q();function Zg(e){let t=[];return{OperationDefinition:{enter(){t=[]},leave(n){let r=Object.create(null),i=e.getRecursiveVariableUsages(n);for(let{node:a}of i)r[a.name.value]=!0;for(let a of t){let o=a.variable.name.value;r[o]!==!0&&e.reportError(new zg.GraphQLError(n.name?`Variable "$${o}" is never used in operation "${n.name.value}".`:`Variable "$${o}" is never used.`,{nodes:a}))}}},VariableDefinition(n){t.push(n)}}}});var Zs=_(zs=>{"use strict";Object.defineProperty(zs,"__esModule",{value:!0});zs.sortValueNode=Hs;var eb=Cr(),en=ie();function Hs(e){switch(e.kind){case en.Kind.OBJECT:return{...e,fields:tb(e.fields)};case en.Kind.LIST:return{...e,values:e.values.map(Hs)};case en.Kind.INT:case en.Kind.FLOAT:case en.Kind.STRING:case en.Kind.BOOLEAN:case en.Kind.NULL:case en.Kind.ENUM:case en.Kind.VARIABLE:return e}}function tb(e){return e.map(t=>({...t,value:Hs(t.value)})).sort((t,n)=>(0,eb.naturalCompare)(t.name.value,n.name.value))}});var iu=_(ru=>{"use strict";Object.defineProperty(ru,"__esModule",{value:!0});ru.OverlappingFieldsCanBeMergedRule=ab;var ap=pe(),nb=Q(),ti=ie(),rb=ct(),at=te(),ib=Zs(),op=Qt();function sp(e){return Array.isArray(e)?e.map(([t,n])=>`subfields "${t}" conflict because `+sp(n)).join(" and "):e}function ab(e){let t=new dp,n=new Map;return{SelectionSet(r){let i=ob(e,n,t,e.getParentType(),r);for(let[[a,o],s,c]of i){let d=sp(o);e.reportError(new nb.GraphQLError(`Fields "${a}" conflict because ${d}. Use different aliases on the fields to fetch both if this was intentional.`,{nodes:s.concat(c)}))}}}}function ob(e,t,n,r,i){let a=[],[o,s]=ha(e,t,r,i);if(ub(e,a,t,n,o),s.length!==0)for(let c=0;c<s.length;c++){ma(e,a,t,n,!1,o,s[c]);for(let d=c+1;d<s.length;d++)ya(e,a,t,n,!1,s[c],s[d])}return a}function ma(e,t,n,r,i,a,o){let s=e.getFragment(o);if(!s)return;let[c,d]=nu(e,n,s);if(a!==c){eu(e,t,n,r,i,a,c);for(let f of d)r.has(f,o,i)||(r.add(f,o,i),ma(e,t,n,r,i,a,f))}}function ya(e,t,n,r,i,a,o){if(a===o||r.has(a,o,i))return;r.add(a,o,i);let s=e.getFragment(a),c=e.getFragment(o);if(!s||!c)return;let[d,f]=nu(e,n,s),[m,g]=nu(e,n,c);eu(e,t,n,r,i,d,m);for(let T of g)ya(e,t,n,r,i,a,T);for(let T of f)ya(e,t,n,r,i,T,o)}function sb(e,t,n,r,i,a,o,s){let c=[],[d,f]=ha(e,t,i,a),[m,g]=ha(e,t,o,s);eu(e,c,t,n,r,d,m);for(let T of g)ma(e,c,t,n,r,d,T);for(let T of f)ma(e,c,t,n,r,m,T);for(let T of f)for(let O of g)ya(e,c,t,n,r,T,O);return c}function ub(e,t,n,r,i){for(let[a,o]of Object.entries(i))if(o.length>1)for(let s=0;s<o.length;s++)for(let c=s+1;c<o.length;c++){let d=up(e,n,r,!1,a,o[s],o[c]);d&&t.push(d)}}function eu(e,t,n,r,i,a,o){for(let[s,c]of Object.entries(a)){let d=o[s];if(d)for(let f of c)for(let m of d){let g=up(e,n,r,i,s,f,m);g&&t.push(g)}}}function up(e,t,n,r,i,a,o){let[s,c,d]=a,[f,m,g]=o,T=r||s!==f&&(0,at.isObjectType)(s)&&(0,at.isObjectType)(f);if(!T){let se=c.name.value,z=m.name.value;if(se!==z)return[[i,`"${se}" and "${z}" are different fields`],[c],[m]];if(cp(c)!==cp(m))return[[i,"they have differing arguments"],[c],[m]]}let O=d==null?void 0:d.type,U=g==null?void 0:g.type;if(O&&U&&tu(O,U))return[[i,`they return conflicting types "${(0,ap.inspect)(O)}" and "${(0,ap.inspect)(U)}"`],[c],[m]];let k=c.selectionSet,Y=m.selectionSet;if(k&&Y){let se=sb(e,t,n,T,(0,at.getNamedType)(O),k,(0,at.getNamedType)(U),Y);return cb(se,i,c,m)}}function cp(e){var t;let n=(t=e.arguments)!==null&&t!==void 0?t:[],r={kind:ti.Kind.OBJECT,fields:n.map(i=>({kind:ti.Kind.OBJECT_FIELD,name:i.name,value:i.value}))};return(0,rb.print)((0,ib.sortValueNode)(r))}function tu(e,t){return(0,at.isListType)(e)?(0,at.isListType)(t)?tu(e.ofType,t.ofType):!0:(0,at.isListType)(t)?!0:(0,at.isNonNullType)(e)?(0,at.isNonNullType)(t)?tu(e.ofType,t.ofType):!0:(0,at.isNonNullType)(t)?!0:(0,at.isLeafType)(e)||(0,at.isLeafType)(t)?e!==t:!1}function ha(e,t,n,r){let i=t.get(r);if(i)return i;let a=Object.create(null),o=Object.create(null);lp(e,n,r,a,o);let s=[a,Object.keys(o)];return t.set(r,s),s}function nu(e,t,n){let r=t.get(n.selectionSet);if(r)return r;let i=(0,op.typeFromAST)(e.getSchema(),n.typeCondition);return ha(e,t,i,n.selectionSet)}function lp(e,t,n,r,i){for(let a of n.selections)switch(a.kind){case ti.Kind.FIELD:{let o=a.name.value,s;((0,at.isObjectType)(t)||(0,at.isInterfaceType)(t))&&(s=t.getFields()[o]);let c=a.alias?a.alias.value:o;r[c]||(r[c]=[]),r[c].push([t,a,s]);break}case ti.Kind.FRAGMENT_SPREAD:i[a.name.value]=!0;break;case ti.Kind.INLINE_FRAGMENT:{let o=a.typeCondition,s=o?(0,op.typeFromAST)(e.getSchema(),o):t;lp(e,s,a.selectionSet,r,i);break}}}function cb(e,t,n,r){if(e.length>0)return[[t,e.map(([i])=>i)],[n,...e.map(([,i])=>i).flat()],[r,...e.map(([,,i])=>i).flat()]]}var dp=class{constructor(){this._data=new Map}has(t,n,r){var i;let[a,o]=t<n?[t,n]:[n,t],s=(i=this._data.get(a))===null||i===void 0?void 0:i.get(o);return s===void 0?!1:r?!0:r===s}add(t,n,r){let[i,a]=t<n?[t,n]:[n,t],o=this._data.get(i);o===void 0?this._data.set(i,new Map([[a,r]])):o.set(a,r)}}});var su=_(ou=>{"use strict";Object.defineProperty(ou,"__esModule",{value:!0});ou.PossibleFragmentSpreadsRule=db;var va=pe(),pp=Q(),au=te(),fp=Qr(),lb=Qt();function db(e){return{InlineFragment(t){let n=e.getType(),r=e.getParentType();if((0,au.isCompositeType)(n)&&(0,au.isCompositeType)(r)&&!(0,fp.doTypesOverlap)(e.getSchema(),n,r)){let i=(0,va.inspect)(r),a=(0,va.inspect)(n);e.reportError(new pp.GraphQLError(`Fragment cannot be spread here as objects of type "${i}" can never be of type "${a}".`,{nodes:t}))}},FragmentSpread(t){let n=t.name.value,r=pb(e,n),i=e.getParentType();if(r&&i&&!(0,fp.doTypesOverlap)(e.getSchema(),r,i)){let a=(0,va.inspect)(i),o=(0,va.inspect)(r);e.reportError(new pp.GraphQLError(`Fragment "${n}" cannot be spread here as objects of type "${a}" can never be of type "${o}".`,{nodes:t}))}}}}function pb(e,t){let n=e.getFragment(t);if(n){let r=(0,lb.typeFromAST)(e.getSchema(),n.typeCondition);if((0,au.isCompositeType)(r))return r}}});var cu=_(uu=>{"use strict";Object.defineProperty(uu,"__esModule",{value:!0});uu.PossibleTypeExtensionsRule=hb;var fb=pn(),mp=pe(),yp=He(),mb=mn(),hp=Q(),Ie=ie(),yb=qn(),sr=te();function hb(e){let t=e.getSchema(),n=Object.create(null);for(let i of e.getDocument().definitions)(0,yb.isTypeDefinitionNode)(i)&&(n[i.name.value]=i);return{ScalarTypeExtension:r,ObjectTypeExtension:r,InterfaceTypeExtension:r,UnionTypeExtension:r,EnumTypeExtension:r,InputObjectTypeExtension:r};function r(i){let a=i.name.value,o=n[a],s=t==null?void 0:t.getType(a),c;if(o?c=vb[o.kind]:s&&(c=Tb(s)),c){if(c!==i.kind){let d=gb(i.kind);e.reportError(new hp.GraphQLError(`Cannot extend non-${d} type "${a}".`,{nodes:o?[o,i]:i}))}}else{let d=Object.keys({...n,...t==null?void 0:t.getTypeMap()}),f=(0,mb.suggestionList)(a,d);e.reportError(new hp.GraphQLError(`Cannot extend type "${a}" because it is not defined.`+(0,fb.didYouMean)(f),{nodes:i.name}))}}}var vb={[Ie.Kind.SCALAR_TYPE_DEFINITION]:Ie.Kind.SCALAR_TYPE_EXTENSION,[Ie.Kind.OBJECT_TYPE_DEFINITION]:Ie.Kind.OBJECT_TYPE_EXTENSION,[Ie.Kind.INTERFACE_TYPE_DEFINITION]:Ie.Kind.INTERFACE_TYPE_EXTENSION,[Ie.Kind.UNION_TYPE_DEFINITION]:Ie.Kind.UNION_TYPE_EXTENSION,[Ie.Kind.ENUM_TYPE_DEFINITION]:Ie.Kind.ENUM_TYPE_EXTENSION,[Ie.Kind.INPUT_OBJECT_TYPE_DEFINITION]:Ie.Kind.INPUT_OBJECT_TYPE_EXTENSION};function Tb(e){if((0,sr.isScalarType)(e))return Ie.Kind.SCALAR_TYPE_EXTENSION;if((0,sr.isObjectType)(e))return Ie.Kind.OBJECT_TYPE_EXTENSION;if((0,sr.isInterfaceType)(e))return Ie.Kind.INTERFACE_TYPE_EXTENSION;if((0,sr.isUnionType)(e))return Ie.Kind.UNION_TYPE_EXTENSION;if((0,sr.isEnumType)(e))return Ie.Kind.ENUM_TYPE_EXTENSION;if((0,sr.isInputObjectType)(e))return Ie.Kind.INPUT_OBJECT_TYPE_EXTENSION;(0,yp.invariant)(!1,"Unexpected type: "+(0,mp.inspect)(e))}function gb(e){switch(e){case Ie.Kind.SCALAR_TYPE_EXTENSION:return"scalar";case Ie.Kind.OBJECT_TYPE_EXTENSION:return"object";case Ie.Kind.INTERFACE_TYPE_EXTENSION:return"interface";case Ie.Kind.UNION_TYPE_EXTENSION:return"union";case Ie.Kind.ENUM_TYPE_EXTENSION:return"enum";case Ie.Kind.INPUT_OBJECT_TYPE_EXTENSION:return"input object";default:(0,yp.invariant)(!1,"Unexpected kind: "+(0,mp.inspect)(e))}}});var du=_(Ta=>{"use strict";Object.defineProperty(Ta,"__esModule",{value:!0});Ta.ProvidedRequiredArgumentsOnDirectivesRule=Ep;Ta.ProvidedRequiredArgumentsRule=_b;var vp=pe(),Tp=fn(),gp=Q(),bp=ie(),bb=ct(),lu=te(),Eb=it();function _b(e){return{...Ep(e),Field:{leave(t){var n;let r=e.getFieldDef();if(!r)return!1;let i=new Set((n=t.arguments)===null||n===void 0?void 0:n.map(a=>a.name.value));for(let a of r.args)if(!i.has(a.name)&&(0,lu.isRequiredArgument)(a)){let o=(0,vp.inspect)(a.type);e.reportError(new gp.GraphQLError(`Field "${r.name}" argument "${a.name}" of type "${o}" is required, but it was not provided.`,{nodes:t}))}}}}}function Ep(e){var t;let n=Object.create(null),r=e.getSchema(),i=(t=r==null?void 0:r.getDirectives())!==null&&t!==void 0?t:Eb.specifiedDirectives;for(let s of i)n[s.name]=(0,Tp.keyMap)(s.args.filter(lu.isRequiredArgument),c=>c.name);let a=e.getDocument().definitions;for(let s of a)if(s.kind===bp.Kind.DIRECTIVE_DEFINITION){var o;let c=(o=s.arguments)!==null&&o!==void 0?o:[];n[s.name.value]=(0,Tp.keyMap)(c.filter(Nb),d=>d.name.value)}return{Directive:{leave(s){let c=s.name.value,d=n[c];if(d){var f;let m=(f=s.arguments)!==null&&f!==void 0?f:[],g=new Set(m.map(T=>T.name.value));for(let[T,O]of Object.entries(d))if(!g.has(T)){let U=(0,lu.isType)(O.type)?(0,vp.inspect)(O.type):(0,bb.print)(O.type);e.reportError(new gp.GraphQLError(`Directive "@${c}" argument "${T}" of type "${U}" is required, but it was not provided.`,{nodes:s}))}}}}}}function Nb(e){return e.type.kind===bp.Kind.NON_NULL_TYPE&&e.defaultValue==null}});var fu=_(pu=>{"use strict";Object.defineProperty(pu,"__esModule",{value:!0});pu.ScalarLeafsRule=Ob;var _p=pe(),Np=Q(),Op=te();function Ob(e){return{Field(t){let n=e.getType(),r=t.selectionSet;if(n){if((0,Op.isLeafType)((0,Op.getNamedType)(n))){if(r){let i=t.name.value,a=(0,_p.inspect)(n);e.reportError(new Np.GraphQLError(`Field "${i}" must not have a selection since type "${a}" has no subfields.`,{nodes:r}))}}else if(!r){let i=t.name.value,a=(0,_p.inspect)(n);e.reportError(new Np.GraphQLError(`Field "${i}" of type "${a}" must have a selection of subfields. Did you mean "${i} { ... }"?`,{nodes:t}))}}}}}});var yu=_(mu=>{"use strict";Object.defineProperty(mu,"__esModule",{value:!0});mu.printPathArray=Ib;function Ib(e){return e.map(t=>typeof t=="number"?"["+t.toString()+"]":"."+t).join("")}});var ni=_(ga=>{"use strict";Object.defineProperty(ga,"__esModule",{value:!0});ga.addPath=Db;ga.pathToArray=Sb;function Db(e,t,n){return{prev:e,key:t,typename:n}}function Sb(e){let t=[],n=e;for(;n;)t.push(n.key),n=n.prev;return t.reverse()}});var vu=_(hu=>{"use strict";Object.defineProperty(hu,"__esModule",{value:!0});hu.coerceInputValue=kb;var Lb=pn(),ba=pe(),Rb=He(),Ab=ca(),Pb=qt(),tn=ni(),jb=yu(),Fb=mn(),Vn=Q(),ri=te();function kb(e,t,n=wb){return ii(e,t,n,void 0)}function wb(e,t,n){let r="Invalid value "+(0,ba.inspect)(t);throw e.length>0&&(r+=` at "value${(0,jb.printPathArray)(e)}"`),n.message=r+": "+n.message,n}function ii(e,t,n,r){if((0,ri.isNonNullType)(t)){if(e!=null)return ii(e,t.ofType,n,r);n((0,tn.pathToArray)(r),e,new Vn.GraphQLError(`Expected non-nullable type "${(0,ba.inspect)(t)}" not to be null.`));return}if(e==null)return null;if((0,ri.isListType)(t)){let i=t.ofType;return(0,Ab.isIterableObject)(e)?Array.from(e,(a,o)=>{let s=(0,tn.addPath)(r,o,void 0);return ii(a,i,n,s)}):[ii(e,i,n,r)]}if((0,ri.isInputObjectType)(t)){if(!(0,Pb.isObjectLike)(e)){n((0,tn.pathToArray)(r),e,new Vn.GraphQLError(`Expected type "${t.name}" to be an object.`));return}let i={},a=t.getFields();for(let o of Object.values(a)){let s=e[o.name];if(s===void 0){if(o.defaultValue!==void 0)i[o.name]=o.defaultValue;else if((0,ri.isNonNullType)(o.type)){let c=(0,ba.inspect)(o.type);n((0,tn.pathToArray)(r),e,new Vn.GraphQLError(`Field "${o.name}" of required type "${c}" was not provided.`))}continue}i[o.name]=ii(s,o.type,n,(0,tn.addPath)(r,o.name,t.name))}for(let o of Object.keys(e))if(!a[o]){let s=(0,Fb.suggestionList)(o,Object.keys(t.getFields()));n((0,tn.pathToArray)(r),e,new Vn.GraphQLError(`Field "${o}" is not defined by type "${t.name}".`+(0,Lb.didYouMean)(s)))}return i}if((0,ri.isLeafType)(t)){let i;try{i=t.parseValue(e)}catch(a){a instanceof Vn.GraphQLError?n((0,tn.pathToArray)(r),e,a):n((0,tn.pathToArray)(r),e,new Vn.GraphQLError(`Expected type "${t.name}". `+a.message,{originalError:a}));return}return i===void 0&&n((0,tn.pathToArray)(r),e,new Vn.GraphQLError(`Expected type "${t.name}".`)),i}(0,Rb.invariant)(!1,"Unexpected input type: "+(0,ba.inspect)(t))}});var oi=_(Tu=>{"use strict";Object.defineProperty(Tu,"__esModule",{value:!0});Tu.valueFromAST=ai;var Cb=pe(),qb=He(),Vb=fn(),ur=ie(),Mn=te();function ai(e,t,n){if(!!e){if(e.kind===ur.Kind.VARIABLE){let r=e.name.value;if(n==null||n[r]===void 0)return;let i=n[r];return i===null&&(0,Mn.isNonNullType)(t)?void 0:i}if((0,Mn.isNonNullType)(t))return e.kind===ur.Kind.NULL?void 0:ai(e,t.ofType,n);if(e.kind===ur.Kind.NULL)return null;if((0,Mn.isListType)(t)){let r=t.ofType;if(e.kind===ur.Kind.LIST){let a=[];for(let o of e.values)if(Ip(o,n)){if((0,Mn.isNonNullType)(r))return;a.push(null)}else{let s=ai(o,r,n);if(s===void 0)return;a.push(s)}return a}let i=ai(e,r,n);return i===void 0?void 0:[i]}if((0,Mn.isInputObjectType)(t)){if(e.kind!==ur.Kind.OBJECT)return;let r=Object.create(null),i=(0,Vb.keyMap)(e.fields,a=>a.name.value);for(let a of Object.values(t.getFields())){let o=i[a.name];if(!o||Ip(o.value,n)){if(a.defaultValue!==void 0)r[a.name]=a.defaultValue;else if((0,Mn.isNonNullType)(a.type))return;continue}let s=ai(o.value,a.type,n);if(s===void 0)return;r[a.name]=s}return r}if((0,Mn.isLeafType)(t)){let r;try{r=t.parseLiteral(e,n)}catch{return}return r===void 0?void 0:r}(0,qb.invariant)(!1,"Unexpected input type: "+(0,Cb.inspect)(t))}}function Ip(e,t){return e.kind===ur.Kind.VARIABLE&&(t==null||t[e.name.value]===void 0)}});var dr=_(si=>{"use strict";Object.defineProperty(si,"__esModule",{value:!0});si.getArgumentValues=Rp;si.getDirectiveValues=$b;si.getVariableValues=Kb;var cr=pe(),Mb=fn(),Gb=yu(),nn=Q(),Dp=ie(),Sp=ct(),lr=te(),Ub=vu(),Qb=Qt(),Lp=oi();function Kb(e,t,n,r){let i=[],a=r==null?void 0:r.maxErrors;try{let o=xb(e,t,n,s=>{if(a!=null&&i.length>=a)throw new nn.GraphQLError("Too many errors processing variables, error limit reached. Execution aborted.");i.push(s)});if(i.length===0)return{coerced:o}}catch(o){i.push(o)}return{errors:i}}function xb(e,t,n,r){let i={};for(let a of t){let o=a.variable.name.value,s=(0,Qb.typeFromAST)(e,a.type);if(!(0,lr.isInputType)(s)){let d=(0,Sp.print)(a.type);r(new nn.GraphQLError(`Variable "$${o}" expected value of type "${d}" which cannot be used as an input type.`,{nodes:a.type}));continue}if(!Ap(n,o)){if(a.defaultValue)i[o]=(0,Lp.valueFromAST)(a.defaultValue,s);else if((0,lr.isNonNullType)(s)){let d=(0,cr.inspect)(s);r(new nn.GraphQLError(`Variable "$${o}" of required type "${d}" was not provided.`,{nodes:a}))}continue}let c=n[o];if(c===null&&(0,lr.isNonNullType)(s)){let d=(0,cr.inspect)(s);r(new nn.GraphQLError(`Variable "$${o}" of non-null type "${d}" must not be null.`,{nodes:a}));continue}i[o]=(0,Ub.coerceInputValue)(c,s,(d,f,m)=>{let g=`Variable "$${o}" got invalid value `+(0,cr.inspect)(f);d.length>0&&(g+=` at "${o}${(0,Gb.printPathArray)(d)}"`),r(new nn.GraphQLError(g+"; "+m.message,{nodes:a,originalError:m}))})}return i}function Rp(e,t,n){var r;let i={},a=(r=t.arguments)!==null&&r!==void 0?r:[],o=(0,Mb.keyMap)(a,s=>s.name.value);for(let s of e.args){let c=s.name,d=s.type,f=o[c];if(!f){if(s.defaultValue!==void 0)i[c]=s.defaultValue;else if((0,lr.isNonNullType)(d))throw new nn.GraphQLError(`Argument "${c}" of required type "${(0,cr.inspect)(d)}" was not provided.`,{nodes:t});continue}let m=f.value,g=m.kind===Dp.Kind.NULL;if(m.kind===Dp.Kind.VARIABLE){let O=m.name.value;if(n==null||!Ap(n,O)){if(s.defaultValue!==void 0)i[c]=s.defaultValue;else if((0,lr.isNonNullType)(d))throw new nn.GraphQLError(`Argument "${c}" of required type "${(0,cr.inspect)(d)}" was provided the variable "$${O}" which was not provided a runtime value.`,{nodes:m});continue}g=n[O]==null}if(g&&(0,lr.isNonNullType)(d))throw new nn.GraphQLError(`Argument "${c}" of non-null type "${(0,cr.inspect)(d)}" must not be null.`,{nodes:m});let T=(0,Lp.valueFromAST)(m,d,n);if(T===void 0)throw new nn.GraphQLError(`Argument "${c}" has invalid value ${(0,Sp.print)(m)}.`,{nodes:m});i[c]=T}return i}function $b(e,t,n){var r;let i=(r=t.directives)===null||r===void 0?void 0:r.find(a=>a.name.value===e.name);if(i)return Rp(e,i,n)}function Ap(e,t){return Object.prototype.hasOwnProperty.call(e,t)}});var Na=_(_a=>{"use strict";Object.defineProperty(_a,"__esModule",{value:!0});_a.collectFields=Jb;_a.collectSubfields=Xb;var gu=ie(),Bb=te(),Pp=it(),Yb=Qt(),jp=dr();function Jb(e,t,n,r,i){let a=new Map;return Ea(e,t,n,r,i,a,new Set),a}function Xb(e,t,n,r,i){let a=new Map,o=new Set;for(let s of i)s.selectionSet&&Ea(e,t,n,r,s.selectionSet,a,o);return a}function Ea(e,t,n,r,i,a,o){for(let s of i.selections)switch(s.kind){case gu.Kind.FIELD:{if(!bu(n,s))continue;let c=Wb(s),d=a.get(c);d!==void 0?d.push(s):a.set(c,[s]);break}case gu.Kind.INLINE_FRAGMENT:{if(!bu(n,s)||!Fp(e,s,r))continue;Ea(e,t,n,r,s.selectionSet,a,o);break}case gu.Kind.FRAGMENT_SPREAD:{let c=s.name.value;if(o.has(c)||!bu(n,s))continue;o.add(c);let d=t[c];if(!d||!Fp(e,d,r))continue;Ea(e,t,n,r,d.selectionSet,a,o);break}}}function bu(e,t){let n=(0,jp.getDirectiveValues)(Pp.GraphQLSkipDirective,t,e);if((n==null?void 0:n.if)===!0)return!1;let r=(0,jp.getDirectiveValues)(Pp.GraphQLIncludeDirective,t,e);return(r==null?void 0:r.if)!==!1}function Fp(e,t,n){let r=t.typeCondition;if(!r)return!0;let i=(0,Yb.typeFromAST)(e,r);return i===n?!0:(0,Bb.isAbstractType)(i)?e.isSubType(i,n):!1}function Wb(e){return e.alias?e.alias.value:e.name.value}});var _u=_(Eu=>{"use strict";Object.defineProperty(Eu,"__esModule",{value:!0});Eu.SingleFieldSubscriptionsRule=Zb;var kp=Q(),Hb=ie(),zb=Na();function Zb(e){return{OperationDefinition(t){if(t.operation==="subscription"){let n=e.getSchema(),r=n.getSubscriptionType();if(r){let i=t.name?t.name.value:null,a=Object.create(null),o=e.getDocument(),s=Object.create(null);for(let d of o.definitions)d.kind===Hb.Kind.FRAGMENT_DEFINITION&&(s[d.name.value]=d);let c=(0,zb.collectFields)(n,s,a,r,t.selectionSet);if(c.size>1){let m=[...c.values()].slice(1).flat();e.reportError(new kp.GraphQLError(i!=null?`Subscription "${i}" must select only one top level field.`:"Anonymous Subscription must select only one top level field.",{nodes:m}))}for(let d of c.values())d[0].name.value.startsWith("__")&&e.reportError(new kp.GraphQLError(i!=null?`Subscription "${i}" must not select an introspection top level field.`:"Anonymous Subscription must not select an introspection top level field.",{nodes:d}))}}}}}});var Oa=_(Nu=>{"use strict";Object.defineProperty(Nu,"__esModule",{value:!0});Nu.groupBy=eE;function eE(e,t){let n=new Map;for(let r of e){let i=t(r),a=n.get(i);a===void 0?n.set(i,[r]):a.push(r)}return n}});var Iu=_(Ou=>{"use strict";Object.defineProperty(Ou,"__esModule",{value:!0});Ou.UniqueArgumentDefinitionNamesRule=rE;var tE=Oa(),nE=Q();function rE(e){return{DirectiveDefinition(r){var i;let a=(i=r.arguments)!==null&&i!==void 0?i:[];return n(`@${r.name.value}`,a)},InterfaceTypeDefinition:t,InterfaceTypeExtension:t,ObjectTypeDefinition:t,ObjectTypeExtension:t};function t(r){var i;let a=r.name.value,o=(i=r.fields)!==null&&i!==void 0?i:[];for(let c of o){var s;let d=c.name.value,f=(s=c.arguments)!==null&&s!==void 0?s:[];n(`${a}.${d}`,f)}return!1}function n(r,i){let a=(0,tE.groupBy)(i,o=>o.name.value);for(let[o,s]of a)s.length>1&&e.reportError(new nE.GraphQLError(`Argument "${r}(${o}:)" can only be defined once.`,{nodes:s.map(c=>c.name)}));return!1}}});var Su=_(Du=>{"use strict";Object.defineProperty(Du,"__esModule",{value:!0});Du.UniqueArgumentNamesRule=oE;var iE=Oa(),aE=Q();function oE(e){return{Field:t,Directive:t};function t(n){var r;let i=(r=n.arguments)!==null&&r!==void 0?r:[],a=(0,iE.groupBy)(i,o=>o.name.value);for(let[o,s]of a)s.length>1&&e.reportError(new aE.GraphQLError(`There can be only one argument named "${o}".`,{nodes:s.map(c=>c.name)}))}}});var Ru=_(Lu=>{"use strict";Object.defineProperty(Lu,"__esModule",{value:!0});Lu.UniqueDirectiveNamesRule=sE;var wp=Q();function sE(e){let t=Object.create(null),n=e.getSchema();return{DirectiveDefinition(r){let i=r.name.value;if(n!=null&&n.getDirective(i)){e.reportError(new wp.GraphQLError(`Directive "@${i}" already exists in the schema. It cannot be redefined.`,{nodes:r.name}));return}return t[i]?e.reportError(new wp.GraphQLError(`There can be only one directive named "@${i}".`,{nodes:[t[i],r.name]})):t[i]=r.name,!1}}}});var ju=_(Pu=>{"use strict";Object.defineProperty(Pu,"__esModule",{value:!0});Pu.UniqueDirectivesPerLocationRule=lE;var uE=Q(),Au=ie(),Cp=qn(),cE=it();function lE(e){let t=Object.create(null),n=e.getSchema(),r=n?n.getDirectives():cE.specifiedDirectives;for(let s of r)t[s.name]=!s.isRepeatable;let i=e.getDocument().definitions;for(let s of i)s.kind===Au.Kind.DIRECTIVE_DEFINITION&&(t[s.name.value]=!s.repeatable);let a=Object.create(null),o=Object.create(null);return{enter(s){if(!("directives"in s)||!s.directives)return;let c;if(s.kind===Au.Kind.SCHEMA_DEFINITION||s.kind===Au.Kind.SCHEMA_EXTENSION)c=a;else if((0,Cp.isTypeDefinitionNode)(s)||(0,Cp.isTypeExtensionNode)(s)){let d=s.name.value;c=o[d],c===void 0&&(o[d]=c=Object.create(null))}else c=Object.create(null);for(let d of s.directives){let f=d.name.value;t[f]&&(c[f]?e.reportError(new uE.GraphQLError(`The directive "@${f}" can only be used once at this location.`,{nodes:[c[f],d]})):c[f]=d)}}}}});var ku=_(Fu=>{"use strict";Object.defineProperty(Fu,"__esModule",{value:!0});Fu.UniqueEnumValueNamesRule=pE;var qp=Q(),dE=te();function pE(e){let t=e.getSchema(),n=t?t.getTypeMap():Object.create(null),r=Object.create(null);return{EnumTypeDefinition:i,EnumTypeExtension:i};function i(a){var o;let s=a.name.value;r[s]||(r[s]=Object.create(null));let c=(o=a.values)!==null&&o!==void 0?o:[],d=r[s];for(let f of c){let m=f.name.value,g=n[s];(0,dE.isEnumType)(g)&&g.getValue(m)?e.reportError(new qp.GraphQLError(`Enum value "${s}.${m}" already exists in the schema. It cannot also be defined in this type extension.`,{nodes:f.name})):d[m]?e.reportError(new qp.GraphQLError(`Enum value "${s}.${m}" can only be defined once.`,{nodes:[d[m],f.name]})):d[m]=f.name}return!1}}});var qu=_(Cu=>{"use strict";Object.defineProperty(Cu,"__esModule",{value:!0});Cu.UniqueFieldDefinitionNamesRule=fE;var Vp=Q(),wu=te();function fE(e){let t=e.getSchema(),n=t?t.getTypeMap():Object.create(null),r=Object.create(null);return{InputObjectTypeDefinition:i,InputObjectTypeExtension:i,InterfaceTypeDefinition:i,InterfaceTypeExtension:i,ObjectTypeDefinition:i,ObjectTypeExtension:i};function i(a){var o;let s=a.name.value;r[s]||(r[s]=Object.create(null));let c=(o=a.fields)!==null&&o!==void 0?o:[],d=r[s];for(let f of c){let m=f.name.value;mE(n[s],m)?e.reportError(new Vp.GraphQLError(`Field "${s}.${m}" already exists in the schema. It cannot also be defined in this type extension.`,{nodes:f.name})):d[m]?e.reportError(new Vp.GraphQLError(`Field "${s}.${m}" can only be defined once.`,{nodes:[d[m],f.name]})):d[m]=f.name}return!1}}function mE(e,t){return(0,wu.isObjectType)(e)||(0,wu.isInterfaceType)(e)||(0,wu.isInputObjectType)(e)?e.getFields()[t]!=null:!1}});var Mu=_(Vu=>{"use strict";Object.defineProperty(Vu,"__esModule",{value:!0});Vu.UniqueFragmentNamesRule=hE;var yE=Q();function hE(e){let t=Object.create(null);return{OperationDefinition:()=>!1,FragmentDefinition(n){let r=n.name.value;return t[r]?e.reportError(new yE.GraphQLError(`There can be only one fragment named "${r}".`,{nodes:[t[r],n.name]})):t[r]=n.name,!1}}}});var Uu=_(Gu=>{"use strict";Object.defineProperty(Gu,"__esModule",{value:!0});Gu.UniqueInputFieldNamesRule=gE;var vE=He(),TE=Q();function gE(e){let t=[],n=Object.create(null);return{ObjectValue:{enter(){t.push(n),n=Object.create(null)},leave(){let r=t.pop();r||(0,vE.invariant)(!1),n=r}},ObjectField(r){let i=r.name.value;n[i]?e.reportError(new TE.GraphQLError(`There can be only one input field named "${i}".`,{nodes:[n[i],r.name]})):n[i]=r.name}}}});var Ku=_(Qu=>{"use strict";Object.defineProperty(Qu,"__esModule",{value:!0});Qu.UniqueOperationNamesRule=EE;var bE=Q();function EE(e){let t=Object.create(null);return{OperationDefinition(n){let r=n.name;return r&&(t[r.value]?e.reportError(new bE.GraphQLError(`There can be only one operation named "${r.value}".`,{nodes:[t[r.value],r]})):t[r.value]=r),!1},FragmentDefinition:()=>!1}}});var $u=_(xu=>{"use strict";Object.defineProperty(xu,"__esModule",{value:!0});xu.UniqueOperationTypesRule=_E;var Mp=Q();function _E(e){let t=e.getSchema(),n=Object.create(null),r=t?{query:t.getQueryType(),mutation:t.getMutationType(),subscription:t.getSubscriptionType()}:{};return{SchemaDefinition:i,SchemaExtension:i};function i(a){var o;let s=(o=a.operationTypes)!==null&&o!==void 0?o:[];for(let c of s){let d=c.operation,f=n[d];r[d]?e.reportError(new Mp.GraphQLError(`Type for ${d} already defined in the schema. It cannot be redefined.`,{nodes:c})):f?e.reportError(new Mp.GraphQLError(`There can be only one ${d} type in schema.`,{nodes:[f,c]})):n[d]=c}return!1}}});var Yu=_(Bu=>{"use strict";Object.defineProperty(Bu,"__esModule",{value:!0});Bu.UniqueTypeNamesRule=NE;var Gp=Q();function NE(e){let t=Object.create(null),n=e.getSchema();return{ScalarTypeDefinition:r,ObjectTypeDefinition:r,InterfaceTypeDefinition:r,UnionTypeDefinition:r,EnumTypeDefinition:r,InputObjectTypeDefinition:r};function r(i){let a=i.name.value;if(n!=null&&n.getType(a)){e.reportError(new Gp.GraphQLError(`Type "${a}" already exists in the schema. It cannot also be defined in this type definition.`,{nodes:i.name}));return}return t[a]?e.reportError(new Gp.GraphQLError(`There can be only one type named "${a}".`,{nodes:[t[a],i.name]})):t[a]=i.name,!1}}});var Xu=_(Ju=>{"use strict";Object.defineProperty(Ju,"__esModule",{value:!0});Ju.UniqueVariableNamesRule=DE;var OE=Oa(),IE=Q();function DE(e){return{OperationDefinition(t){var n;let r=(n=t.variableDefinitions)!==null&&n!==void 0?n:[],i=(0,OE.groupBy)(r,a=>a.variable.name.value);for(let[a,o]of i)o.length>1&&e.reportError(new IE.GraphQLError(`There can be only one variable named "$${a}".`,{nodes:o.map(s=>s.variable.name)}))}}}});var Hu=_(Wu=>{"use strict";Object.defineProperty(Wu,"__esModule",{value:!0});Wu.ValuesOfCorrectTypeRule=AE;var SE=pn(),ui=pe(),LE=fn(),RE=mn(),Gn=Q(),Ia=ct(),Kt=te();function AE(e){return{ListValue(t){let n=(0,Kt.getNullableType)(e.getParentInputType());if(!(0,Kt.isListType)(n))return Un(e,t),!1},ObjectValue(t){let n=(0,Kt.getNamedType)(e.getInputType());if(!(0,Kt.isInputObjectType)(n))return Un(e,t),!1;let r=(0,LE.keyMap)(t.fields,i=>i.name.value);for(let i of Object.values(n.getFields()))if(!r[i.name]&&(0,Kt.isRequiredInputField)(i)){let o=(0,ui.inspect)(i.type);e.reportError(new Gn.GraphQLError(`Field "${n.name}.${i.name}" of required type "${o}" was not provided.`,{nodes:t}))}},ObjectField(t){let n=(0,Kt.getNamedType)(e.getParentInputType());if(!e.getInputType()&&(0,Kt.isInputObjectType)(n)){let i=(0,RE.suggestionList)(t.name.value,Object.keys(n.getFields()));e.reportError(new Gn.GraphQLError(`Field "${t.name.value}" is not defined by type "${n.name}".`+(0,SE.didYouMean)(i),{nodes:t}))}},NullValue(t){let n=e.getInputType();(0,Kt.isNonNullType)(n)&&e.reportError(new Gn.GraphQLError(`Expected value of type "${(0,ui.inspect)(n)}", found ${(0,Ia.print)(t)}.`,{nodes:t}))},EnumValue:t=>Un(e,t),IntValue:t=>Un(e,t),FloatValue:t=>Un(e,t),StringValue:t=>Un(e,t),BooleanValue:t=>Un(e,t)}}function Un(e,t){let n=e.getInputType();if(!n)return;let r=(0,Kt.getNamedType)(n);if(!(0,Kt.isLeafType)(r)){let i=(0,ui.inspect)(n);e.reportError(new Gn.GraphQLError(`Expected value of type "${i}", found ${(0,Ia.print)(t)}.`,{nodes:t}));return}try{if(r.parseLiteral(t,void 0)===void 0){let a=(0,ui.inspect)(n);e.reportError(new Gn.GraphQLError(`Expected value of type "${a}", found ${(0,Ia.print)(t)}.`,{nodes:t}))}}catch(i){let a=(0,ui.inspect)(n);i instanceof Gn.GraphQLError?e.reportError(i):e.reportError(new Gn.GraphQLError(`Expected value of type "${a}", found ${(0,Ia.print)(t)}; `+i.message,{nodes:t,originalError:i}))}}});var Zu=_(zu=>{"use strict";Object.defineProperty(zu,"__esModule",{value:!0});zu.VariablesAreInputTypesRule=wE;var PE=Q(),jE=ct(),FE=te(),kE=Qt();function wE(e){return{VariableDefinition(t){let n=(0,kE.typeFromAST)(e.getSchema(),t.type);if(n!==void 0&&!(0,FE.isInputType)(n)){let r=t.variable.name.value,i=(0,jE.print)(t.type);e.reportError(new PE.GraphQLError(`Variable "$${r}" cannot be non-input type "${i}".`,{nodes:t.type}))}}}}});var tc=_(ec=>{"use strict";Object.defineProperty(ec,"__esModule",{value:!0});ec.VariablesInAllowedPositionRule=ME;var Up=pe(),CE=Q(),qE=ie(),Qp=te(),Kp=Qr(),VE=Qt();function ME(e){let t=Object.create(null);return{OperationDefinition:{enter(){t=Object.create(null)},leave(n){let r=e.getRecursiveVariableUsages(n);for(let{node:i,type:a,defaultValue:o}of r){let s=i.name.value,c=t[s];if(c&&a){let d=e.getSchema(),f=(0,VE.typeFromAST)(d,c.type);if(f&&!GE(d,f,c.defaultValue,a,o)){let m=(0,Up.inspect)(f),g=(0,Up.inspect)(a);e.reportError(new CE.GraphQLError(`Variable "$${s}" of type "${m}" used in position expecting type "${g}".`,{nodes:[c,i]}))}}}}},VariableDefinition(n){t[n.variable.name.value]=n}}}function GE(e,t,n,r,i){if((0,Qp.isNonNullType)(r)&&!(0,Qp.isNonNullType)(t)){if(!(n!=null&&n.kind!==qE.Kind.NULL)&&!(i!==void 0))return!1;let s=r.ofType;return(0,Kp.isTypeSubTypeOf)(e,t,s)}return(0,Kp.isTypeSubTypeOf)(e,t,r)}});var nc=_(pr=>{"use strict";Object.defineProperty(pr,"__esModule",{value:!0});pr.specifiedSDLRules=pr.specifiedRules=void 0;var UE=Os(),QE=Ds(),KE=Ls(),xp=Rs(),$p=Fs(),xE=ws(),Bp=Vs(),$E=Gs(),BE=Qs(),YE=xs(),JE=Bs(),XE=Js(),WE=Ws(),HE=iu(),zE=su(),ZE=cu(),Yp=du(),e_=fu(),t_=_u(),n_=Iu(),Jp=Su(),r_=Ru(),Xp=ju(),i_=ku(),a_=qu(),o_=Mu(),Wp=Uu(),s_=Ku(),u_=$u(),c_=Yu(),l_=Xu(),d_=Hu(),p_=Zu(),f_=tc(),m_=Object.freeze([UE.ExecutableDefinitionsRule,s_.UniqueOperationNamesRule,$E.LoneAnonymousOperationRule,t_.SingleFieldSubscriptionsRule,Bp.KnownTypeNamesRule,KE.FragmentsOnCompositeTypesRule,p_.VariablesAreInputTypesRule,e_.ScalarLeafsRule,QE.FieldsOnCorrectTypeRule,o_.UniqueFragmentNamesRule,xE.KnownFragmentNamesRule,XE.NoUnusedFragmentsRule,zE.PossibleFragmentSpreadsRule,YE.NoFragmentCyclesRule,l_.UniqueVariableNamesRule,JE.NoUndefinedVariablesRule,WE.NoUnusedVariablesRule,$p.KnownDirectivesRule,Xp.UniqueDirectivesPerLocationRule,xp.KnownArgumentNamesRule,Jp.UniqueArgumentNamesRule,d_.ValuesOfCorrectTypeRule,Yp.ProvidedRequiredArgumentsRule,f_.VariablesInAllowedPositionRule,HE.OverlappingFieldsCanBeMergedRule,Wp.UniqueInputFieldNamesRule]);pr.specifiedRules=m_;var y_=Object.freeze([BE.LoneSchemaDefinitionRule,u_.UniqueOperationTypesRule,c_.UniqueTypeNamesRule,i_.UniqueEnumValueNamesRule,a_.UniqueFieldDefinitionNamesRule,n_.UniqueArgumentDefinitionNamesRule,r_.UniqueDirectiveNamesRule,Bp.KnownTypeNamesRule,$p.KnownDirectivesRule,Xp.UniqueDirectivesPerLocationRule,ZE.PossibleTypeExtensionsRule,xp.KnownArgumentNamesOnDirectivesRule,Jp.UniqueArgumentNamesRule,Wp.UniqueInputFieldNamesRule,Yp.ProvidedRequiredArgumentsOnDirectivesRule]);pr.specifiedSDLRules=y_});var rc=_(Tn=>{"use strict";Object.defineProperty(Tn,"__esModule",{value:!0});Tn.ValidationContext=Tn.SDLValidationContext=Tn.ASTValidationContext=void 0;var Hp=ie(),h_=Ln(),zp=pa(),Da=class{constructor(t,n){this._ast=t,this._fragments=void 0,this._fragmentSpreads=new Map,this._recursivelyReferencedFragments=new Map,this._onError=n}get[Symbol.toStringTag](){return"ASTValidationContext"}reportError(t){this._onError(t)}getDocument(){return this._ast}getFragment(t){let n;if(this._fragments)n=this._fragments;else{n=Object.create(null);for(let r of this.getDocument().definitions)r.kind===Hp.Kind.FRAGMENT_DEFINITION&&(n[r.name.value]=r);this._fragments=n}return n[t]}getFragmentSpreads(t){let n=this._fragmentSpreads.get(t);if(!n){n=[];let r=[t],i;for(;i=r.pop();)for(let a of i.selections)a.kind===Hp.Kind.FRAGMENT_SPREAD?n.push(a):a.selectionSet&&r.push(a.selectionSet);this._fragmentSpreads.set(t,n)}return n}getRecursivelyReferencedFragments(t){let n=this._recursivelyReferencedFragments.get(t);if(!n){n=[];let r=Object.create(null),i=[t.selectionSet],a;for(;a=i.pop();)for(let o of this.getFragmentSpreads(a)){let s=o.name.value;if(r[s]!==!0){r[s]=!0;let c=this.getFragment(s);c&&(n.push(c),i.push(c.selectionSet))}}this._recursivelyReferencedFragments.set(t,n)}return n}};Tn.ASTValidationContext=Da;var Zp=class extends Da{constructor(t,n,r){super(t,r);this._schema=n}get[Symbol.toStringTag](){return"SDLValidationContext"}getSchema(){return this._schema}};Tn.SDLValidationContext=Zp;var ef=class extends Da{constructor(t,n,r,i){super(n,i);this._schema=t,this._typeInfo=r,this._variableUsages=new Map,this._recursiveVariableUsages=new Map}get[Symbol.toStringTag](){return"ValidationContext"}getSchema(){return this._schema}getVariableUsages(t){let n=this._variableUsages.get(t);if(!n){let r=[],i=new zp.TypeInfo(this._schema);(0,h_.visit)(t,(0,zp.visitWithTypeInfo)(i,{VariableDefinition:()=>!1,Variable(a){r.push({node:a,type:i.getInputType(),defaultValue:i.getDefaultValue()})}})),n=r,this._variableUsages.set(t,n)}return n}getRecursiveVariableUsages(t){let n=this._recursiveVariableUsages.get(t);if(!n){n=this.getVariableUsages(t);for(let r of this.getRecursivelyReferencedFragments(t))n=n.concat(this.getVariableUsages(r));this._recursiveVariableUsages.set(t,n)}return n}getType(){return this._typeInfo.getType()}getParentType(){return this._typeInfo.getParentType()}getInputType(){return this._typeInfo.getInputType()}getParentInputType(){return this._typeInfo.getParentInputType()}getFieldDef(){return this._typeInfo.getFieldDef()}getDirective(){return this._typeInfo.getDirective()}getArgument(){return this._typeInfo.getArgument()}getEnumValue(){return this._typeInfo.getEnumValue()}};Tn.ValidationContext=ef});var ci=_(fr=>{"use strict";Object.defineProperty(fr,"__esModule",{value:!0});fr.assertValidSDL=E_;fr.assertValidSDLExtension=__;fr.validate=b_;fr.validateSDL=ic;var v_=et(),T_=Q(),Sa=Ln(),g_=zr(),tf=pa(),nf=nc(),rf=rc();function b_(e,t,n=nf.specifiedRules,r,i=new tf.TypeInfo(e)){var a;let o=(a=r==null?void 0:r.maxErrors)!==null&&a!==void 0?a:100;t||(0,v_.devAssert)(!1,"Must provide document."),(0,g_.assertValidSchema)(e);let s=Object.freeze({}),c=[],d=new rf.ValidationContext(e,t,i,m=>{if(c.length>=o)throw c.push(new T_.GraphQLError("Too many validation errors, error limit reached. Validation aborted.")),s;c.push(m)}),f=(0,Sa.visitInParallel)(n.map(m=>m(d)));try{(0,Sa.visit)(t,(0,tf.visitWithTypeInfo)(i,f))}catch(m){if(m!==s)throw m}return c}function ic(e,t,n=nf.specifiedSDLRules){let r=[],i=new rf.SDLValidationContext(e,t,o=>{r.push(o)}),a=n.map(o=>o(i));return(0,Sa.visit)(e,(0,Sa.visitInParallel)(a)),r}function E_(e){let t=ic(e);if(t.length!==0)throw new Error(t.map(n=>n.message).join(`

`))}function __(e,t){let n=ic(e,t);if(n.length!==0)throw new Error(n.map(r=>r.message).join(`

`))}});var af=_(ac=>{"use strict";Object.defineProperty(ac,"__esModule",{value:!0});ac.memoize3=N_;function N_(e){let t;return function(r,i,a){t===void 0&&(t=new WeakMap);let o=t.get(r);o===void 0&&(o=new WeakMap,t.set(r,o));let s=o.get(i);s===void 0&&(s=new WeakMap,o.set(i,s));let c=s.get(a);return c===void 0&&(c=e(r,i,a),s.set(a,c)),c}}});var of=_(oc=>{"use strict";Object.defineProperty(oc,"__esModule",{value:!0});oc.promiseForObject=O_;function O_(e){return Promise.all(Object.values(e)).then(t=>{let n=Object.create(null);for(let[r,i]of Object.keys(e).entries())n[i]=t[r];return n})}});var sf=_(sc=>{"use strict";Object.defineProperty(sc,"__esModule",{value:!0});sc.promiseReduce=D_;var I_=qi();function D_(e,t,n){let r=n;for(let i of e)r=(0,I_.isPromise)(r)?r.then(a=>t(a,i)):t(r,i);return r}});var cf=_(uc=>{"use strict";Object.defineProperty(uc,"__esModule",{value:!0});uc.toError=L_;var S_=pe();function L_(e){return e instanceof Error?e:new uf(e)}var uf=class extends Error{constructor(t){super("Unexpected error value: "+(0,S_.inspect)(t));this.name="NonErrorThrown",this.thrownValue=t}}});var La=_(cc=>{"use strict";Object.defineProperty(cc,"__esModule",{value:!0});cc.locatedError=P_;var R_=cf(),A_=Q();function P_(e,t,n){var r;let i=(0,R_.toError)(e);return j_(i)?i:new A_.GraphQLError(i.message,{nodes:(r=i.nodes)!==null&&r!==void 0?r:t,source:i.source,positions:i.positions,path:n,originalError:i})}function j_(e){return Array.isArray(e.path)}});var di=_(gt=>{"use strict";Object.defineProperty(gt,"__esModule",{value:!0});gt.assertValidExecutionArguments=yf;gt.buildExecutionContext=hf;gt.buildResolveInfo=Tf;gt.defaultTypeResolver=gt.defaultFieldResolver=void 0;gt.execute=mf;gt.executeSync=M_;gt.getFieldDef=_f;var lc=et(),Qn=pe(),F_=He(),k_=ca(),dc=qt(),wt=qi(),w_=af(),Kn=ni(),lf=of(),C_=sf(),Tt=Q(),Ra=La(),pc=Vt(),df=ie(),gn=te(),mr=lt(),q_=zr(),pf=Na(),ff=dr(),V_=(0,w_.memoize3)((e,t,n)=>(0,pf.collectSubfields)(e.schema,e.fragments,e.variableValues,t,n));function mf(e){arguments.length<2||(0,lc.devAssert)(!1,"graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.");let{schema:t,document:n,variableValues:r,rootValue:i}=e;yf(t,n,r);let a=hf(e);if(!("schema"in a))return{errors:a};try{let{operation:o}=a,s=G_(a,o,i);return(0,wt.isPromise)(s)?s.then(c=>Aa(c,a.errors),c=>(a.errors.push(c),Aa(null,a.errors))):Aa(s,a.errors)}catch(o){return a.errors.push(o),Aa(null,a.errors)}}function M_(e){let t=mf(e);if((0,wt.isPromise)(t))throw new Error("GraphQL execution failed to complete synchronously.");return t}function Aa(e,t){return t.length===0?{data:e}:{errors:t,data:e}}function yf(e,t,n){t||(0,lc.devAssert)(!1,"Must provide document."),(0,q_.assertValidSchema)(e),n==null||(0,dc.isObjectLike)(n)||(0,lc.devAssert)(!1,"Variables must be provided as an Object where each property is a variable value. Perhaps look to see if an unparsed JSON string was provided.")}function hf(e){var t,n;let{schema:r,document:i,rootValue:a,contextValue:o,variableValues:s,operationName:c,fieldResolver:d,typeResolver:f,subscribeFieldResolver:m}=e,g,T=Object.create(null);for(let k of i.definitions)switch(k.kind){case df.Kind.OPERATION_DEFINITION:if(c==null){if(g!==void 0)return[new Tt.GraphQLError("Must provide operation name if query contains multiple operations.")];g=k}else((t=k.name)===null||t===void 0?void 0:t.value)===c&&(g=k);break;case df.Kind.FRAGMENT_DEFINITION:T[k.name.value]=k;break;default:}if(!g)return c!=null?[new Tt.GraphQLError(`Unknown operation named "${c}".`)]:[new Tt.GraphQLError("Must provide an operation.")];let O=(n=g.variableDefinitions)!==null&&n!==void 0?n:[],U=(0,ff.getVariableValues)(r,O,s!=null?s:{},{maxErrors:50});return U.errors?U.errors:{schema:r,fragments:T,rootValue:a,contextValue:o,operation:g,variableValues:U.coerced,fieldResolver:d!=null?d:mc,typeResolver:f!=null?f:Ef,subscribeFieldResolver:m!=null?m:mc,errors:[]}}function G_(e,t,n){let r=e.schema.getRootType(t.operation);if(r==null)throw new Tt.GraphQLError(`Schema is not configured to execute ${t.operation} operation.`,{nodes:t});let i=(0,pf.collectFields)(e.schema,e.fragments,e.variableValues,r,t.selectionSet),a=void 0;switch(t.operation){case pc.OperationTypeNode.QUERY:return Pa(e,r,n,a,i);case pc.OperationTypeNode.MUTATION:return U_(e,r,n,a,i);case pc.OperationTypeNode.SUBSCRIPTION:return Pa(e,r,n,a,i)}}function U_(e,t,n,r,i){return(0,C_.promiseReduce)(i.entries(),(a,[o,s])=>{let c=(0,Kn.addPath)(r,o,t.name),d=vf(e,t,n,s,c);return d===void 0?a:(0,wt.isPromise)(d)?d.then(f=>(a[o]=f,a)):(a[o]=d,a)},Object.create(null))}function Pa(e,t,n,r,i){let a=Object.create(null),o=!1;try{for(let[s,c]of i.entries()){let d=(0,Kn.addPath)(r,s,t.name),f=vf(e,t,n,c,d);f!==void 0&&(a[s]=f,(0,wt.isPromise)(f)&&(o=!0))}}catch(s){if(o)return(0,lf.promiseForObject)(a).finally(()=>{throw s});throw s}return o?(0,lf.promiseForObject)(a):a}function vf(e,t,n,r,i){var a;let o=_f(e.schema,t,r[0]);if(!o)return;let s=o.type,c=(a=o.resolve)!==null&&a!==void 0?a:e.fieldResolver,d=Tf(e,o,r,t,i);try{let f=(0,ff.getArgumentValues)(o,r[0],e.variableValues),m=e.contextValue,g=c(n,f,m,d),T;return(0,wt.isPromise)(g)?T=g.then(O=>li(e,s,r,d,i,O)):T=li(e,s,r,d,i,g),(0,wt.isPromise)(T)?T.then(void 0,O=>{let U=(0,Ra.locatedError)(O,r,(0,Kn.pathToArray)(i));return ja(U,s,e)}):T}catch(f){let m=(0,Ra.locatedError)(f,r,(0,Kn.pathToArray)(i));return ja(m,s,e)}}function Tf(e,t,n,r,i){return{fieldName:t.name,fieldNodes:n,returnType:t.type,parentType:r,path:i,schema:e.schema,fragments:e.fragments,rootValue:e.rootValue,operation:e.operation,variableValues:e.variableValues}}function ja(e,t,n){if((0,gn.isNonNullType)(t))throw e;return n.errors.push(e),null}function li(e,t,n,r,i,a){if(a instanceof Error)throw a;if((0,gn.isNonNullType)(t)){let o=li(e,t.ofType,n,r,i,a);if(o===null)throw new Error(`Cannot return null for non-nullable field ${r.parentType.name}.${r.fieldName}.`);return o}if(a==null)return null;if((0,gn.isListType)(t))return Q_(e,t,n,r,i,a);if((0,gn.isLeafType)(t))return K_(t,a);if((0,gn.isAbstractType)(t))return x_(e,t,n,r,i,a);if((0,gn.isObjectType)(t))return fc(e,t,n,r,i,a);(0,F_.invariant)(!1,"Cannot complete value of unexpected output type: "+(0,Qn.inspect)(t))}function Q_(e,t,n,r,i,a){if(!(0,k_.isIterableObject)(a))throw new Tt.GraphQLError(`Expected Iterable, but did not find one for field "${r.parentType.name}.${r.fieldName}".`);let o=t.ofType,s=!1,c=Array.from(a,(d,f)=>{let m=(0,Kn.addPath)(i,f,void 0);try{let g;return(0,wt.isPromise)(d)?g=d.then(T=>li(e,o,n,r,m,T)):g=li(e,o,n,r,m,d),(0,wt.isPromise)(g)?(s=!0,g.then(void 0,T=>{let O=(0,Ra.locatedError)(T,n,(0,Kn.pathToArray)(m));return ja(O,o,e)})):g}catch(g){let T=(0,Ra.locatedError)(g,n,(0,Kn.pathToArray)(m));return ja(T,o,e)}});return s?Promise.all(c):c}function K_(e,t){let n=e.serialize(t);if(n==null)throw new Error(`Expected \`${(0,Qn.inspect)(e)}.serialize(${(0,Qn.inspect)(t)})\` to return non-nullable value, returned: ${(0,Qn.inspect)(n)}`);return n}function x_(e,t,n,r,i,a){var o;let s=(o=t.resolveType)!==null&&o!==void 0?o:e.typeResolver,c=e.contextValue,d=s(a,c,r,t);return(0,wt.isPromise)(d)?d.then(f=>fc(e,gf(f,e,t,n,r,a),n,r,i,a)):fc(e,gf(d,e,t,n,r,a),n,r,i,a)}function gf(e,t,n,r,i,a){if(e==null)throw new Tt.GraphQLError(`Abstract type "${n.name}" must resolve to an Object type at runtime for field "${i.parentType.name}.${i.fieldName}". Either the "${n.name}" type should provide a "resolveType" function or each possible type should provide an "isTypeOf" function.`,r);if((0,gn.isObjectType)(e))throw new Tt.GraphQLError("Support for returning GraphQLObjectType from resolveType was removed in graphql-js@16.0.0 please return type name instead.");if(typeof e!="string")throw new Tt.GraphQLError(`Abstract type "${n.name}" must resolve to an Object type at runtime for field "${i.parentType.name}.${i.fieldName}" with value ${(0,Qn.inspect)(a)}, received "${(0,Qn.inspect)(e)}".`);let o=t.schema.getType(e);if(o==null)throw new Tt.GraphQLError(`Abstract type "${n.name}" was resolved to a type "${e}" that does not exist inside the schema.`,{nodes:r});if(!(0,gn.isObjectType)(o))throw new Tt.GraphQLError(`Abstract type "${n.name}" was resolved to a non-object type "${e}".`,{nodes:r});if(!t.schema.isSubType(n,o))throw new Tt.GraphQLError(`Runtime Object type "${o.name}" is not a possible type for "${n.name}".`,{nodes:r});return o}function fc(e,t,n,r,i,a){let o=V_(e,t,n);if(t.isTypeOf){let s=t.isTypeOf(a,e.contextValue,r);if((0,wt.isPromise)(s))return s.then(c=>{if(!c)throw bf(t,a,n);return Pa(e,t,a,i,o)});if(!s)throw bf(t,a,n)}return Pa(e,t,a,i,o)}function bf(e,t,n){return new Tt.GraphQLError(`Expected value of type "${e.name}" but got: ${(0,Qn.inspect)(t)}.`,{nodes:n})}var Ef=function(e,t,n,r){if((0,dc.isObjectLike)(e)&&typeof e.__typename=="string")return e.__typename;let i=n.schema.getPossibleTypes(r),a=[];for(let o=0;o<i.length;o++){let s=i[o];if(s.isTypeOf){let c=s.isTypeOf(e,t,n);if((0,wt.isPromise)(c))a[o]=c;else if(c)return s.name}}if(a.length)return Promise.all(a).then(o=>{for(let s=0;s<o.length;s++)if(o[s])return i[s].name})};gt.defaultTypeResolver=Ef;var mc=function(e,t,n,r){if((0,dc.isObjectLike)(e)||typeof e=="function"){let i=e[r.fieldName];return typeof i=="function"?e[r.fieldName](t,n,r):i}};gt.defaultFieldResolver=mc;function _f(e,t,n){let r=n.name.value;return r===mr.SchemaMetaFieldDef.name&&e.getQueryType()===t?mr.SchemaMetaFieldDef:r===mr.TypeMetaFieldDef.name&&e.getQueryType()===t?mr.TypeMetaFieldDef:r===mr.TypeNameMetaFieldDef.name?mr.TypeNameMetaFieldDef:t.getFields()[r]}});var Of=_(Fa=>{"use strict";Object.defineProperty(Fa,"__esModule",{value:!0});Fa.graphql=H_;Fa.graphqlSync=z_;var $_=et(),B_=qi(),Y_=tr(),J_=zr(),X_=ci(),W_=di();function H_(e){return new Promise(t=>t(Nf(e)))}function z_(e){let t=Nf(e);if((0,B_.isPromise)(t))throw new Error("GraphQL execution failed to complete synchronously.");return t}function Nf(e){arguments.length<2||(0,$_.devAssert)(!1,"graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.");let{schema:t,source:n,rootValue:r,contextValue:i,variableValues:a,operationName:o,fieldResolver:s,typeResolver:c}=e,d=(0,J_.validateSchema)(t);if(d.length>0)return{errors:d};let f;try{f=(0,Y_.parse)(n)}catch(g){return{errors:[g]}}let m=(0,X_.validate)(t,f);return m.length>0?{errors:m}:(0,W_.execute)({schema:t,document:f,rootValue:r,contextValue:i,variableValues:a,operationName:o,fieldResolver:s,typeResolver:c})}});var Sf=_(P=>{"use strict";Object.defineProperty(P,"__esModule",{value:!0});Object.defineProperty(P,"DEFAULT_DEPRECATION_REASON",{enumerable:!0,get:function(){return xt.DEFAULT_DEPRECATION_REASON}});Object.defineProperty(P,"GRAPHQL_MAX_INT",{enumerable:!0,get:function(){return rn.GRAPHQL_MAX_INT}});Object.defineProperty(P,"GRAPHQL_MIN_INT",{enumerable:!0,get:function(){return rn.GRAPHQL_MIN_INT}});Object.defineProperty(P,"GraphQLBoolean",{enumerable:!0,get:function(){return rn.GraphQLBoolean}});Object.defineProperty(P,"GraphQLDeprecatedDirective",{enumerable:!0,get:function(){return xt.GraphQLDeprecatedDirective}});Object.defineProperty(P,"GraphQLDirective",{enumerable:!0,get:function(){return xt.GraphQLDirective}});Object.defineProperty(P,"GraphQLEnumType",{enumerable:!0,get:function(){return x.GraphQLEnumType}});Object.defineProperty(P,"GraphQLFloat",{enumerable:!0,get:function(){return rn.GraphQLFloat}});Object.defineProperty(P,"GraphQLID",{enumerable:!0,get:function(){return rn.GraphQLID}});Object.defineProperty(P,"GraphQLIncludeDirective",{enumerable:!0,get:function(){return xt.GraphQLIncludeDirective}});Object.defineProperty(P,"GraphQLInputObjectType",{enumerable:!0,get:function(){return x.GraphQLInputObjectType}});Object.defineProperty(P,"GraphQLInt",{enumerable:!0,get:function(){return rn.GraphQLInt}});Object.defineProperty(P,"GraphQLInterfaceType",{enumerable:!0,get:function(){return x.GraphQLInterfaceType}});Object.defineProperty(P,"GraphQLList",{enumerable:!0,get:function(){return x.GraphQLList}});Object.defineProperty(P,"GraphQLNonNull",{enumerable:!0,get:function(){return x.GraphQLNonNull}});Object.defineProperty(P,"GraphQLObjectType",{enumerable:!0,get:function(){return x.GraphQLObjectType}});Object.defineProperty(P,"GraphQLScalarType",{enumerable:!0,get:function(){return x.GraphQLScalarType}});Object.defineProperty(P,"GraphQLSchema",{enumerable:!0,get:function(){return yc.GraphQLSchema}});Object.defineProperty(P,"GraphQLSkipDirective",{enumerable:!0,get:function(){return xt.GraphQLSkipDirective}});Object.defineProperty(P,"GraphQLSpecifiedByDirective",{enumerable:!0,get:function(){return xt.GraphQLSpecifiedByDirective}});Object.defineProperty(P,"GraphQLString",{enumerable:!0,get:function(){return rn.GraphQLString}});Object.defineProperty(P,"GraphQLUnionType",{enumerable:!0,get:function(){return x.GraphQLUnionType}});Object.defineProperty(P,"SchemaMetaFieldDef",{enumerable:!0,get:function(){return ot.SchemaMetaFieldDef}});Object.defineProperty(P,"TypeKind",{enumerable:!0,get:function(){return ot.TypeKind}});Object.defineProperty(P,"TypeMetaFieldDef",{enumerable:!0,get:function(){return ot.TypeMetaFieldDef}});Object.defineProperty(P,"TypeNameMetaFieldDef",{enumerable:!0,get:function(){return ot.TypeNameMetaFieldDef}});Object.defineProperty(P,"__Directive",{enumerable:!0,get:function(){return ot.__Directive}});Object.defineProperty(P,"__DirectiveLocation",{enumerable:!0,get:function(){return ot.__DirectiveLocation}});Object.defineProperty(P,"__EnumValue",{enumerable:!0,get:function(){return ot.__EnumValue}});Object.defineProperty(P,"__Field",{enumerable:!0,get:function(){return ot.__Field}});Object.defineProperty(P,"__InputValue",{enumerable:!0,get:function(){return ot.__InputValue}});Object.defineProperty(P,"__Schema",{enumerable:!0,get:function(){return ot.__Schema}});Object.defineProperty(P,"__Type",{enumerable:!0,get:function(){return ot.__Type}});Object.defineProperty(P,"__TypeKind",{enumerable:!0,get:function(){return ot.__TypeKind}});Object.defineProperty(P,"assertAbstractType",{enumerable:!0,get:function(){return x.assertAbstractType}});Object.defineProperty(P,"assertCompositeType",{enumerable:!0,get:function(){return x.assertCompositeType}});Object.defineProperty(P,"assertDirective",{enumerable:!0,get:function(){return xt.assertDirective}});Object.defineProperty(P,"assertEnumType",{enumerable:!0,get:function(){return x.assertEnumType}});Object.defineProperty(P,"assertEnumValueName",{enumerable:!0,get:function(){return Df.assertEnumValueName}});Object.defineProperty(P,"assertInputObjectType",{enumerable:!0,get:function(){return x.assertInputObjectType}});Object.defineProperty(P,"assertInputType",{enumerable:!0,get:function(){return x.assertInputType}});Object.defineProperty(P,"assertInterfaceType",{enumerable:!0,get:function(){return x.assertInterfaceType}});Object.defineProperty(P,"assertLeafType",{enumerable:!0,get:function(){return x.assertLeafType}});Object.defineProperty(P,"assertListType",{enumerable:!0,get:function(){return x.assertListType}});Object.defineProperty(P,"assertName",{enumerable:!0,get:function(){return Df.assertName}});Object.defineProperty(P,"assertNamedType",{enumerable:!0,get:function(){return x.assertNamedType}});Object.defineProperty(P,"assertNonNullType",{enumerable:!0,get:function(){return x.assertNonNullType}});Object.defineProperty(P,"assertNullableType",{enumerable:!0,get:function(){return x.assertNullableType}});Object.defineProperty(P,"assertObjectType",{enumerable:!0,get:function(){return x.assertObjectType}});Object.defineProperty(P,"assertOutputType",{enumerable:!0,get:function(){return x.assertOutputType}});Object.defineProperty(P,"assertScalarType",{enumerable:!0,get:function(){return x.assertScalarType}});Object.defineProperty(P,"assertSchema",{enumerable:!0,get:function(){return yc.assertSchema}});Object.defineProperty(P,"assertType",{enumerable:!0,get:function(){return x.assertType}});Object.defineProperty(P,"assertUnionType",{enumerable:!0,get:function(){return x.assertUnionType}});Object.defineProperty(P,"assertValidSchema",{enumerable:!0,get:function(){return If.assertValidSchema}});Object.defineProperty(P,"assertWrappingType",{enumerable:!0,get:function(){return x.assertWrappingType}});Object.defineProperty(P,"getNamedType",{enumerable:!0,get:function(){return x.getNamedType}});Object.defineProperty(P,"getNullableType",{enumerable:!0,get:function(){return x.getNullableType}});Object.defineProperty(P,"introspectionTypes",{enumerable:!0,get:function(){return ot.introspectionTypes}});Object.defineProperty(P,"isAbstractType",{enumerable:!0,get:function(){return x.isAbstractType}});Object.defineProperty(P,"isCompositeType",{enumerable:!0,get:function(){return x.isCompositeType}});Object.defineProperty(P,"isDirective",{enumerable:!0,get:function(){return xt.isDirective}});Object.defineProperty(P,"isEnumType",{enumerable:!0,get:function(){return x.isEnumType}});Object.defineProperty(P,"isInputObjectType",{enumerable:!0,get:function(){return x.isInputObjectType}});Object.defineProperty(P,"isInputType",{enumerable:!0,get:function(){return x.isInputType}});Object.defineProperty(P,"isInterfaceType",{enumerable:!0,get:function(){return x.isInterfaceType}});Object.defineProperty(P,"isIntrospectionType",{enumerable:!0,get:function(){return ot.isIntrospectionType}});Object.defineProperty(P,"isLeafType",{enumerable:!0,get:function(){return x.isLeafType}});Object.defineProperty(P,"isListType",{enumerable:!0,get:function(){return x.isListType}});Object.defineProperty(P,"isNamedType",{enumerable:!0,get:function(){return x.isNamedType}});Object.defineProperty(P,"isNonNullType",{enumerable:!0,get:function(){return x.isNonNullType}});Object.defineProperty(P,"isNullableType",{enumerable:!0,get:function(){return x.isNullableType}});Object.defineProperty(P,"isObjectType",{enumerable:!0,get:function(){return x.isObjectType}});Object.defineProperty(P,"isOutputType",{enumerable:!0,get:function(){return x.isOutputType}});Object.defineProperty(P,"isRequiredArgument",{enumerable:!0,get:function(){return x.isRequiredArgument}});Object.defineProperty(P,"isRequiredInputField",{enumerable:!0,get:function(){return x.isRequiredInputField}});Object.defineProperty(P,"isScalarType",{enumerable:!0,get:function(){return x.isScalarType}});Object.defineProperty(P,"isSchema",{enumerable:!0,get:function(){return yc.isSchema}});Object.defineProperty(P,"isSpecifiedDirective",{enumerable:!0,get:function(){return xt.isSpecifiedDirective}});Object.defineProperty(P,"isSpecifiedScalarType",{enumerable:!0,get:function(){return rn.isSpecifiedScalarType}});Object.defineProperty(P,"isType",{enumerable:!0,get:function(){return x.isType}});Object.defineProperty(P,"isUnionType",{enumerable:!0,get:function(){return x.isUnionType}});Object.defineProperty(P,"isWrappingType",{enumerable:!0,get:function(){return x.isWrappingType}});Object.defineProperty(P,"resolveObjMapThunk",{enumerable:!0,get:function(){return x.resolveObjMapThunk}});Object.defineProperty(P,"resolveReadonlyArrayThunk",{enumerable:!0,get:function(){return x.resolveReadonlyArrayThunk}});Object.defineProperty(P,"specifiedDirectives",{enumerable:!0,get:function(){return xt.specifiedDirectives}});Object.defineProperty(P,"specifiedScalarTypes",{enumerable:!0,get:function(){return rn.specifiedScalarTypes}});Object.defineProperty(P,"validateSchema",{enumerable:!0,get:function(){return If.validateSchema}});var yc=wn(),x=te(),xt=it(),rn=Ut(),ot=lt(),If=zr(),Df=qr()});var Rf=_(ae=>{"use strict";Object.defineProperty(ae,"__esModule",{value:!0});Object.defineProperty(ae,"BREAK",{enumerable:!0,get:function(){return pi.BREAK}});Object.defineProperty(ae,"DirectiveLocation",{enumerable:!0,get:function(){return a0.DirectiveLocation}});Object.defineProperty(ae,"Kind",{enumerable:!0,get:function(){return t0.Kind}});Object.defineProperty(ae,"Lexer",{enumerable:!0,get:function(){return r0.Lexer}});Object.defineProperty(ae,"Location",{enumerable:!0,get:function(){return hc.Location}});Object.defineProperty(ae,"OperationTypeNode",{enumerable:!0,get:function(){return hc.OperationTypeNode}});Object.defineProperty(ae,"Source",{enumerable:!0,get:function(){return Z_.Source}});Object.defineProperty(ae,"Token",{enumerable:!0,get:function(){return hc.Token}});Object.defineProperty(ae,"TokenKind",{enumerable:!0,get:function(){return n0.TokenKind}});Object.defineProperty(ae,"getEnterLeaveForKind",{enumerable:!0,get:function(){return pi.getEnterLeaveForKind}});Object.defineProperty(ae,"getLocation",{enumerable:!0,get:function(){return e0.getLocation}});Object.defineProperty(ae,"getVisitFn",{enumerable:!0,get:function(){return pi.getVisitFn}});Object.defineProperty(ae,"isConstValueNode",{enumerable:!0,get:function(){return $t.isConstValueNode}});Object.defineProperty(ae,"isDefinitionNode",{enumerable:!0,get:function(){return $t.isDefinitionNode}});Object.defineProperty(ae,"isExecutableDefinitionNode",{enumerable:!0,get:function(){return $t.isExecutableDefinitionNode}});Object.defineProperty(ae,"isSelectionNode",{enumerable:!0,get:function(){return $t.isSelectionNode}});Object.defineProperty(ae,"isTypeDefinitionNode",{enumerable:!0,get:function(){return $t.isTypeDefinitionNode}});Object.defineProperty(ae,"isTypeExtensionNode",{enumerable:!0,get:function(){return $t.isTypeExtensionNode}});Object.defineProperty(ae,"isTypeNode",{enumerable:!0,get:function(){return $t.isTypeNode}});Object.defineProperty(ae,"isTypeSystemDefinitionNode",{enumerable:!0,get:function(){return $t.isTypeSystemDefinitionNode}});Object.defineProperty(ae,"isTypeSystemExtensionNode",{enumerable:!0,get:function(){return $t.isTypeSystemExtensionNode}});Object.defineProperty(ae,"isValueNode",{enumerable:!0,get:function(){return $t.isValueNode}});Object.defineProperty(ae,"parse",{enumerable:!0,get:function(){return ka.parse}});Object.defineProperty(ae,"parseConstValue",{enumerable:!0,get:function(){return ka.parseConstValue}});Object.defineProperty(ae,"parseType",{enumerable:!0,get:function(){return ka.parseType}});Object.defineProperty(ae,"parseValue",{enumerable:!0,get:function(){return ka.parseValue}});Object.defineProperty(ae,"print",{enumerable:!0,get:function(){return i0.print}});Object.defineProperty(ae,"printLocation",{enumerable:!0,get:function(){return Lf.printLocation}});Object.defineProperty(ae,"printSourceLocation",{enumerable:!0,get:function(){return Lf.printSourceLocation}});Object.defineProperty(ae,"visit",{enumerable:!0,get:function(){return pi.visit}});Object.defineProperty(ae,"visitInParallel",{enumerable:!0,get:function(){return pi.visitInParallel}});var Z_=Yi(),e0=Vi(),Lf=vo(),t0=ie(),n0=Rr(),r0=xi(),ka=tr(),i0=ct(),pi=Ln(),hc=Vt(),$t=qn(),a0=zn()});var Af=_(vc=>{"use strict";Object.defineProperty(vc,"__esModule",{value:!0});vc.isAsyncIterable=o0;function o0(e){return typeof(e==null?void 0:e[Symbol.asyncIterator])=="function"}});var Pf=_(Tc=>{"use strict";Object.defineProperty(Tc,"__esModule",{value:!0});Tc.mapAsyncIterator=s0;function s0(e,t){let n=e[Symbol.asyncIterator]();async function r(i){if(i.done)return i;try{return{value:await t(i.value),done:!1}}catch(a){if(typeof n.return=="function")try{await n.return()}catch{}throw a}}return{async next(){return r(await n.next())},async return(){return typeof n.return=="function"?r(await n.return()):{value:void 0,done:!0}},async throw(i){if(typeof n.throw=="function")return r(await n.throw(i));throw i},[Symbol.asyncIterator](){return this}}}});var wf=_(wa=>{"use strict";Object.defineProperty(wa,"__esModule",{value:!0});wa.createSourceEventStream=kf;wa.subscribe=m0;var u0=et(),c0=pe(),jf=Af(),Ff=ni(),gc=Q(),l0=La(),d0=Na(),fi=di(),p0=Pf(),f0=dr();async function m0(e){arguments.length<2||(0,u0.devAssert)(!1,"graphql@16 dropped long-deprecated support for positional arguments, please pass an object instead.");let t=await kf(e);if(!(0,jf.isAsyncIterable)(t))return t;let n=r=>(0,fi.execute)({...e,rootValue:r});return(0,p0.mapAsyncIterator)(t,n)}function y0(e){let t=e[0];return t&&"document"in t?t:{schema:t,document:e[1],rootValue:e[2],contextValue:e[3],variableValues:e[4],operationName:e[5],subscribeFieldResolver:e[6]}}async function kf(...e){let t=y0(e),{schema:n,document:r,variableValues:i}=t;(0,fi.assertValidExecutionArguments)(n,r,i);let a=(0,fi.buildExecutionContext)(t);if(!("schema"in a))return{errors:a};try{let o=await h0(a);if(!(0,jf.isAsyncIterable)(o))throw new Error(`Subscription field must return Async Iterable. Received: ${(0,c0.inspect)(o)}.`);return o}catch(o){if(o instanceof gc.GraphQLError)return{errors:[o]};throw o}}async function h0(e){let{schema:t,fragments:n,operation:r,variableValues:i,rootValue:a}=e,o=t.getSubscriptionType();if(o==null)throw new gc.GraphQLError("Schema is not configured to execute subscription operation.",{nodes:r});let s=(0,d0.collectFields)(t,n,i,o,r.selectionSet),[c,d]=[...s.entries()][0],f=(0,fi.getFieldDef)(t,o,d[0]);if(!f){let O=d[0].name.value;throw new gc.GraphQLError(`The subscription field "${O}" is not defined.`,{nodes:d})}let m=(0,Ff.addPath)(void 0,c,o.name),g=(0,fi.buildResolveInfo)(e,f,d,o,m);try{var T;let O=(0,f0.getArgumentValues)(f,d[0],i),U=e.contextValue,Y=await((T=f.subscribe)!==null&&T!==void 0?T:e.subscribeFieldResolver)(a,O,U,g);if(Y instanceof Error)throw Y;return Y}catch(O){throw(0,l0.locatedError)(O,d,(0,Ff.pathToArray)(m))}}});var qf=_(bt=>{"use strict";Object.defineProperty(bt,"__esModule",{value:!0});Object.defineProperty(bt,"createSourceEventStream",{enumerable:!0,get:function(){return Cf.createSourceEventStream}});Object.defineProperty(bt,"defaultFieldResolver",{enumerable:!0,get:function(){return Ca.defaultFieldResolver}});Object.defineProperty(bt,"defaultTypeResolver",{enumerable:!0,get:function(){return Ca.defaultTypeResolver}});Object.defineProperty(bt,"execute",{enumerable:!0,get:function(){return Ca.execute}});Object.defineProperty(bt,"executeSync",{enumerable:!0,get:function(){return Ca.executeSync}});Object.defineProperty(bt,"getArgumentValues",{enumerable:!0,get:function(){return bc.getArgumentValues}});Object.defineProperty(bt,"getDirectiveValues",{enumerable:!0,get:function(){return bc.getDirectiveValues}});Object.defineProperty(bt,"getVariableValues",{enumerable:!0,get:function(){return bc.getVariableValues}});Object.defineProperty(bt,"responsePathAsArray",{enumerable:!0,get:function(){return v0.pathToArray}});Object.defineProperty(bt,"subscribe",{enumerable:!0,get:function(){return Cf.subscribe}});var v0=ni(),Ca=di(),Cf=wf(),bc=dr()});var Vf=_(Nc=>{"use strict";Object.defineProperty(Nc,"__esModule",{value:!0});Nc.NoDeprecatedCustomRule=T0;var Ec=He(),mi=Q(),_c=te();function T0(e){return{Field(t){let n=e.getFieldDef(),r=n==null?void 0:n.deprecationReason;if(n&&r!=null){let i=e.getParentType();i!=null||(0,Ec.invariant)(!1),e.reportError(new mi.GraphQLError(`The field ${i.name}.${n.name} is deprecated. ${r}`,{nodes:t}))}},Argument(t){let n=e.getArgument(),r=n==null?void 0:n.deprecationReason;if(n&&r!=null){let i=e.getDirective();if(i!=null)e.reportError(new mi.GraphQLError(`Directive "@${i.name}" argument "${n.name}" is deprecated. ${r}`,{nodes:t}));else{let a=e.getParentType(),o=e.getFieldDef();a!=null&&o!=null||(0,Ec.invariant)(!1),e.reportError(new mi.GraphQLError(`Field "${a.name}.${o.name}" argument "${n.name}" is deprecated. ${r}`,{nodes:t}))}}},ObjectField(t){let n=(0,_c.getNamedType)(e.getParentInputType());if((0,_c.isInputObjectType)(n)){let r=n.getFields()[t.name.value],i=r==null?void 0:r.deprecationReason;i!=null&&e.reportError(new mi.GraphQLError(`The input field ${n.name}.${r.name} is deprecated. ${i}`,{nodes:t}))}},EnumValue(t){let n=e.getEnumValue(),r=n==null?void 0:n.deprecationReason;if(n&&r!=null){let i=(0,_c.getNamedType)(e.getInputType());i!=null||(0,Ec.invariant)(!1),e.reportError(new mi.GraphQLError(`The enum value "${i.name}.${n.name}" is deprecated. ${r}`,{nodes:t}))}}}}});var Mf=_(Oc=>{"use strict";Object.defineProperty(Oc,"__esModule",{value:!0});Oc.NoSchemaIntrospectionCustomRule=_0;var g0=Q(),b0=te(),E0=lt();function _0(e){return{Field(t){let n=(0,b0.getNamedType)(e.getType());n&&(0,E0.isIntrospectionType)(n)&&e.reportError(new g0.GraphQLError(`GraphQL introspection has been disabled, but the requested query contained the field "${t.name.value}".`,{nodes:t}))}}}});var Gf=_(J=>{"use strict";Object.defineProperty(J,"__esModule",{value:!0});Object.defineProperty(J,"ExecutableDefinitionsRule",{enumerable:!0,get:function(){return D0.ExecutableDefinitionsRule}});Object.defineProperty(J,"FieldsOnCorrectTypeRule",{enumerable:!0,get:function(){return S0.FieldsOnCorrectTypeRule}});Object.defineProperty(J,"FragmentsOnCompositeTypesRule",{enumerable:!0,get:function(){return L0.FragmentsOnCompositeTypesRule}});Object.defineProperty(J,"KnownArgumentNamesRule",{enumerable:!0,get:function(){return R0.KnownArgumentNamesRule}});Object.defineProperty(J,"KnownDirectivesRule",{enumerable:!0,get:function(){return A0.KnownDirectivesRule}});Object.defineProperty(J,"KnownFragmentNamesRule",{enumerable:!0,get:function(){return P0.KnownFragmentNamesRule}});Object.defineProperty(J,"KnownTypeNamesRule",{enumerable:!0,get:function(){return j0.KnownTypeNamesRule}});Object.defineProperty(J,"LoneAnonymousOperationRule",{enumerable:!0,get:function(){return F0.LoneAnonymousOperationRule}});Object.defineProperty(J,"LoneSchemaDefinitionRule",{enumerable:!0,get:function(){return z0.LoneSchemaDefinitionRule}});Object.defineProperty(J,"NoDeprecatedCustomRule",{enumerable:!0,get:function(){return oN.NoDeprecatedCustomRule}});Object.defineProperty(J,"NoFragmentCyclesRule",{enumerable:!0,get:function(){return k0.NoFragmentCyclesRule}});Object.defineProperty(J,"NoSchemaIntrospectionCustomRule",{enumerable:!0,get:function(){return sN.NoSchemaIntrospectionCustomRule}});Object.defineProperty(J,"NoUndefinedVariablesRule",{enumerable:!0,get:function(){return w0.NoUndefinedVariablesRule}});Object.defineProperty(J,"NoUnusedFragmentsRule",{enumerable:!0,get:function(){return C0.NoUnusedFragmentsRule}});Object.defineProperty(J,"NoUnusedVariablesRule",{enumerable:!0,get:function(){return q0.NoUnusedVariablesRule}});Object.defineProperty(J,"OverlappingFieldsCanBeMergedRule",{enumerable:!0,get:function(){return V0.OverlappingFieldsCanBeMergedRule}});Object.defineProperty(J,"PossibleFragmentSpreadsRule",{enumerable:!0,get:function(){return M0.PossibleFragmentSpreadsRule}});Object.defineProperty(J,"PossibleTypeExtensionsRule",{enumerable:!0,get:function(){return aN.PossibleTypeExtensionsRule}});Object.defineProperty(J,"ProvidedRequiredArgumentsRule",{enumerable:!0,get:function(){return G0.ProvidedRequiredArgumentsRule}});Object.defineProperty(J,"ScalarLeafsRule",{enumerable:!0,get:function(){return U0.ScalarLeafsRule}});Object.defineProperty(J,"SingleFieldSubscriptionsRule",{enumerable:!0,get:function(){return Q0.SingleFieldSubscriptionsRule}});Object.defineProperty(J,"UniqueArgumentDefinitionNamesRule",{enumerable:!0,get:function(){return rN.UniqueArgumentDefinitionNamesRule}});Object.defineProperty(J,"UniqueArgumentNamesRule",{enumerable:!0,get:function(){return K0.UniqueArgumentNamesRule}});Object.defineProperty(J,"UniqueDirectiveNamesRule",{enumerable:!0,get:function(){return iN.UniqueDirectiveNamesRule}});Object.defineProperty(J,"UniqueDirectivesPerLocationRule",{enumerable:!0,get:function(){return x0.UniqueDirectivesPerLocationRule}});Object.defineProperty(J,"UniqueEnumValueNamesRule",{enumerable:!0,get:function(){return tN.UniqueEnumValueNamesRule}});Object.defineProperty(J,"UniqueFieldDefinitionNamesRule",{enumerable:!0,get:function(){return nN.UniqueFieldDefinitionNamesRule}});Object.defineProperty(J,"UniqueFragmentNamesRule",{enumerable:!0,get:function(){return $0.UniqueFragmentNamesRule}});Object.defineProperty(J,"UniqueInputFieldNamesRule",{enumerable:!0,get:function(){return B0.UniqueInputFieldNamesRule}});Object.defineProperty(J,"UniqueOperationNamesRule",{enumerable:!0,get:function(){return Y0.UniqueOperationNamesRule}});Object.defineProperty(J,"UniqueOperationTypesRule",{enumerable:!0,get:function(){return Z0.UniqueOperationTypesRule}});Object.defineProperty(J,"UniqueTypeNamesRule",{enumerable:!0,get:function(){return eN.UniqueTypeNamesRule}});Object.defineProperty(J,"UniqueVariableNamesRule",{enumerable:!0,get:function(){return J0.UniqueVariableNamesRule}});Object.defineProperty(J,"ValidationContext",{enumerable:!0,get:function(){return O0.ValidationContext}});Object.defineProperty(J,"ValuesOfCorrectTypeRule",{enumerable:!0,get:function(){return X0.ValuesOfCorrectTypeRule}});Object.defineProperty(J,"VariablesAreInputTypesRule",{enumerable:!0,get:function(){return W0.VariablesAreInputTypesRule}});Object.defineProperty(J,"VariablesInAllowedPositionRule",{enumerable:!0,get:function(){return H0.VariablesInAllowedPositionRule}});Object.defineProperty(J,"specifiedRules",{enumerable:!0,get:function(){return I0.specifiedRules}});Object.defineProperty(J,"validate",{enumerable:!0,get:function(){return N0.validate}});var N0=ci(),O0=rc(),I0=nc(),D0=Os(),S0=Ds(),L0=Ls(),R0=Rs(),A0=Fs(),P0=ws(),j0=Vs(),F0=Gs(),k0=xs(),w0=Bs(),C0=Js(),q0=Ws(),V0=iu(),M0=su(),G0=du(),U0=fu(),Q0=_u(),K0=Su(),x0=ju(),$0=Mu(),B0=Uu(),Y0=Ku(),J0=Xu(),X0=Hu(),W0=Zu(),H0=tc(),z0=Qs(),Z0=$u(),eN=Yu(),tN=ku(),nN=qu(),rN=Iu(),iN=Ru(),aN=cu(),oN=Vf(),sN=Mf()});var Uf=_(xn=>{"use strict";Object.defineProperty(xn,"__esModule",{value:!0});Object.defineProperty(xn,"GraphQLError",{enumerable:!0,get:function(){return Ic.GraphQLError}});Object.defineProperty(xn,"formatError",{enumerable:!0,get:function(){return Ic.formatError}});Object.defineProperty(xn,"locatedError",{enumerable:!0,get:function(){return cN.locatedError}});Object.defineProperty(xn,"printError",{enumerable:!0,get:function(){return Ic.printError}});Object.defineProperty(xn,"syntaxError",{enumerable:!0,get:function(){return uN.syntaxError}});var Ic=Q(),uN=Ui(),cN=La()});var Sc=_(Dc=>{"use strict";Object.defineProperty(Dc,"__esModule",{value:!0});Dc.getIntrospectionQuery=lN;function lN(e){let t={descriptions:!0,specifiedByUrl:!1,directiveIsRepeatable:!1,schemaDescription:!1,inputValueDeprecation:!1,...e},n=t.descriptions?"description":"",r=t.specifiedByUrl?"specifiedByURL":"",i=t.directiveIsRepeatable?"isRepeatable":"",a=t.schemaDescription?n:"";function o(s){return t.inputValueDeprecation?s:""}return`
    query IntrospectionQuery {
      __schema {
        ${a}
        queryType { name }
        mutationType { name }
        subscriptionType { name }
        types {
          ...FullType
        }
        directives {
          name
          ${n}
          ${i}
          locations
          args${o("(includeDeprecated: true)")} {
            ...InputValue
          }
        }
      }
    }

    fragment FullType on __Type {
      kind
      name
      ${n}
      ${r}
      fields(includeDeprecated: true) {
        name
        ${n}
        args${o("(includeDeprecated: true)")} {
          ...InputValue
        }
        type {
          ...TypeRef
        }
        isDeprecated
        deprecationReason
      }
      inputFields${o("(includeDeprecated: true)")} {
        ...InputValue
      }
      interfaces {
        ...TypeRef
      }
      enumValues(includeDeprecated: true) {
        name
        ${n}
        isDeprecated
        deprecationReason
      }
      possibleTypes {
        ...TypeRef
      }
    }

    fragment InputValue on __InputValue {
      name
      ${n}
      type { ...TypeRef }
      defaultValue
      ${o("isDeprecated")}
      ${o("deprecationReason")}
    }

    fragment TypeRef on __Type {
      kind
      name
      ofType {
        kind
        name
        ofType {
          kind
          name
          ofType {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
                ofType {
                  kind
                  name
                  ofType {
                    kind
                    name
                    ofType {
                      kind
                      name
                      ofType {
                        kind
                        name
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  `}});var Qf=_(Lc=>{"use strict";Object.defineProperty(Lc,"__esModule",{value:!0});Lc.getOperationAST=pN;var dN=ie();function pN(e,t){let n=null;for(let i of e.definitions)if(i.kind===dN.Kind.OPERATION_DEFINITION){var r;if(t==null){if(n)return null;n=i}else if(((r=i.name)===null||r===void 0?void 0:r.value)===t)return i}return n}});var Kf=_(Rc=>{"use strict";Object.defineProperty(Rc,"__esModule",{value:!0});Rc.getOperationRootType=fN;var qa=Q();function fN(e,t){if(t.operation==="query"){let n=e.getQueryType();if(!n)throw new qa.GraphQLError("Schema does not define the required query root type.",{nodes:t});return n}if(t.operation==="mutation"){let n=e.getMutationType();if(!n)throw new qa.GraphQLError("Schema is not configured for mutations.",{nodes:t});return n}if(t.operation==="subscription"){let n=e.getSubscriptionType();if(!n)throw new qa.GraphQLError("Schema is not configured for subscriptions.",{nodes:t});return n}throw new qa.GraphQLError("Can only have query, mutation and subscription operations.",{nodes:t})}});var xf=_(Ac=>{"use strict";Object.defineProperty(Ac,"__esModule",{value:!0});Ac.introspectionFromSchema=TN;var mN=He(),yN=tr(),hN=di(),vN=Sc();function TN(e,t){let n={specifiedByUrl:!0,directiveIsRepeatable:!0,schemaDescription:!0,inputValueDeprecation:!0,...t},r=(0,yN.parse)((0,vN.getIntrospectionQuery)(n)),i=(0,hN.executeSync)({schema:e,document:r});return!i.errors&&i.data||(0,mN.invariant)(!1),i.data}});var Bf=_(Pc=>{"use strict";Object.defineProperty(Pc,"__esModule",{value:!0});Pc.buildClientSchema=IN;var gN=et(),dt=pe(),$f=qt(),Va=wr(),bN=tr(),pt=te(),EN=it(),Bt=lt(),_N=Ut(),NN=wn(),ON=oi();function IN(e,t){(0,$f.isObjectLike)(e)&&(0,$f.isObjectLike)(e.__schema)||(0,gN.devAssert)(!1,`Invalid or incomplete introspection result. Ensure that you are passing "data" property of introspection response and no "errors" was returned alongside: ${(0,dt.inspect)(e)}.`);let n=e.__schema,r=(0,Va.keyValMap)(n.types,D=>D.name,D=>g(D));for(let D of[..._N.specifiedScalarTypes,...Bt.introspectionTypes])r[D.name]&&(r[D.name]=D);let i=n.queryType?f(n.queryType):null,a=n.mutationType?f(n.mutationType):null,o=n.subscriptionType?f(n.subscriptionType):null,s=n.directives?n.directives.map(It):[];return new NN.GraphQLSchema({description:n.description,query:i,mutation:a,subscription:o,types:Object.values(r),directives:s,assumeValid:t==null?void 0:t.assumeValid});function c(D){if(D.kind===Bt.TypeKind.LIST){let K=D.ofType;if(!K)throw new Error("Decorated type deeper than introspection query.");return new pt.GraphQLList(c(K))}if(D.kind===Bt.TypeKind.NON_NULL){let K=D.ofType;if(!K)throw new Error("Decorated type deeper than introspection query.");let $e=c(K);return new pt.GraphQLNonNull((0,pt.assertNullableType)($e))}return d(D)}function d(D){let K=D.name;if(!K)throw new Error(`Unknown type reference: ${(0,dt.inspect)(D)}.`);let $e=r[K];if(!$e)throw new Error(`Invalid or incomplete schema, unknown type: ${K}. Ensure that a full introspection query is used in order to build a client schema.`);return $e}function f(D){return(0,pt.assertObjectType)(d(D))}function m(D){return(0,pt.assertInterfaceType)(d(D))}function g(D){if(D!=null&&D.name!=null&&D.kind!=null)switch(D.kind){case Bt.TypeKind.SCALAR:return T(D);case Bt.TypeKind.OBJECT:return U(D);case Bt.TypeKind.INTERFACE:return k(D);case Bt.TypeKind.UNION:return Y(D);case Bt.TypeKind.ENUM:return se(D);case Bt.TypeKind.INPUT_OBJECT:return z(D)}let K=(0,dt.inspect)(D);throw new Error(`Invalid or incomplete introspection result. Ensure that a full introspection query is used in order to build a client schema: ${K}.`)}function T(D){return new pt.GraphQLScalarType({name:D.name,description:D.description,specifiedByURL:D.specifiedByURL})}function O(D){if(D.interfaces===null&&D.kind===Bt.TypeKind.INTERFACE)return[];if(!D.interfaces){let K=(0,dt.inspect)(D);throw new Error(`Introspection result missing interfaces: ${K}.`)}return D.interfaces.map(m)}function U(D){return new pt.GraphQLObjectType({name:D.name,description:D.description,interfaces:()=>O(D),fields:()=>Oe(D)})}function k(D){return new pt.GraphQLInterfaceType({name:D.name,description:D.description,interfaces:()=>O(D),fields:()=>Oe(D)})}function Y(D){if(!D.possibleTypes){let K=(0,dt.inspect)(D);throw new Error(`Introspection result missing possibleTypes: ${K}.`)}return new pt.GraphQLUnionType({name:D.name,description:D.description,types:()=>D.possibleTypes.map(f)})}function se(D){if(!D.enumValues){let K=(0,dt.inspect)(D);throw new Error(`Introspection result missing enumValues: ${K}.`)}return new pt.GraphQLEnumType({name:D.name,description:D.description,values:(0,Va.keyValMap)(D.enumValues,K=>K.name,K=>({description:K.description,deprecationReason:K.deprecationReason}))})}function z(D){if(!D.inputFields){let K=(0,dt.inspect)(D);throw new Error(`Introspection result missing inputFields: ${K}.`)}return new pt.GraphQLInputObjectType({name:D.name,description:D.description,fields:()=>we(D.inputFields)})}function Oe(D){if(!D.fields)throw new Error(`Introspection result missing fields: ${(0,dt.inspect)(D)}.`);return(0,Va.keyValMap)(D.fields,K=>K.name,ke)}function ke(D){let K=c(D.type);if(!(0,pt.isOutputType)(K)){let $e=(0,dt.inspect)(K);throw new Error(`Introspection must provide output type for fields, but received: ${$e}.`)}if(!D.args){let $e=(0,dt.inspect)(D);throw new Error(`Introspection result missing field args: ${$e}.`)}return{description:D.description,deprecationReason:D.deprecationReason,type:K,args:we(D.args)}}function we(D){return(0,Va.keyValMap)(D,K=>K.name,Te)}function Te(D){let K=c(D.type);if(!(0,pt.isInputType)(K)){let Dt=(0,dt.inspect)(K);throw new Error(`Introspection must provide input type for arguments, but received: ${Dt}.`)}let $e=D.defaultValue!=null?(0,ON.valueFromAST)((0,bN.parseValue)(D.defaultValue),K):void 0;return{description:D.description,type:K,defaultValue:$e,deprecationReason:D.deprecationReason}}function It(D){if(!D.args){let K=(0,dt.inspect)(D);throw new Error(`Introspection result missing directive args: ${K}.`)}if(!D.locations){let K=(0,dt.inspect)(D);throw new Error(`Introspection result missing directive locations: ${K}.`)}return new EN.GraphQLDirective({name:D.name,description:D.description,isRepeatable:D.isRepeatable,locations:D.locations.slice(),args:we(D.args)})}}});var jc=_(Ua=>{"use strict";Object.defineProperty(Ua,"__esModule",{value:!0});Ua.extendSchema=PN;Ua.extendSchemaImpl=Zf;var DN=et(),SN=pe(),LN=He(),RN=fn(),yi=ko(),Et=ie(),Yf=qn(),De=te(),Ma=it(),Jf=lt(),Xf=Ut(),Wf=wn(),AN=ci(),Hf=dr(),zf=oi();function PN(e,t,n){(0,Wf.assertSchema)(e),t!=null&&t.kind===Et.Kind.DOCUMENT||(0,DN.devAssert)(!1,"Must provide valid Document AST."),(n==null?void 0:n.assumeValid)!==!0&&(n==null?void 0:n.assumeValidSDL)!==!0&&(0,AN.assertValidSDLExtension)(t,e);let r=e.toConfig(),i=Zf(r,t,n);return r===i?e:new Wf.GraphQLSchema(i)}function Zf(e,t,n){var r,i,a,o;let s=[],c=Object.create(null),d=[],f,m=[];for(let N of t.definitions)if(N.kind===Et.Kind.SCHEMA_DEFINITION)f=N;else if(N.kind===Et.Kind.SCHEMA_EXTENSION)m.push(N);else if((0,Yf.isTypeDefinitionNode)(N))s.push(N);else if((0,Yf.isTypeExtensionNode)(N)){let C=N.name.value,w=c[C];c[C]=w?w.concat([N]):[N]}else N.kind===Et.Kind.DIRECTIVE_DEFINITION&&d.push(N);if(Object.keys(c).length===0&&s.length===0&&d.length===0&&m.length===0&&f==null)return e;let g=Object.create(null);for(let N of e.types)g[N.name]=se(N);for(let N of s){var T;let C=N.name.value;g[C]=(T=em[C])!==null&&T!==void 0?T:wi(N)}let O={query:e.query&&k(e.query),mutation:e.mutation&&k(e.mutation),subscription:e.subscription&&k(e.subscription),...f&&$e([f]),...$e(m)};return{description:(r=f)===null||r===void 0||(i=r.description)===null||i===void 0?void 0:i.value,...O,types:Object.values(g),directives:[...e.directives.map(Y),...d.map(Jn)],extensions:Object.create(null),astNode:(a=f)!==null&&a!==void 0?a:e.astNode,extensionASTNodes:e.extensionASTNodes.concat(m),assumeValid:(o=n==null?void 0:n.assumeValid)!==null&&o!==void 0?o:!1};function U(N){return(0,De.isListType)(N)?new De.GraphQLList(U(N.ofType)):(0,De.isNonNullType)(N)?new De.GraphQLNonNull(U(N.ofType)):k(N)}function k(N){return g[N.name]}function Y(N){let C=N.toConfig();return new Ma.GraphQLDirective({...C,args:(0,yi.mapValue)(C.args,K)})}function se(N){if((0,Jf.isIntrospectionType)(N)||(0,Xf.isSpecifiedScalarType)(N))return N;if((0,De.isScalarType)(N))return ke(N);if((0,De.isObjectType)(N))return we(N);if((0,De.isInterfaceType)(N))return Te(N);if((0,De.isUnionType)(N))return It(N);if((0,De.isEnumType)(N))return Oe(N);if((0,De.isInputObjectType)(N))return z(N);(0,LN.invariant)(!1,"Unexpected type: "+(0,SN.inspect)(N))}function z(N){var C;let w=N.toConfig(),M=(C=c[w.name])!==null&&C!==void 0?C:[];return new De.GraphQLInputObjectType({...w,fields:()=>({...(0,yi.mapValue)(w.fields,ye=>({...ye,type:U(ye.type)})),...ji(M)}),extensionASTNodes:w.extensionASTNodes.concat(M)})}function Oe(N){var C;let w=N.toConfig(),M=(C=c[N.name])!==null&&C!==void 0?C:[];return new De.GraphQLEnumType({...w,values:{...w.values,...Fi(M)},extensionASTNodes:w.extensionASTNodes.concat(M)})}function ke(N){var C;let w=N.toConfig(),M=(C=c[w.name])!==null&&C!==void 0?C:[],ye=w.specifiedByURL;for(let ge of M){var je;ye=(je=tm(ge))!==null&&je!==void 0?je:ye}return new De.GraphQLScalarType({...w,specifiedByURL:ye,extensionASTNodes:w.extensionASTNodes.concat(M)})}function we(N){var C;let w=N.toConfig(),M=(C=c[w.name])!==null&&C!==void 0?C:[];return new De.GraphQLObjectType({...w,interfaces:()=>[...N.getInterfaces().map(k),...cn(M)],fields:()=>({...(0,yi.mapValue)(w.fields,D),..._n(M)}),extensionASTNodes:w.extensionASTNodes.concat(M)})}function Te(N){var C;let w=N.toConfig(),M=(C=c[w.name])!==null&&C!==void 0?C:[];return new De.GraphQLInterfaceType({...w,interfaces:()=>[...N.getInterfaces().map(k),...cn(M)],fields:()=>({...(0,yi.mapValue)(w.fields,D),..._n(M)}),extensionASTNodes:w.extensionASTNodes.concat(M)})}function It(N){var C;let w=N.toConfig(),M=(C=c[w.name])!==null&&C!==void 0?C:[];return new De.GraphQLUnionType({...w,types:()=>[...N.getTypes().map(k),...ki(M)],extensionASTNodes:w.extensionASTNodes.concat(M)})}function D(N){return{...N,type:U(N.type),args:N.args&&(0,yi.mapValue)(N.args,K)}}function K(N){return{...N,type:U(N.type)}}function $e(N){let C={};for(let M of N){var w;let ye=(w=M.operationTypes)!==null&&w!==void 0?w:[];for(let je of ye)C[je.operation]=Dt(je.type)}return C}function Dt(N){var C;let w=N.name.value,M=(C=em[w])!==null&&C!==void 0?C:g[w];if(M===void 0)throw new Error(`Unknown type: "${w}".`);return M}function un(N){return N.kind===Et.Kind.LIST_TYPE?new De.GraphQLList(un(N.type)):N.kind===Et.Kind.NON_NULL_TYPE?new De.GraphQLNonNull(un(N.type)):Dt(N)}function Jn(N){var C;return new Ma.GraphQLDirective({name:N.name.value,description:(C=N.description)===null||C===void 0?void 0:C.value,locations:N.locations.map(({value:w})=>w),isRepeatable:N.repeatable,args:Pi(N.arguments),astNode:N})}function _n(N){let C=Object.create(null);for(let ye of N){var w;let je=(w=ye.fields)!==null&&w!==void 0?w:[];for(let ge of je){var M;C[ge.name.value]={type:un(ge.type),description:(M=ge.description)===null||M===void 0?void 0:M.value,args:Pi(ge.arguments),deprecationReason:Ga(ge),astNode:ge}}}return C}function Pi(N){let C=N!=null?N:[],w=Object.create(null);for(let ye of C){var M;let je=un(ye.type);w[ye.name.value]={type:je,description:(M=ye.description)===null||M===void 0?void 0:M.value,defaultValue:(0,zf.valueFromAST)(ye.defaultValue,je),deprecationReason:Ga(ye),astNode:ye}}return w}function ji(N){let C=Object.create(null);for(let ye of N){var w;let je=(w=ye.fields)!==null&&w!==void 0?w:[];for(let ge of je){var M;let ln=un(ge.type);C[ge.name.value]={type:ln,description:(M=ge.description)===null||M===void 0?void 0:M.value,defaultValue:(0,zf.valueFromAST)(ge.defaultValue,ln),deprecationReason:Ga(ge),astNode:ge}}}return C}function Fi(N){let C=Object.create(null);for(let ye of N){var w;let je=(w=ye.values)!==null&&w!==void 0?w:[];for(let ge of je){var M;C[ge.name.value]={description:(M=ge.description)===null||M===void 0?void 0:M.value,deprecationReason:Ga(ge),astNode:ge}}}return C}function cn(N){return N.flatMap(C=>{var w,M;return(w=(M=C.interfaces)===null||M===void 0?void 0:M.map(Dt))!==null&&w!==void 0?w:[]})}function ki(N){return N.flatMap(C=>{var w,M;return(w=(M=C.types)===null||M===void 0?void 0:M.map(Dt))!==null&&w!==void 0?w:[]})}function wi(N){var C;let w=N.name.value,M=(C=c[w])!==null&&C!==void 0?C:[];switch(N.kind){case Et.Kind.OBJECT_TYPE_DEFINITION:{var ye;let ut=[N,...M];return new De.GraphQLObjectType({name:w,description:(ye=N.description)===null||ye===void 0?void 0:ye.value,interfaces:()=>cn(ut),fields:()=>_n(ut),astNode:N,extensionASTNodes:M})}case Et.Kind.INTERFACE_TYPE_DEFINITION:{var je;let ut=[N,...M];return new De.GraphQLInterfaceType({name:w,description:(je=N.description)===null||je===void 0?void 0:je.value,interfaces:()=>cn(ut),fields:()=>_n(ut),astNode:N,extensionASTNodes:M})}case Et.Kind.ENUM_TYPE_DEFINITION:{var ge;let ut=[N,...M];return new De.GraphQLEnumType({name:w,description:(ge=N.description)===null||ge===void 0?void 0:ge.value,values:Fi(ut),astNode:N,extensionASTNodes:M})}case Et.Kind.UNION_TYPE_DEFINITION:{var ln;let ut=[N,...M];return new De.GraphQLUnionType({name:w,description:(ln=N.description)===null||ln===void 0?void 0:ln.value,types:()=>ki(ut),astNode:N,extensionASTNodes:M})}case Et.Kind.SCALAR_TYPE_DEFINITION:{var Nr;return new De.GraphQLScalarType({name:w,description:(Nr=N.description)===null||Nr===void 0?void 0:Nr.value,specifiedByURL:tm(N),astNode:N,extensionASTNodes:M})}case Et.Kind.INPUT_OBJECT_TYPE_DEFINITION:{var Nn;let ut=[N,...M];return new De.GraphQLInputObjectType({name:w,description:(Nn=N.description)===null||Nn===void 0?void 0:Nn.value,fields:()=>ji(ut),astNode:N,extensionASTNodes:M})}}}}var em=(0,RN.keyMap)([...Xf.specifiedScalarTypes,...Jf.introspectionTypes],e=>e.name);function Ga(e){let t=(0,Hf.getDirectiveValues)(Ma.GraphQLDeprecatedDirective,e);return t==null?void 0:t.reason}function tm(e){let t=(0,Hf.getDirectiveValues)(Ma.GraphQLSpecifiedByDirective,e);return t==null?void 0:t.url}});var rm=_(Qa=>{"use strict";Object.defineProperty(Qa,"__esModule",{value:!0});Qa.buildASTSchema=nm;Qa.buildSchema=MN;var jN=et(),FN=ie(),kN=tr(),wN=it(),CN=wn(),qN=ci(),VN=jc();function nm(e,t){e!=null&&e.kind===FN.Kind.DOCUMENT||(0,jN.devAssert)(!1,"Must provide valid Document AST."),(t==null?void 0:t.assumeValid)!==!0&&(t==null?void 0:t.assumeValidSDL)!==!0&&(0,qN.assertValidSDL)(e);let n={description:void 0,types:[],directives:[],extensions:Object.create(null),extensionASTNodes:[],assumeValid:!1},r=(0,VN.extendSchemaImpl)(n,e,t);if(r.astNode==null)for(let a of r.types)switch(a.name){case"Query":r.query=a;break;case"Mutation":r.mutation=a;break;case"Subscription":r.subscription=a;break}let i=[...r.directives,...wN.specifiedDirectives.filter(a=>r.directives.every(o=>o.name!==a.name))];return new CN.GraphQLSchema({...r,directives:i})}function MN(e,t){let n=(0,kN.parse)(e,{noLocation:t==null?void 0:t.noLocation,allowLegacyFragmentVariables:t==null?void 0:t.allowLegacyFragmentVariables});return nm(n,{assumeValidSDL:t==null?void 0:t.assumeValidSDL,assumeValid:t==null?void 0:t.assumeValid})}});var om=_(kc=>{"use strict";Object.defineProperty(kc,"__esModule",{value:!0});kc.lexicographicSortSchema=BN;var GN=pe(),UN=He(),QN=wr(),im=Cr(),tt=te(),KN=it(),xN=lt(),$N=wn();function BN(e){let t=e.toConfig(),n=(0,QN.keyValMap)(Fc(t.types),g=>g.name,m);return new $N.GraphQLSchema({...t,types:Object.values(n),directives:Fc(t.directives).map(o),query:a(t.query),mutation:a(t.mutation),subscription:a(t.subscription)});function r(g){return(0,tt.isListType)(g)?new tt.GraphQLList(r(g.ofType)):(0,tt.isNonNullType)(g)?new tt.GraphQLNonNull(r(g.ofType)):i(g)}function i(g){return n[g.name]}function a(g){return g&&i(g)}function o(g){let T=g.toConfig();return new KN.GraphQLDirective({...T,locations:am(T.locations,O=>O),args:s(T.args)})}function s(g){return Ka(g,T=>({...T,type:r(T.type)}))}function c(g){return Ka(g,T=>({...T,type:r(T.type),args:T.args&&s(T.args)}))}function d(g){return Ka(g,T=>({...T,type:r(T.type)}))}function f(g){return Fc(g).map(i)}function m(g){if((0,tt.isScalarType)(g)||(0,xN.isIntrospectionType)(g))return g;if((0,tt.isObjectType)(g)){let T=g.toConfig();return new tt.GraphQLObjectType({...T,interfaces:()=>f(T.interfaces),fields:()=>c(T.fields)})}if((0,tt.isInterfaceType)(g)){let T=g.toConfig();return new tt.GraphQLInterfaceType({...T,interfaces:()=>f(T.interfaces),fields:()=>c(T.fields)})}if((0,tt.isUnionType)(g)){let T=g.toConfig();return new tt.GraphQLUnionType({...T,types:()=>f(T.types)})}if((0,tt.isEnumType)(g)){let T=g.toConfig();return new tt.GraphQLEnumType({...T,values:Ka(T.values,O=>O)})}if((0,tt.isInputObjectType)(g)){let T=g.toConfig();return new tt.GraphQLInputObjectType({...T,fields:()=>d(T.fields)})}(0,UN.invariant)(!1,"Unexpected type: "+(0,GN.inspect)(g))}}function Ka(e,t){let n=Object.create(null);for(let r of Object.keys(e).sort(im.naturalCompare))n[r]=t(e[r]);return n}function Fc(e){return am(e,t=>t.name)}function am(e,t){return e.slice().sort((n,r)=>{let i=t(n),a=t(r);return(0,im.naturalCompare)(i,a)})}});var fm=_(hi=>{"use strict";Object.defineProperty(hi,"__esModule",{value:!0});hi.printIntrospectionSchema=ZN;hi.printSchema=zN;hi.printType=cm;var YN=pe(),JN=He(),XN=Sr(),wc=ie(),xa=ct(),yr=te(),Cc=it(),sm=lt(),WN=Ut(),HN=Xr();function zN(e){return um(e,t=>!(0,Cc.isSpecifiedDirective)(t),eO)}function ZN(e){return um(e,Cc.isSpecifiedDirective,sm.isIntrospectionType)}function eO(e){return!(0,WN.isSpecifiedScalarType)(e)&&!(0,sm.isIntrospectionType)(e)}function um(e,t,n){let r=e.getDirectives().filter(t),i=Object.values(e.getTypeMap()).filter(n);return[tO(e),...r.map(a=>cO(a)),...i.map(a=>cm(a))].filter(Boolean).join(`

`)}function tO(e){if(e.description==null&&nO(e))return;let t=[],n=e.getQueryType();n&&t.push(`  query: ${n.name}`);let r=e.getMutationType();r&&t.push(`  mutation: ${r.name}`);let i=e.getSubscriptionType();return i&&t.push(`  subscription: ${i.name}`),_t(e)+`schema {
${t.join(`
`)}
}`}function nO(e){let t=e.getQueryType();if(t&&t.name!=="Query")return!1;let n=e.getMutationType();if(n&&n.name!=="Mutation")return!1;let r=e.getSubscriptionType();return!(r&&r.name!=="Subscription")}function cm(e){if((0,yr.isScalarType)(e))return rO(e);if((0,yr.isObjectType)(e))return iO(e);if((0,yr.isInterfaceType)(e))return aO(e);if((0,yr.isUnionType)(e))return oO(e);if((0,yr.isEnumType)(e))return sO(e);if((0,yr.isInputObjectType)(e))return uO(e);(0,JN.invariant)(!1,"Unexpected type: "+(0,YN.inspect)(e))}function rO(e){return _t(e)+`scalar ${e.name}`+lO(e)}function lm(e){let t=e.getInterfaces();return t.length?" implements "+t.map(n=>n.name).join(" & "):""}function iO(e){return _t(e)+`type ${e.name}`+lm(e)+dm(e)}function aO(e){return _t(e)+`interface ${e.name}`+lm(e)+dm(e)}function oO(e){let t=e.getTypes(),n=t.length?" = "+t.join(" | "):"";return _t(e)+"union "+e.name+n}function sO(e){let t=e.getValues().map((n,r)=>_t(n,"  ",!r)+"  "+n.name+Mc(n.deprecationReason));return _t(e)+`enum ${e.name}`+qc(t)}function uO(e){let t=Object.values(e.getFields()).map((n,r)=>_t(n,"  ",!r)+"  "+Vc(n));return _t(e)+`input ${e.name}`+qc(t)}function dm(e){let t=Object.values(e.getFields()).map((n,r)=>_t(n,"  ",!r)+"  "+n.name+pm(n.args,"  ")+": "+String(n.type)+Mc(n.deprecationReason));return qc(t)}function qc(e){return e.length!==0?` {
`+e.join(`
`)+`
}`:""}function pm(e,t=""){return e.length===0?"":e.every(n=>!n.description)?"("+e.map(Vc).join(", ")+")":`(
`+e.map((n,r)=>_t(n,"  "+t,!r)+"  "+t+Vc(n)).join(`
`)+`
`+t+")"}function Vc(e){let t=(0,HN.astFromValue)(e.defaultValue,e.type),n=e.name+": "+String(e.type);return t&&(n+=` = ${(0,xa.print)(t)}`),n+Mc(e.deprecationReason)}function cO(e){return _t(e)+"directive @"+e.name+pm(e.args)+(e.isRepeatable?" repeatable":"")+" on "+e.locations.join(" | ")}function Mc(e){return e==null?"":e!==Cc.DEFAULT_DEPRECATION_REASON?` @deprecated(reason: ${(0,xa.print)({kind:wc.Kind.STRING,value:e})})`:" @deprecated"}function lO(e){return e.specifiedByURL==null?"":` @specifiedBy(url: ${(0,xa.print)({kind:wc.Kind.STRING,value:e.specifiedByURL})})`}function _t(e,t="",n=!0){let{description:r}=e;if(r==null)return"";let i=(0,xa.print)({kind:wc.Kind.STRING,value:r,block:(0,XN.isPrintableAsBlockString)(r)});return(t&&!n?`
`+t:t)+i.replace(/\n/g,`
`+t)+`
`}});var mm=_(Gc=>{"use strict";Object.defineProperty(Gc,"__esModule",{value:!0});Gc.concatAST=pO;var dO=ie();function pO(e){let t=[];for(let n of e)t.push(...n.definitions);return{kind:dO.Kind.DOCUMENT,definitions:t}}});var vm=_(Uc=>{"use strict";Object.defineProperty(Uc,"__esModule",{value:!0});Uc.separateOperations=mO;var $a=ie(),fO=Ln();function mO(e){let t=[],n=Object.create(null);for(let i of e.definitions)switch(i.kind){case $a.Kind.OPERATION_DEFINITION:t.push(i);break;case $a.Kind.FRAGMENT_DEFINITION:n[i.name.value]=hm(i.selectionSet);break;default:}let r=Object.create(null);for(let i of t){let a=new Set;for(let s of hm(i.selectionSet))ym(a,n,s);let o=i.name?i.name.value:"";r[o]={kind:$a.Kind.DOCUMENT,definitions:e.definitions.filter(s=>s===i||s.kind===$a.Kind.FRAGMENT_DEFINITION&&a.has(s.name.value))}}return r}function ym(e,t,n){if(!e.has(n)){e.add(n);let r=t[n];if(r!==void 0)for(let i of r)ym(e,t,i)}}function hm(e){let t=[];return(0,fO.visit)(e,{FragmentSpread(n){t.push(n.name.value)}}),t}});var bm=_(Kc=>{"use strict";Object.defineProperty(Kc,"__esModule",{value:!0});Kc.stripIgnoredCharacters=hO;var yO=Sr(),Tm=xi(),gm=Yi(),Qc=Rr();function hO(e){let t=(0,gm.isSource)(e)?e:new gm.Source(e),n=t.body,r=new Tm.Lexer(t),i="",a=!1;for(;r.advance().kind!==Qc.TokenKind.EOF;){let o=r.token,s=o.kind,c=!(0,Tm.isPunctuatorTokenKind)(o.kind);a&&(c||o.kind===Qc.TokenKind.SPREAD)&&(i+=" ");let d=n.slice(o.start,o.end);s===Qc.TokenKind.BLOCK_STRING?i+=(0,yO.printBlockString)(o.value,{minimize:!0}):i+=d,a=c}return i}});var _m=_(Ba=>{"use strict";Object.defineProperty(Ba,"__esModule",{value:!0});Ba.assertValidName=bO;Ba.isValidNameError=Em;var vO=et(),TO=Q(),gO=qr();function bO(e){let t=Em(e);if(t)throw t;return e}function Em(e){if(typeof e=="string"||(0,vO.devAssert)(!1,"Expected name to be a string."),e.startsWith("__"))return new TO.GraphQLError(`Name "${e}" must not begin with "__", which is reserved by GraphQL introspection.`);try{(0,gO.assertName)(e)}catch(t){return t}}});var Am=_(Yt=>{"use strict";Object.defineProperty(Yt,"__esModule",{value:!0});Yt.DangerousChangeType=Yt.BreakingChangeType=void 0;Yt.findBreakingChanges=DO;Yt.findDangerousChanges=SO;var EO=pe(),Nm=He(),Om=fn(),_O=ct(),ue=te(),NO=Ut(),OO=Xr(),IO=Zs(),Pe;Yt.BreakingChangeType=Pe;(function(e){e.TYPE_REMOVED="TYPE_REMOVED",e.TYPE_CHANGED_KIND="TYPE_CHANGED_KIND",e.TYPE_REMOVED_FROM_UNION="TYPE_REMOVED_FROM_UNION",e.VALUE_REMOVED_FROM_ENUM="VALUE_REMOVED_FROM_ENUM",e.REQUIRED_INPUT_FIELD_ADDED="REQUIRED_INPUT_FIELD_ADDED",e.IMPLEMENTED_INTERFACE_REMOVED="IMPLEMENTED_INTERFACE_REMOVED",e.FIELD_REMOVED="FIELD_REMOVED",e.FIELD_CHANGED_KIND="FIELD_CHANGED_KIND",e.REQUIRED_ARG_ADDED="REQUIRED_ARG_ADDED",e.ARG_REMOVED="ARG_REMOVED",e.ARG_CHANGED_KIND="ARG_CHANGED_KIND",e.DIRECTIVE_REMOVED="DIRECTIVE_REMOVED",e.DIRECTIVE_ARG_REMOVED="DIRECTIVE_ARG_REMOVED",e.REQUIRED_DIRECTIVE_ARG_ADDED="REQUIRED_DIRECTIVE_ARG_ADDED",e.DIRECTIVE_REPEATABLE_REMOVED="DIRECTIVE_REPEATABLE_REMOVED",e.DIRECTIVE_LOCATION_REMOVED="DIRECTIVE_LOCATION_REMOVED"})(Pe||(Yt.BreakingChangeType=Pe={}));var Ct;Yt.DangerousChangeType=Ct;(function(e){e.VALUE_ADDED_TO_ENUM="VALUE_ADDED_TO_ENUM",e.TYPE_ADDED_TO_UNION="TYPE_ADDED_TO_UNION",e.OPTIONAL_INPUT_FIELD_ADDED="OPTIONAL_INPUT_FIELD_ADDED",e.OPTIONAL_ARG_ADDED="OPTIONAL_ARG_ADDED",e.IMPLEMENTED_INTERFACE_ADDED="IMPLEMENTED_INTERFACE_ADDED",e.ARG_DEFAULT_VALUE_CHANGE="ARG_DEFAULT_VALUE_CHANGE"})(Ct||(Yt.DangerousChangeType=Ct={}));function DO(e,t){return Im(e,t).filter(n=>n.type in Pe)}function SO(e,t){return Im(e,t).filter(n=>n.type in Ct)}function Im(e,t){return[...RO(e,t),...LO(e,t)]}function LO(e,t){let n=[],r=an(e.getDirectives(),t.getDirectives());for(let i of r.removed)n.push({type:Pe.DIRECTIVE_REMOVED,description:`${i.name} was removed.`});for(let[i,a]of r.persisted){let o=an(i.args,a.args);for(let s of o.added)(0,ue.isRequiredArgument)(s)&&n.push({type:Pe.REQUIRED_DIRECTIVE_ARG_ADDED,description:`A required arg ${s.name} on directive ${i.name} was added.`});for(let s of o.removed)n.push({type:Pe.DIRECTIVE_ARG_REMOVED,description:`${s.name} was removed from ${i.name}.`});i.isRepeatable&&!a.isRepeatable&&n.push({type:Pe.DIRECTIVE_REPEATABLE_REMOVED,description:`Repeatable flag was removed from ${i.name}.`});for(let s of i.locations)a.locations.includes(s)||n.push({type:Pe.DIRECTIVE_LOCATION_REMOVED,description:`${s} was removed from ${i.name}.`})}return n}function RO(e,t){let n=[],r=an(Object.values(e.getTypeMap()),Object.values(t.getTypeMap()));for(let i of r.removed)n.push({type:Pe.TYPE_REMOVED,description:(0,NO.isSpecifiedScalarType)(i)?`Standard scalar ${i.name} was removed because it is not referenced anymore.`:`${i.name} was removed.`});for(let[i,a]of r.persisted)(0,ue.isEnumType)(i)&&(0,ue.isEnumType)(a)?n.push(...jO(i,a)):(0,ue.isUnionType)(i)&&(0,ue.isUnionType)(a)?n.push(...PO(i,a)):(0,ue.isInputObjectType)(i)&&(0,ue.isInputObjectType)(a)?n.push(...AO(i,a)):(0,ue.isObjectType)(i)&&(0,ue.isObjectType)(a)?n.push(...Sm(i,a),...Dm(i,a)):(0,ue.isInterfaceType)(i)&&(0,ue.isInterfaceType)(a)?n.push(...Sm(i,a),...Dm(i,a)):i.constructor!==a.constructor&&n.push({type:Pe.TYPE_CHANGED_KIND,description:`${i.name} changed from ${Lm(i)} to ${Lm(a)}.`});return n}function AO(e,t){let n=[],r=an(Object.values(e.getFields()),Object.values(t.getFields()));for(let i of r.added)(0,ue.isRequiredInputField)(i)?n.push({type:Pe.REQUIRED_INPUT_FIELD_ADDED,description:`A required field ${i.name} on input type ${e.name} was added.`}):n.push({type:Ct.OPTIONAL_INPUT_FIELD_ADDED,description:`An optional field ${i.name} on input type ${e.name} was added.`});for(let i of r.removed)n.push({type:Pe.FIELD_REMOVED,description:`${e.name}.${i.name} was removed.`});for(let[i,a]of r.persisted)Ti(i.type,a.type)||n.push({type:Pe.FIELD_CHANGED_KIND,description:`${e.name}.${i.name} changed type from ${String(i.type)} to ${String(a.type)}.`});return n}function PO(e,t){let n=[],r=an(e.getTypes(),t.getTypes());for(let i of r.added)n.push({type:Ct.TYPE_ADDED_TO_UNION,description:`${i.name} was added to union type ${e.name}.`});for(let i of r.removed)n.push({type:Pe.TYPE_REMOVED_FROM_UNION,description:`${i.name} was removed from union type ${e.name}.`});return n}function jO(e,t){let n=[],r=an(e.getValues(),t.getValues());for(let i of r.added)n.push({type:Ct.VALUE_ADDED_TO_ENUM,description:`${i.name} was added to enum type ${e.name}.`});for(let i of r.removed)n.push({type:Pe.VALUE_REMOVED_FROM_ENUM,description:`${i.name} was removed from enum type ${e.name}.`});return n}function Dm(e,t){let n=[],r=an(e.getInterfaces(),t.getInterfaces());for(let i of r.added)n.push({type:Ct.IMPLEMENTED_INTERFACE_ADDED,description:`${i.name} added to interfaces implemented by ${e.name}.`});for(let i of r.removed)n.push({type:Pe.IMPLEMENTED_INTERFACE_REMOVED,description:`${e.name} no longer implements interface ${i.name}.`});return n}function Sm(e,t){let n=[],r=an(Object.values(e.getFields()),Object.values(t.getFields()));for(let i of r.removed)n.push({type:Pe.FIELD_REMOVED,description:`${e.name}.${i.name} was removed.`});for(let[i,a]of r.persisted)n.push(...FO(e,i,a)),vi(i.type,a.type)||n.push({type:Pe.FIELD_CHANGED_KIND,description:`${e.name}.${i.name} changed type from ${String(i.type)} to ${String(a.type)}.`});return n}function FO(e,t,n){let r=[],i=an(t.args,n.args);for(let a of i.removed)r.push({type:Pe.ARG_REMOVED,description:`${e.name}.${t.name} arg ${a.name} was removed.`});for(let[a,o]of i.persisted)if(!Ti(a.type,o.type))r.push({type:Pe.ARG_CHANGED_KIND,description:`${e.name}.${t.name} arg ${a.name} has changed type from ${String(a.type)} to ${String(o.type)}.`});else if(a.defaultValue!==void 0)if(o.defaultValue===void 0)r.push({type:Ct.ARG_DEFAULT_VALUE_CHANGE,description:`${e.name}.${t.name} arg ${a.name} defaultValue was removed.`});else{let c=Rm(a.defaultValue,a.type),d=Rm(o.defaultValue,o.type);c!==d&&r.push({type:Ct.ARG_DEFAULT_VALUE_CHANGE,description:`${e.name}.${t.name} arg ${a.name} has changed defaultValue from ${c} to ${d}.`})}for(let a of i.added)(0,ue.isRequiredArgument)(a)?r.push({type:Pe.REQUIRED_ARG_ADDED,description:`A required arg ${a.name} on ${e.name}.${t.name} was added.`}):r.push({type:Ct.OPTIONAL_ARG_ADDED,description:`An optional arg ${a.name} on ${e.name}.${t.name} was added.`});return r}function vi(e,t){return(0,ue.isListType)(e)?(0,ue.isListType)(t)&&vi(e.ofType,t.ofType)||(0,ue.isNonNullType)(t)&&vi(e,t.ofType):(0,ue.isNonNullType)(e)?(0,ue.isNonNullType)(t)&&vi(e.ofType,t.ofType):(0,ue.isNamedType)(t)&&e.name===t.name||(0,ue.isNonNullType)(t)&&vi(e,t.ofType)}function Ti(e,t){return(0,ue.isListType)(e)?(0,ue.isListType)(t)&&Ti(e.ofType,t.ofType):(0,ue.isNonNullType)(e)?(0,ue.isNonNullType)(t)&&Ti(e.ofType,t.ofType)||!(0,ue.isNonNullType)(t)&&Ti(e.ofType,t):(0,ue.isNamedType)(t)&&e.name===t.name}function Lm(e){if((0,ue.isScalarType)(e))return"a Scalar type";if((0,ue.isObjectType)(e))return"an Object type";if((0,ue.isInterfaceType)(e))return"an Interface type";if((0,ue.isUnionType)(e))return"a Union type";if((0,ue.isEnumType)(e))return"an Enum type";if((0,ue.isInputObjectType)(e))return"an Input type";(0,Nm.invariant)(!1,"Unexpected type: "+(0,EO.inspect)(e))}function Rm(e,t){let n=(0,OO.astFromValue)(e,t);return n!=null||(0,Nm.invariant)(!1),(0,_O.print)((0,IO.sortValueNode)(n))}function an(e,t){let n=[],r=[],i=[],a=(0,Om.keyMap)(e,({name:s})=>s),o=(0,Om.keyMap)(t,({name:s})=>s);for(let s of e){let c=o[s.name];c===void 0?r.push(s):i.push([s,c])}for(let s of t)a[s.name]===void 0&&n.push(s);return{added:n,persisted:i,removed:r}}});var km=_(oe=>{"use strict";Object.defineProperty(oe,"__esModule",{value:!0});Object.defineProperty(oe,"BreakingChangeType",{enumerable:!0,get:function(){return Ya.BreakingChangeType}});Object.defineProperty(oe,"DangerousChangeType",{enumerable:!0,get:function(){return Ya.DangerousChangeType}});Object.defineProperty(oe,"TypeInfo",{enumerable:!0,get:function(){return jm.TypeInfo}});Object.defineProperty(oe,"assertValidName",{enumerable:!0,get:function(){return Fm.assertValidName}});Object.defineProperty(oe,"astFromValue",{enumerable:!0,get:function(){return xO.astFromValue}});Object.defineProperty(oe,"buildASTSchema",{enumerable:!0,get:function(){return Pm.buildASTSchema}});Object.defineProperty(oe,"buildClientSchema",{enumerable:!0,get:function(){return VO.buildClientSchema}});Object.defineProperty(oe,"buildSchema",{enumerable:!0,get:function(){return Pm.buildSchema}});Object.defineProperty(oe,"coerceInputValue",{enumerable:!0,get:function(){return $O.coerceInputValue}});Object.defineProperty(oe,"concatAST",{enumerable:!0,get:function(){return BO.concatAST}});Object.defineProperty(oe,"doTypesOverlap",{enumerable:!0,get:function(){return $c.doTypesOverlap}});Object.defineProperty(oe,"extendSchema",{enumerable:!0,get:function(){return MO.extendSchema}});Object.defineProperty(oe,"findBreakingChanges",{enumerable:!0,get:function(){return Ya.findBreakingChanges}});Object.defineProperty(oe,"findDangerousChanges",{enumerable:!0,get:function(){return Ya.findDangerousChanges}});Object.defineProperty(oe,"getIntrospectionQuery",{enumerable:!0,get:function(){return kO.getIntrospectionQuery}});Object.defineProperty(oe,"getOperationAST",{enumerable:!0,get:function(){return wO.getOperationAST}});Object.defineProperty(oe,"getOperationRootType",{enumerable:!0,get:function(){return CO.getOperationRootType}});Object.defineProperty(oe,"introspectionFromSchema",{enumerable:!0,get:function(){return qO.introspectionFromSchema}});Object.defineProperty(oe,"isEqualType",{enumerable:!0,get:function(){return $c.isEqualType}});Object.defineProperty(oe,"isTypeSubTypeOf",{enumerable:!0,get:function(){return $c.isTypeSubTypeOf}});Object.defineProperty(oe,"isValidNameError",{enumerable:!0,get:function(){return Fm.isValidNameError}});Object.defineProperty(oe,"lexicographicSortSchema",{enumerable:!0,get:function(){return GO.lexicographicSortSchema}});Object.defineProperty(oe,"printIntrospectionSchema",{enumerable:!0,get:function(){return xc.printIntrospectionSchema}});Object.defineProperty(oe,"printSchema",{enumerable:!0,get:function(){return xc.printSchema}});Object.defineProperty(oe,"printType",{enumerable:!0,get:function(){return xc.printType}});Object.defineProperty(oe,"separateOperations",{enumerable:!0,get:function(){return YO.separateOperations}});Object.defineProperty(oe,"stripIgnoredCharacters",{enumerable:!0,get:function(){return JO.stripIgnoredCharacters}});Object.defineProperty(oe,"typeFromAST",{enumerable:!0,get:function(){return UO.typeFromAST}});Object.defineProperty(oe,"valueFromAST",{enumerable:!0,get:function(){return QO.valueFromAST}});Object.defineProperty(oe,"valueFromASTUntyped",{enumerable:!0,get:function(){return KO.valueFromASTUntyped}});Object.defineProperty(oe,"visitWithTypeInfo",{enumerable:!0,get:function(){return jm.visitWithTypeInfo}});var kO=Sc(),wO=Qf(),CO=Kf(),qO=xf(),VO=Bf(),Pm=rm(),MO=jc(),GO=om(),xc=fm(),UO=Qt(),QO=oi(),KO=xo(),xO=Xr(),jm=pa(),$O=vu(),BO=mm(),YO=vm(),JO=bm(),$c=Qr(),Fm=_m(),Ya=Am()});var nt=_(y=>{"use strict";Object.defineProperty(y,"__esModule",{value:!0});Object.defineProperty(y,"BREAK",{enumerable:!0,get:function(){return le.BREAK}});Object.defineProperty(y,"BreakingChangeType",{enumerable:!0,get:function(){return de.BreakingChangeType}});Object.defineProperty(y,"DEFAULT_DEPRECATION_REASON",{enumerable:!0,get:function(){return j.DEFAULT_DEPRECATION_REASON}});Object.defineProperty(y,"DangerousChangeType",{enumerable:!0,get:function(){return de.DangerousChangeType}});Object.defineProperty(y,"DirectiveLocation",{enumerable:!0,get:function(){return le.DirectiveLocation}});Object.defineProperty(y,"ExecutableDefinitionsRule",{enumerable:!0,get:function(){return H.ExecutableDefinitionsRule}});Object.defineProperty(y,"FieldsOnCorrectTypeRule",{enumerable:!0,get:function(){return H.FieldsOnCorrectTypeRule}});Object.defineProperty(y,"FragmentsOnCompositeTypesRule",{enumerable:!0,get:function(){return H.FragmentsOnCompositeTypesRule}});Object.defineProperty(y,"GRAPHQL_MAX_INT",{enumerable:!0,get:function(){return j.GRAPHQL_MAX_INT}});Object.defineProperty(y,"GRAPHQL_MIN_INT",{enumerable:!0,get:function(){return j.GRAPHQL_MIN_INT}});Object.defineProperty(y,"GraphQLBoolean",{enumerable:!0,get:function(){return j.GraphQLBoolean}});Object.defineProperty(y,"GraphQLDeprecatedDirective",{enumerable:!0,get:function(){return j.GraphQLDeprecatedDirective}});Object.defineProperty(y,"GraphQLDirective",{enumerable:!0,get:function(){return j.GraphQLDirective}});Object.defineProperty(y,"GraphQLEnumType",{enumerable:!0,get:function(){return j.GraphQLEnumType}});Object.defineProperty(y,"GraphQLError",{enumerable:!0,get:function(){return gi.GraphQLError}});Object.defineProperty(y,"GraphQLFloat",{enumerable:!0,get:function(){return j.GraphQLFloat}});Object.defineProperty(y,"GraphQLID",{enumerable:!0,get:function(){return j.GraphQLID}});Object.defineProperty(y,"GraphQLIncludeDirective",{enumerable:!0,get:function(){return j.GraphQLIncludeDirective}});Object.defineProperty(y,"GraphQLInputObjectType",{enumerable:!0,get:function(){return j.GraphQLInputObjectType}});Object.defineProperty(y,"GraphQLInt",{enumerable:!0,get:function(){return j.GraphQLInt}});Object.defineProperty(y,"GraphQLInterfaceType",{enumerable:!0,get:function(){return j.GraphQLInterfaceType}});Object.defineProperty(y,"GraphQLList",{enumerable:!0,get:function(){return j.GraphQLList}});Object.defineProperty(y,"GraphQLNonNull",{enumerable:!0,get:function(){return j.GraphQLNonNull}});Object.defineProperty(y,"GraphQLObjectType",{enumerable:!0,get:function(){return j.GraphQLObjectType}});Object.defineProperty(y,"GraphQLScalarType",{enumerable:!0,get:function(){return j.GraphQLScalarType}});Object.defineProperty(y,"GraphQLSchema",{enumerable:!0,get:function(){return j.GraphQLSchema}});Object.defineProperty(y,"GraphQLSkipDirective",{enumerable:!0,get:function(){return j.GraphQLSkipDirective}});Object.defineProperty(y,"GraphQLSpecifiedByDirective",{enumerable:!0,get:function(){return j.GraphQLSpecifiedByDirective}});Object.defineProperty(y,"GraphQLString",{enumerable:!0,get:function(){return j.GraphQLString}});Object.defineProperty(y,"GraphQLUnionType",{enumerable:!0,get:function(){return j.GraphQLUnionType}});Object.defineProperty(y,"Kind",{enumerable:!0,get:function(){return le.Kind}});Object.defineProperty(y,"KnownArgumentNamesRule",{enumerable:!0,get:function(){return H.KnownArgumentNamesRule}});Object.defineProperty(y,"KnownDirectivesRule",{enumerable:!0,get:function(){return H.KnownDirectivesRule}});Object.defineProperty(y,"KnownFragmentNamesRule",{enumerable:!0,get:function(){return H.KnownFragmentNamesRule}});Object.defineProperty(y,"KnownTypeNamesRule",{enumerable:!0,get:function(){return H.KnownTypeNamesRule}});Object.defineProperty(y,"Lexer",{enumerable:!0,get:function(){return le.Lexer}});Object.defineProperty(y,"Location",{enumerable:!0,get:function(){return le.Location}});Object.defineProperty(y,"LoneAnonymousOperationRule",{enumerable:!0,get:function(){return H.LoneAnonymousOperationRule}});Object.defineProperty(y,"LoneSchemaDefinitionRule",{enumerable:!0,get:function(){return H.LoneSchemaDefinitionRule}});Object.defineProperty(y,"NoDeprecatedCustomRule",{enumerable:!0,get:function(){return H.NoDeprecatedCustomRule}});Object.defineProperty(y,"NoFragmentCyclesRule",{enumerable:!0,get:function(){return H.NoFragmentCyclesRule}});Object.defineProperty(y,"NoSchemaIntrospectionCustomRule",{enumerable:!0,get:function(){return H.NoSchemaIntrospectionCustomRule}});Object.defineProperty(y,"NoUndefinedVariablesRule",{enumerable:!0,get:function(){return H.NoUndefinedVariablesRule}});Object.defineProperty(y,"NoUnusedFragmentsRule",{enumerable:!0,get:function(){return H.NoUnusedFragmentsRule}});Object.defineProperty(y,"NoUnusedVariablesRule",{enumerable:!0,get:function(){return H.NoUnusedVariablesRule}});Object.defineProperty(y,"OperationTypeNode",{enumerable:!0,get:function(){return le.OperationTypeNode}});Object.defineProperty(y,"OverlappingFieldsCanBeMergedRule",{enumerable:!0,get:function(){return H.OverlappingFieldsCanBeMergedRule}});Object.defineProperty(y,"PossibleFragmentSpreadsRule",{enumerable:!0,get:function(){return H.PossibleFragmentSpreadsRule}});Object.defineProperty(y,"PossibleTypeExtensionsRule",{enumerable:!0,get:function(){return H.PossibleTypeExtensionsRule}});Object.defineProperty(y,"ProvidedRequiredArgumentsRule",{enumerable:!0,get:function(){return H.ProvidedRequiredArgumentsRule}});Object.defineProperty(y,"ScalarLeafsRule",{enumerable:!0,get:function(){return H.ScalarLeafsRule}});Object.defineProperty(y,"SchemaMetaFieldDef",{enumerable:!0,get:function(){return j.SchemaMetaFieldDef}});Object.defineProperty(y,"SingleFieldSubscriptionsRule",{enumerable:!0,get:function(){return H.SingleFieldSubscriptionsRule}});Object.defineProperty(y,"Source",{enumerable:!0,get:function(){return le.Source}});Object.defineProperty(y,"Token",{enumerable:!0,get:function(){return le.Token}});Object.defineProperty(y,"TokenKind",{enumerable:!0,get:function(){return le.TokenKind}});Object.defineProperty(y,"TypeInfo",{enumerable:!0,get:function(){return de.TypeInfo}});Object.defineProperty(y,"TypeKind",{enumerable:!0,get:function(){return j.TypeKind}});Object.defineProperty(y,"TypeMetaFieldDef",{enumerable:!0,get:function(){return j.TypeMetaFieldDef}});Object.defineProperty(y,"TypeNameMetaFieldDef",{enumerable:!0,get:function(){return j.TypeNameMetaFieldDef}});Object.defineProperty(y,"UniqueArgumentDefinitionNamesRule",{enumerable:!0,get:function(){return H.UniqueArgumentDefinitionNamesRule}});Object.defineProperty(y,"UniqueArgumentNamesRule",{enumerable:!0,get:function(){return H.UniqueArgumentNamesRule}});Object.defineProperty(y,"UniqueDirectiveNamesRule",{enumerable:!0,get:function(){return H.UniqueDirectiveNamesRule}});Object.defineProperty(y,"UniqueDirectivesPerLocationRule",{enumerable:!0,get:function(){return H.UniqueDirectivesPerLocationRule}});Object.defineProperty(y,"UniqueEnumValueNamesRule",{enumerable:!0,get:function(){return H.UniqueEnumValueNamesRule}});Object.defineProperty(y,"UniqueFieldDefinitionNamesRule",{enumerable:!0,get:function(){return H.UniqueFieldDefinitionNamesRule}});Object.defineProperty(y,"UniqueFragmentNamesRule",{enumerable:!0,get:function(){return H.UniqueFragmentNamesRule}});Object.defineProperty(y,"UniqueInputFieldNamesRule",{enumerable:!0,get:function(){return H.UniqueInputFieldNamesRule}});Object.defineProperty(y,"UniqueOperationNamesRule",{enumerable:!0,get:function(){return H.UniqueOperationNamesRule}});Object.defineProperty(y,"UniqueOperationTypesRule",{enumerable:!0,get:function(){return H.UniqueOperationTypesRule}});Object.defineProperty(y,"UniqueTypeNamesRule",{enumerable:!0,get:function(){return H.UniqueTypeNamesRule}});Object.defineProperty(y,"UniqueVariableNamesRule",{enumerable:!0,get:function(){return H.UniqueVariableNamesRule}});Object.defineProperty(y,"ValidationContext",{enumerable:!0,get:function(){return H.ValidationContext}});Object.defineProperty(y,"ValuesOfCorrectTypeRule",{enumerable:!0,get:function(){return H.ValuesOfCorrectTypeRule}});Object.defineProperty(y,"VariablesAreInputTypesRule",{enumerable:!0,get:function(){return H.VariablesAreInputTypesRule}});Object.defineProperty(y,"VariablesInAllowedPositionRule",{enumerable:!0,get:function(){return H.VariablesInAllowedPositionRule}});Object.defineProperty(y,"__Directive",{enumerable:!0,get:function(){return j.__Directive}});Object.defineProperty(y,"__DirectiveLocation",{enumerable:!0,get:function(){return j.__DirectiveLocation}});Object.defineProperty(y,"__EnumValue",{enumerable:!0,get:function(){return j.__EnumValue}});Object.defineProperty(y,"__Field",{enumerable:!0,get:function(){return j.__Field}});Object.defineProperty(y,"__InputValue",{enumerable:!0,get:function(){return j.__InputValue}});Object.defineProperty(y,"__Schema",{enumerable:!0,get:function(){return j.__Schema}});Object.defineProperty(y,"__Type",{enumerable:!0,get:function(){return j.__Type}});Object.defineProperty(y,"__TypeKind",{enumerable:!0,get:function(){return j.__TypeKind}});Object.defineProperty(y,"assertAbstractType",{enumerable:!0,get:function(){return j.assertAbstractType}});Object.defineProperty(y,"assertCompositeType",{enumerable:!0,get:function(){return j.assertCompositeType}});Object.defineProperty(y,"assertDirective",{enumerable:!0,get:function(){return j.assertDirective}});Object.defineProperty(y,"assertEnumType",{enumerable:!0,get:function(){return j.assertEnumType}});Object.defineProperty(y,"assertEnumValueName",{enumerable:!0,get:function(){return j.assertEnumValueName}});Object.defineProperty(y,"assertInputObjectType",{enumerable:!0,get:function(){return j.assertInputObjectType}});Object.defineProperty(y,"assertInputType",{enumerable:!0,get:function(){return j.assertInputType}});Object.defineProperty(y,"assertInterfaceType",{enumerable:!0,get:function(){return j.assertInterfaceType}});Object.defineProperty(y,"assertLeafType",{enumerable:!0,get:function(){return j.assertLeafType}});Object.defineProperty(y,"assertListType",{enumerable:!0,get:function(){return j.assertListType}});Object.defineProperty(y,"assertName",{enumerable:!0,get:function(){return j.assertName}});Object.defineProperty(y,"assertNamedType",{enumerable:!0,get:function(){return j.assertNamedType}});Object.defineProperty(y,"assertNonNullType",{enumerable:!0,get:function(){return j.assertNonNullType}});Object.defineProperty(y,"assertNullableType",{enumerable:!0,get:function(){return j.assertNullableType}});Object.defineProperty(y,"assertObjectType",{enumerable:!0,get:function(){return j.assertObjectType}});Object.defineProperty(y,"assertOutputType",{enumerable:!0,get:function(){return j.assertOutputType}});Object.defineProperty(y,"assertScalarType",{enumerable:!0,get:function(){return j.assertScalarType}});Object.defineProperty(y,"assertSchema",{enumerable:!0,get:function(){return j.assertSchema}});Object.defineProperty(y,"assertType",{enumerable:!0,get:function(){return j.assertType}});Object.defineProperty(y,"assertUnionType",{enumerable:!0,get:function(){return j.assertUnionType}});Object.defineProperty(y,"assertValidName",{enumerable:!0,get:function(){return de.assertValidName}});Object.defineProperty(y,"assertValidSchema",{enumerable:!0,get:function(){return j.assertValidSchema}});Object.defineProperty(y,"assertWrappingType",{enumerable:!0,get:function(){return j.assertWrappingType}});Object.defineProperty(y,"astFromValue",{enumerable:!0,get:function(){return de.astFromValue}});Object.defineProperty(y,"buildASTSchema",{enumerable:!0,get:function(){return de.buildASTSchema}});Object.defineProperty(y,"buildClientSchema",{enumerable:!0,get:function(){return de.buildClientSchema}});Object.defineProperty(y,"buildSchema",{enumerable:!0,get:function(){return de.buildSchema}});Object.defineProperty(y,"coerceInputValue",{enumerable:!0,get:function(){return de.coerceInputValue}});Object.defineProperty(y,"concatAST",{enumerable:!0,get:function(){return de.concatAST}});Object.defineProperty(y,"createSourceEventStream",{enumerable:!0,get:function(){return Jt.createSourceEventStream}});Object.defineProperty(y,"defaultFieldResolver",{enumerable:!0,get:function(){return Jt.defaultFieldResolver}});Object.defineProperty(y,"defaultTypeResolver",{enumerable:!0,get:function(){return Jt.defaultTypeResolver}});Object.defineProperty(y,"doTypesOverlap",{enumerable:!0,get:function(){return de.doTypesOverlap}});Object.defineProperty(y,"execute",{enumerable:!0,get:function(){return Jt.execute}});Object.defineProperty(y,"executeSync",{enumerable:!0,get:function(){return Jt.executeSync}});Object.defineProperty(y,"extendSchema",{enumerable:!0,get:function(){return de.extendSchema}});Object.defineProperty(y,"findBreakingChanges",{enumerable:!0,get:function(){return de.findBreakingChanges}});Object.defineProperty(y,"findDangerousChanges",{enumerable:!0,get:function(){return de.findDangerousChanges}});Object.defineProperty(y,"formatError",{enumerable:!0,get:function(){return gi.formatError}});Object.defineProperty(y,"getArgumentValues",{enumerable:!0,get:function(){return Jt.getArgumentValues}});Object.defineProperty(y,"getDirectiveValues",{enumerable:!0,get:function(){return Jt.getDirectiveValues}});Object.defineProperty(y,"getEnterLeaveForKind",{enumerable:!0,get:function(){return le.getEnterLeaveForKind}});Object.defineProperty(y,"getIntrospectionQuery",{enumerable:!0,get:function(){return de.getIntrospectionQuery}});Object.defineProperty(y,"getLocation",{enumerable:!0,get:function(){return le.getLocation}});Object.defineProperty(y,"getNamedType",{enumerable:!0,get:function(){return j.getNamedType}});Object.defineProperty(y,"getNullableType",{enumerable:!0,get:function(){return j.getNullableType}});Object.defineProperty(y,"getOperationAST",{enumerable:!0,get:function(){return de.getOperationAST}});Object.defineProperty(y,"getOperationRootType",{enumerable:!0,get:function(){return de.getOperationRootType}});Object.defineProperty(y,"getVariableValues",{enumerable:!0,get:function(){return Jt.getVariableValues}});Object.defineProperty(y,"getVisitFn",{enumerable:!0,get:function(){return le.getVisitFn}});Object.defineProperty(y,"graphql",{enumerable:!0,get:function(){return Cm.graphql}});Object.defineProperty(y,"graphqlSync",{enumerable:!0,get:function(){return Cm.graphqlSync}});Object.defineProperty(y,"introspectionFromSchema",{enumerable:!0,get:function(){return de.introspectionFromSchema}});Object.defineProperty(y,"introspectionTypes",{enumerable:!0,get:function(){return j.introspectionTypes}});Object.defineProperty(y,"isAbstractType",{enumerable:!0,get:function(){return j.isAbstractType}});Object.defineProperty(y,"isCompositeType",{enumerable:!0,get:function(){return j.isCompositeType}});Object.defineProperty(y,"isConstValueNode",{enumerable:!0,get:function(){return le.isConstValueNode}});Object.defineProperty(y,"isDefinitionNode",{enumerable:!0,get:function(){return le.isDefinitionNode}});Object.defineProperty(y,"isDirective",{enumerable:!0,get:function(){return j.isDirective}});Object.defineProperty(y,"isEnumType",{enumerable:!0,get:function(){return j.isEnumType}});Object.defineProperty(y,"isEqualType",{enumerable:!0,get:function(){return de.isEqualType}});Object.defineProperty(y,"isExecutableDefinitionNode",{enumerable:!0,get:function(){return le.isExecutableDefinitionNode}});Object.defineProperty(y,"isInputObjectType",{enumerable:!0,get:function(){return j.isInputObjectType}});Object.defineProperty(y,"isInputType",{enumerable:!0,get:function(){return j.isInputType}});Object.defineProperty(y,"isInterfaceType",{enumerable:!0,get:function(){return j.isInterfaceType}});Object.defineProperty(y,"isIntrospectionType",{enumerable:!0,get:function(){return j.isIntrospectionType}});Object.defineProperty(y,"isLeafType",{enumerable:!0,get:function(){return j.isLeafType}});Object.defineProperty(y,"isListType",{enumerable:!0,get:function(){return j.isListType}});Object.defineProperty(y,"isNamedType",{enumerable:!0,get:function(){return j.isNamedType}});Object.defineProperty(y,"isNonNullType",{enumerable:!0,get:function(){return j.isNonNullType}});Object.defineProperty(y,"isNullableType",{enumerable:!0,get:function(){return j.isNullableType}});Object.defineProperty(y,"isObjectType",{enumerable:!0,get:function(){return j.isObjectType}});Object.defineProperty(y,"isOutputType",{enumerable:!0,get:function(){return j.isOutputType}});Object.defineProperty(y,"isRequiredArgument",{enumerable:!0,get:function(){return j.isRequiredArgument}});Object.defineProperty(y,"isRequiredInputField",{enumerable:!0,get:function(){return j.isRequiredInputField}});Object.defineProperty(y,"isScalarType",{enumerable:!0,get:function(){return j.isScalarType}});Object.defineProperty(y,"isSchema",{enumerable:!0,get:function(){return j.isSchema}});Object.defineProperty(y,"isSelectionNode",{enumerable:!0,get:function(){return le.isSelectionNode}});Object.defineProperty(y,"isSpecifiedDirective",{enumerable:!0,get:function(){return j.isSpecifiedDirective}});Object.defineProperty(y,"isSpecifiedScalarType",{enumerable:!0,get:function(){return j.isSpecifiedScalarType}});Object.defineProperty(y,"isType",{enumerable:!0,get:function(){return j.isType}});Object.defineProperty(y,"isTypeDefinitionNode",{enumerable:!0,get:function(){return le.isTypeDefinitionNode}});Object.defineProperty(y,"isTypeExtensionNode",{enumerable:!0,get:function(){return le.isTypeExtensionNode}});Object.defineProperty(y,"isTypeNode",{enumerable:!0,get:function(){return le.isTypeNode}});Object.defineProperty(y,"isTypeSubTypeOf",{enumerable:!0,get:function(){return de.isTypeSubTypeOf}});Object.defineProperty(y,"isTypeSystemDefinitionNode",{enumerable:!0,get:function(){return le.isTypeSystemDefinitionNode}});Object.defineProperty(y,"isTypeSystemExtensionNode",{enumerable:!0,get:function(){return le.isTypeSystemExtensionNode}});Object.defineProperty(y,"isUnionType",{enumerable:!0,get:function(){return j.isUnionType}});Object.defineProperty(y,"isValidNameError",{enumerable:!0,get:function(){return de.isValidNameError}});Object.defineProperty(y,"isValueNode",{enumerable:!0,get:function(){return le.isValueNode}});Object.defineProperty(y,"isWrappingType",{enumerable:!0,get:function(){return j.isWrappingType}});Object.defineProperty(y,"lexicographicSortSchema",{enumerable:!0,get:function(){return de.lexicographicSortSchema}});Object.defineProperty(y,"locatedError",{enumerable:!0,get:function(){return gi.locatedError}});Object.defineProperty(y,"parse",{enumerable:!0,get:function(){return le.parse}});Object.defineProperty(y,"parseConstValue",{enumerable:!0,get:function(){return le.parseConstValue}});Object.defineProperty(y,"parseType",{enumerable:!0,get:function(){return le.parseType}});Object.defineProperty(y,"parseValue",{enumerable:!0,get:function(){return le.parseValue}});Object.defineProperty(y,"print",{enumerable:!0,get:function(){return le.print}});Object.defineProperty(y,"printError",{enumerable:!0,get:function(){return gi.printError}});Object.defineProperty(y,"printIntrospectionSchema",{enumerable:!0,get:function(){return de.printIntrospectionSchema}});Object.defineProperty(y,"printLocation",{enumerable:!0,get:function(){return le.printLocation}});Object.defineProperty(y,"printSchema",{enumerable:!0,get:function(){return de.printSchema}});Object.defineProperty(y,"printSourceLocation",{enumerable:!0,get:function(){return le.printSourceLocation}});Object.defineProperty(y,"printType",{enumerable:!0,get:function(){return de.printType}});Object.defineProperty(y,"resolveObjMapThunk",{enumerable:!0,get:function(){return j.resolveObjMapThunk}});Object.defineProperty(y,"resolveReadonlyArrayThunk",{enumerable:!0,get:function(){return j.resolveReadonlyArrayThunk}});Object.defineProperty(y,"responsePathAsArray",{enumerable:!0,get:function(){return Jt.responsePathAsArray}});Object.defineProperty(y,"separateOperations",{enumerable:!0,get:function(){return de.separateOperations}});Object.defineProperty(y,"specifiedDirectives",{enumerable:!0,get:function(){return j.specifiedDirectives}});Object.defineProperty(y,"specifiedRules",{enumerable:!0,get:function(){return H.specifiedRules}});Object.defineProperty(y,"specifiedScalarTypes",{enumerable:!0,get:function(){return j.specifiedScalarTypes}});Object.defineProperty(y,"stripIgnoredCharacters",{enumerable:!0,get:function(){return de.stripIgnoredCharacters}});Object.defineProperty(y,"subscribe",{enumerable:!0,get:function(){return Jt.subscribe}});Object.defineProperty(y,"syntaxError",{enumerable:!0,get:function(){return gi.syntaxError}});Object.defineProperty(y,"typeFromAST",{enumerable:!0,get:function(){return de.typeFromAST}});Object.defineProperty(y,"validate",{enumerable:!0,get:function(){return H.validate}});Object.defineProperty(y,"validateSchema",{enumerable:!0,get:function(){return j.validateSchema}});Object.defineProperty(y,"valueFromAST",{enumerable:!0,get:function(){return de.valueFromAST}});Object.defineProperty(y,"valueFromASTUntyped",{enumerable:!0,get:function(){return de.valueFromASTUntyped}});Object.defineProperty(y,"version",{enumerable:!0,get:function(){return wm.version}});Object.defineProperty(y,"versionInfo",{enumerable:!0,get:function(){return wm.versionInfo}});Object.defineProperty(y,"visit",{enumerable:!0,get:function(){return le.visit}});Object.defineProperty(y,"visitInParallel",{enumerable:!0,get:function(){return le.visitInParallel}});Object.defineProperty(y,"visitWithTypeInfo",{enumerable:!0,get:function(){return de.visitWithTypeInfo}});var wm=Nl(),Cm=Of(),j=Sf(),le=Rf(),Jt=qf(),H=Gf(),gi=Uf(),de=km()});var Yc=_(Nt=>{"use strict";Object.defineProperty(Nt,"__esModule",{value:!0});Nt.hintList=Nt.objectValues=Nt.forEachState=Nt.getFieldDef=Nt.getDefinitionState=void 0;var XO=nt(),hr=lt();function WO(e){let t;return qm(e,n=>{switch(n.kind){case"Query":case"ShortQuery":case"Mutation":case"Subscription":case"FragmentDefinition":t=n;break}}),t}Nt.getDefinitionState=WO;function HO(e,t,n){return n===hr.SchemaMetaFieldDef.name&&e.getQueryType()===t?hr.SchemaMetaFieldDef:n===hr.TypeMetaFieldDef.name&&e.getQueryType()===t?hr.TypeMetaFieldDef:n===hr.TypeNameMetaFieldDef.name&&(0,XO.isCompositeType)(t)?hr.TypeNameMetaFieldDef:"getFields"in t?t.getFields()[n]:null}Nt.getFieldDef=HO;function qm(e,t){let n=[],r=e;for(;r==null?void 0:r.kind;)n.push(r),r=r.prevState;for(let i=n.length-1;i>=0;i--)t(n[i])}Nt.forEachState=qm;function zO(e){let t=Object.keys(e),n=t.length,r=new Array(n);for(let i=0;i<n;++i)r[i]=e[t[i]];return r}Nt.objectValues=zO;function ZO(e,t){return eI(t,Vm(e.string))}Nt.hintList=ZO;function eI(e,t){if(!t)return Bc(e,r=>!r.isDeprecated);let n=e.map(r=>({proximity:tI(Vm(r.label),t),entry:r}));return Bc(Bc(n,r=>r.proximity<=2),r=>!r.entry.isDeprecated).sort((r,i)=>(r.entry.isDeprecated?1:0)-(i.entry.isDeprecated?1:0)||r.proximity-i.proximity||r.entry.label.length-i.entry.label.length).map(r=>r.entry)}function Bc(e,t){let n=e.filter(t);return n.length===0?e:n}function Vm(e){return e.toLowerCase().replaceAll(/\W/g,"")}function tI(e,t){let n=nI(t,e);return e.length>t.length&&(n-=e.length-t.length-1,n+=e.indexOf(t)===0?0:.5),n}function nI(e,t){let n,r,i=[],a=e.length,o=t.length;for(n=0;n<=a;n++)i[n]=[n];for(r=1;r<=o;r++)i[0][r]=r;for(n=1;n<=a;n++)for(r=1;r<=o;r++){let s=e[n-1]===t[r-1]?0:1;i[n][r]=Math.min(i[n-1][r]+1,i[n][r-1]+1,i[n-1][r-1]+s),n>1&&r>1&&e[n-1]===t[r-2]&&e[n-2]===t[r-1]&&(i[n][r]=Math.min(i[n][r],i[n-2][r-2]+s))}return i[a][o]}});var Gm=_((Mm,Ja)=>{(function(e){if(typeof Ja=="object"&&typeof Ja.exports=="object"){var t=e(bl,Mm);t!==void 0&&(Ja.exports=t)}else typeof define=="function"&&define.amd&&define(["require","exports"],e)})(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TextDocument=t.EOL=t.WorkspaceFolder=t.InlayHint=t.InlayHintLabelPart=t.InlayHintKind=t.InlineValueContext=t.InlineValueEvaluatableExpression=t.InlineValueVariableLookup=t.InlineValueText=t.SemanticTokens=t.SemanticTokenModifiers=t.SemanticTokenTypes=t.SelectionRange=t.DocumentLink=t.FormattingOptions=t.CodeLens=t.CodeAction=t.CodeActionContext=t.CodeActionTriggerKind=t.CodeActionKind=t.DocumentSymbol=t.WorkspaceSymbol=t.SymbolInformation=t.SymbolTag=t.SymbolKind=t.DocumentHighlight=t.DocumentHighlightKind=t.SignatureInformation=t.ParameterInformation=t.Hover=t.MarkedString=t.CompletionList=t.CompletionItem=t.CompletionItemLabelDetails=t.InsertTextMode=t.InsertReplaceEdit=t.CompletionItemTag=t.InsertTextFormat=t.CompletionItemKind=t.MarkupContent=t.MarkupKind=t.TextDocumentItem=t.OptionalVersionedTextDocumentIdentifier=t.VersionedTextDocumentIdentifier=t.TextDocumentIdentifier=t.WorkspaceChange=t.WorkspaceEdit=t.DeleteFile=t.RenameFile=t.CreateFile=t.TextDocumentEdit=t.AnnotatedTextEdit=t.ChangeAnnotationIdentifier=t.ChangeAnnotation=t.TextEdit=t.Command=t.Diagnostic=t.CodeDescription=t.DiagnosticTag=t.DiagnosticSeverity=t.DiagnosticRelatedInformation=t.FoldingRange=t.FoldingRangeKind=t.ColorPresentation=t.ColorInformation=t.Color=t.LocationLink=t.Location=t.Range=t.Position=t.uinteger=t.integer=t.URI=t.DocumentUri=void 0;var n;(function(l){function h(v){return typeof v=="string"}l.is=h})(n=t.DocumentUri||(t.DocumentUri={}));var r;(function(l){function h(v){return typeof v=="string"}l.is=h})(r=t.URI||(t.URI={}));var i;(function(l){l.MIN_VALUE=-2147483648,l.MAX_VALUE=2147483647;function h(v){return typeof v=="number"&&l.MIN_VALUE<=v&&v<=l.MAX_VALUE}l.is=h})(i=t.integer||(t.integer={}));var a;(function(l){l.MIN_VALUE=0,l.MAX_VALUE=2147483647;function h(v){return typeof v=="number"&&l.MIN_VALUE<=v&&v<=l.MAX_VALUE}l.is=h})(a=t.uinteger||(t.uinteger={}));var o;(function(l){function h(p,u){return p===Number.MAX_VALUE&&(p=a.MAX_VALUE),u===Number.MAX_VALUE&&(u=a.MAX_VALUE),{line:p,character:u}}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&b.uinteger(u.line)&&b.uinteger(u.character)}l.is=v})(o=t.Position||(t.Position={}));var s;(function(l){function h(p,u,E,I){if(b.uinteger(p)&&b.uinteger(u)&&b.uinteger(E)&&b.uinteger(I))return{start:o.create(p,u),end:o.create(E,I)};if(o.is(p)&&o.is(u))return{start:p,end:u};throw new Error("Range#create called with invalid arguments[".concat(p,", ").concat(u,", ").concat(E,", ").concat(I,"]"))}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&o.is(u.start)&&o.is(u.end)}l.is=v})(s=t.Range||(t.Range={}));var c;(function(l){function h(p,u){return{uri:p,range:u}}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&s.is(u.range)&&(b.string(u.uri)||b.undefined(u.uri))}l.is=v})(c=t.Location||(t.Location={}));var d;(function(l){function h(p,u,E,I){return{targetUri:p,targetRange:u,targetSelectionRange:E,originSelectionRange:I}}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&s.is(u.targetRange)&&b.string(u.targetUri)&&s.is(u.targetSelectionRange)&&(s.is(u.originSelectionRange)||b.undefined(u.originSelectionRange))}l.is=v})(d=t.LocationLink||(t.LocationLink={}));var f;(function(l){function h(p,u,E,I){return{red:p,green:u,blue:E,alpha:I}}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&b.numberRange(u.red,0,1)&&b.numberRange(u.green,0,1)&&b.numberRange(u.blue,0,1)&&b.numberRange(u.alpha,0,1)}l.is=v})(f=t.Color||(t.Color={}));var m;(function(l){function h(p,u){return{range:p,color:u}}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&s.is(u.range)&&f.is(u.color)}l.is=v})(m=t.ColorInformation||(t.ColorInformation={}));var g;(function(l){function h(p,u,E){return{label:p,textEdit:u,additionalTextEdits:E}}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&b.string(u.label)&&(b.undefined(u.textEdit)||ke.is(u))&&(b.undefined(u.additionalTextEdits)||b.typedArray(u.additionalTextEdits,ke.is))}l.is=v})(g=t.ColorPresentation||(t.ColorPresentation={}));var T;(function(l){l.Comment="comment",l.Imports="imports",l.Region="region"})(T=t.FoldingRangeKind||(t.FoldingRangeKind={}));var O;(function(l){function h(p,u,E,I,B,Le){var be={startLine:p,endLine:u};return b.defined(E)&&(be.startCharacter=E),b.defined(I)&&(be.endCharacter=I),b.defined(B)&&(be.kind=B),b.defined(Le)&&(be.collapsedText=Le),be}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&b.uinteger(u.startLine)&&b.uinteger(u.startLine)&&(b.undefined(u.startCharacter)||b.uinteger(u.startCharacter))&&(b.undefined(u.endCharacter)||b.uinteger(u.endCharacter))&&(b.undefined(u.kind)||b.string(u.kind))}l.is=v})(O=t.FoldingRange||(t.FoldingRange={}));var U;(function(l){function h(p,u){return{location:p,message:u}}l.create=h;function v(p){var u=p;return b.defined(u)&&c.is(u.location)&&b.string(u.message)}l.is=v})(U=t.DiagnosticRelatedInformation||(t.DiagnosticRelatedInformation={}));var k;(function(l){l.Error=1,l.Warning=2,l.Information=3,l.Hint=4})(k=t.DiagnosticSeverity||(t.DiagnosticSeverity={}));var Y;(function(l){l.Unnecessary=1,l.Deprecated=2})(Y=t.DiagnosticTag||(t.DiagnosticTag={}));var se;(function(l){function h(v){var p=v;return b.objectLiteral(p)&&b.string(p.href)}l.is=h})(se=t.CodeDescription||(t.CodeDescription={}));var z;(function(l){function h(p,u,E,I,B,Le){var be={range:p,message:u};return b.defined(E)&&(be.severity=E),b.defined(I)&&(be.code=I),b.defined(B)&&(be.source=B),b.defined(Le)&&(be.relatedInformation=Le),be}l.create=h;function v(p){var u,E=p;return b.defined(E)&&s.is(E.range)&&b.string(E.message)&&(b.number(E.severity)||b.undefined(E.severity))&&(b.integer(E.code)||b.string(E.code)||b.undefined(E.code))&&(b.undefined(E.codeDescription)||b.string((u=E.codeDescription)===null||u===void 0?void 0:u.href))&&(b.string(E.source)||b.undefined(E.source))&&(b.undefined(E.relatedInformation)||b.typedArray(E.relatedInformation,U.is))}l.is=v})(z=t.Diagnostic||(t.Diagnostic={}));var Oe;(function(l){function h(p,u){for(var E=[],I=2;I<arguments.length;I++)E[I-2]=arguments[I];var B={title:p,command:u};return b.defined(E)&&E.length>0&&(B.arguments=E),B}l.create=h;function v(p){var u=p;return b.defined(u)&&b.string(u.title)&&b.string(u.command)}l.is=v})(Oe=t.Command||(t.Command={}));var ke;(function(l){function h(E,I){return{range:E,newText:I}}l.replace=h;function v(E,I){return{range:{start:E,end:E},newText:I}}l.insert=v;function p(E){return{range:E,newText:""}}l.del=p;function u(E){var I=E;return b.objectLiteral(I)&&b.string(I.newText)&&s.is(I.range)}l.is=u})(ke=t.TextEdit||(t.TextEdit={}));var we;(function(l){function h(p,u,E){var I={label:p};return u!==void 0&&(I.needsConfirmation=u),E!==void 0&&(I.description=E),I}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&b.string(u.label)&&(b.boolean(u.needsConfirmation)||u.needsConfirmation===void 0)&&(b.string(u.description)||u.description===void 0)}l.is=v})(we=t.ChangeAnnotation||(t.ChangeAnnotation={}));var Te;(function(l){function h(v){var p=v;return b.string(p)}l.is=h})(Te=t.ChangeAnnotationIdentifier||(t.ChangeAnnotationIdentifier={}));var It;(function(l){function h(E,I,B){return{range:E,newText:I,annotationId:B}}l.replace=h;function v(E,I,B){return{range:{start:E,end:E},newText:I,annotationId:B}}l.insert=v;function p(E,I){return{range:E,newText:"",annotationId:I}}l.del=p;function u(E){var I=E;return ke.is(I)&&(we.is(I.annotationId)||Te.is(I.annotationId))}l.is=u})(It=t.AnnotatedTextEdit||(t.AnnotatedTextEdit={}));var D;(function(l){function h(p,u){return{textDocument:p,edits:u}}l.create=h;function v(p){var u=p;return b.defined(u)&&cn.is(u.textDocument)&&Array.isArray(u.edits)}l.is=v})(D=t.TextDocumentEdit||(t.TextDocumentEdit={}));var K;(function(l){function h(p,u,E){var I={kind:"create",uri:p};return u!==void 0&&(u.overwrite!==void 0||u.ignoreIfExists!==void 0)&&(I.options=u),E!==void 0&&(I.annotationId=E),I}l.create=h;function v(p){var u=p;return u&&u.kind==="create"&&b.string(u.uri)&&(u.options===void 0||(u.options.overwrite===void 0||b.boolean(u.options.overwrite))&&(u.options.ignoreIfExists===void 0||b.boolean(u.options.ignoreIfExists)))&&(u.annotationId===void 0||Te.is(u.annotationId))}l.is=v})(K=t.CreateFile||(t.CreateFile={}));var $e;(function(l){function h(p,u,E,I){var B={kind:"rename",oldUri:p,newUri:u};return E!==void 0&&(E.overwrite!==void 0||E.ignoreIfExists!==void 0)&&(B.options=E),I!==void 0&&(B.annotationId=I),B}l.create=h;function v(p){var u=p;return u&&u.kind==="rename"&&b.string(u.oldUri)&&b.string(u.newUri)&&(u.options===void 0||(u.options.overwrite===void 0||b.boolean(u.options.overwrite))&&(u.options.ignoreIfExists===void 0||b.boolean(u.options.ignoreIfExists)))&&(u.annotationId===void 0||Te.is(u.annotationId))}l.is=v})($e=t.RenameFile||(t.RenameFile={}));var Dt;(function(l){function h(p,u,E){var I={kind:"delete",uri:p};return u!==void 0&&(u.recursive!==void 0||u.ignoreIfNotExists!==void 0)&&(I.options=u),E!==void 0&&(I.annotationId=E),I}l.create=h;function v(p){var u=p;return u&&u.kind==="delete"&&b.string(u.uri)&&(u.options===void 0||(u.options.recursive===void 0||b.boolean(u.options.recursive))&&(u.options.ignoreIfNotExists===void 0||b.boolean(u.options.ignoreIfNotExists)))&&(u.annotationId===void 0||Te.is(u.annotationId))}l.is=v})(Dt=t.DeleteFile||(t.DeleteFile={}));var un;(function(l){function h(v){var p=v;return p&&(p.changes!==void 0||p.documentChanges!==void 0)&&(p.documentChanges===void 0||p.documentChanges.every(function(u){return b.string(u.kind)?K.is(u)||$e.is(u)||Dt.is(u):D.is(u)}))}l.is=h})(un=t.WorkspaceEdit||(t.WorkspaceEdit={}));var Jn=function(){function l(h,v){this.edits=h,this.changeAnnotations=v}return l.prototype.insert=function(h,v,p){var u,E;if(p===void 0?u=ke.insert(h,v):Te.is(p)?(E=p,u=It.insert(h,v,p)):(this.assertChangeAnnotations(this.changeAnnotations),E=this.changeAnnotations.manage(p),u=It.insert(h,v,E)),this.edits.push(u),E!==void 0)return E},l.prototype.replace=function(h,v,p){var u,E;if(p===void 0?u=ke.replace(h,v):Te.is(p)?(E=p,u=It.replace(h,v,p)):(this.assertChangeAnnotations(this.changeAnnotations),E=this.changeAnnotations.manage(p),u=It.replace(h,v,E)),this.edits.push(u),E!==void 0)return E},l.prototype.delete=function(h,v){var p,u;if(v===void 0?p=ke.del(h):Te.is(v)?(u=v,p=It.del(h,v)):(this.assertChangeAnnotations(this.changeAnnotations),u=this.changeAnnotations.manage(v),p=It.del(h,u)),this.edits.push(p),u!==void 0)return u},l.prototype.add=function(h){this.edits.push(h)},l.prototype.all=function(){return this.edits},l.prototype.clear=function(){this.edits.splice(0,this.edits.length)},l.prototype.assertChangeAnnotations=function(h){if(h===void 0)throw new Error("Text edit change is not configured to manage change annotations.")},l}(),_n=function(){function l(h){this._annotations=h===void 0?Object.create(null):h,this._counter=0,this._size=0}return l.prototype.all=function(){return this._annotations},Object.defineProperty(l.prototype,"size",{get:function(){return this._size},enumerable:!1,configurable:!0}),l.prototype.manage=function(h,v){var p;if(Te.is(h)?p=h:(p=this.nextId(),v=h),this._annotations[p]!==void 0)throw new Error("Id ".concat(p," is already in use."));if(v===void 0)throw new Error("No annotation provided for id ".concat(p));return this._annotations[p]=v,this._size++,p},l.prototype.nextId=function(){return this._counter++,this._counter.toString()},l}(),Pi=function(){function l(h){var v=this;this._textEditChanges=Object.create(null),h!==void 0?(this._workspaceEdit=h,h.documentChanges?(this._changeAnnotations=new _n(h.changeAnnotations),h.changeAnnotations=this._changeAnnotations.all(),h.documentChanges.forEach(function(p){if(D.is(p)){var u=new Jn(p.edits,v._changeAnnotations);v._textEditChanges[p.textDocument.uri]=u}})):h.changes&&Object.keys(h.changes).forEach(function(p){var u=new Jn(h.changes[p]);v._textEditChanges[p]=u})):this._workspaceEdit={}}return Object.defineProperty(l.prototype,"edit",{get:function(){return this.initDocumentChanges(),this._changeAnnotations!==void 0&&(this._changeAnnotations.size===0?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit},enumerable:!1,configurable:!0}),l.prototype.getTextEditChange=function(h){if(cn.is(h)){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var v={uri:h.uri,version:h.version},p=this._textEditChanges[v.uri];if(!p){var u=[],E={textDocument:v,edits:u};this._workspaceEdit.documentChanges.push(E),p=new Jn(u,this._changeAnnotations),this._textEditChanges[v.uri]=p}return p}else{if(this.initChanges(),this._workspaceEdit.changes===void 0)throw new Error("Workspace edit is not configured for normal text edit changes.");var p=this._textEditChanges[h];if(!p){var u=[];this._workspaceEdit.changes[h]=u,p=new Jn(u),this._textEditChanges[h]=p}return p}},l.prototype.initDocumentChanges=function(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._changeAnnotations=new _n,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())},l.prototype.initChanges=function(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._workspaceEdit.changes=Object.create(null))},l.prototype.createFile=function(h,v,p){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var u;we.is(v)||Te.is(v)?u=v:p=v;var E,I;if(u===void 0?E=K.create(h,p):(I=Te.is(u)?u:this._changeAnnotations.manage(u),E=K.create(h,p,I)),this._workspaceEdit.documentChanges.push(E),I!==void 0)return I},l.prototype.renameFile=function(h,v,p,u){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var E;we.is(p)||Te.is(p)?E=p:u=p;var I,B;if(E===void 0?I=$e.create(h,v,u):(B=Te.is(E)?E:this._changeAnnotations.manage(E),I=$e.create(h,v,u,B)),this._workspaceEdit.documentChanges.push(I),B!==void 0)return B},l.prototype.deleteFile=function(h,v,p){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");var u;we.is(v)||Te.is(v)?u=v:p=v;var E,I;if(u===void 0?E=Dt.create(h,p):(I=Te.is(u)?u:this._changeAnnotations.manage(u),E=Dt.create(h,p,I)),this._workspaceEdit.documentChanges.push(E),I!==void 0)return I},l}();t.WorkspaceChange=Pi;var ji;(function(l){function h(p){return{uri:p}}l.create=h;function v(p){var u=p;return b.defined(u)&&b.string(u.uri)}l.is=v})(ji=t.TextDocumentIdentifier||(t.TextDocumentIdentifier={}));var Fi;(function(l){function h(p,u){return{uri:p,version:u}}l.create=h;function v(p){var u=p;return b.defined(u)&&b.string(u.uri)&&b.integer(u.version)}l.is=v})(Fi=t.VersionedTextDocumentIdentifier||(t.VersionedTextDocumentIdentifier={}));var cn;(function(l){function h(p,u){return{uri:p,version:u}}l.create=h;function v(p){var u=p;return b.defined(u)&&b.string(u.uri)&&(u.version===null||b.integer(u.version))}l.is=v})(cn=t.OptionalVersionedTextDocumentIdentifier||(t.OptionalVersionedTextDocumentIdentifier={}));var ki;(function(l){function h(p,u,E,I){return{uri:p,languageId:u,version:E,text:I}}l.create=h;function v(p){var u=p;return b.defined(u)&&b.string(u.uri)&&b.string(u.languageId)&&b.integer(u.version)&&b.string(u.text)}l.is=v})(ki=t.TextDocumentItem||(t.TextDocumentItem={}));var wi;(function(l){l.PlainText="plaintext",l.Markdown="markdown";function h(v){var p=v;return p===l.PlainText||p===l.Markdown}l.is=h})(wi=t.MarkupKind||(t.MarkupKind={}));var N;(function(l){function h(v){var p=v;return b.objectLiteral(v)&&wi.is(p.kind)&&b.string(p.value)}l.is=h})(N=t.MarkupContent||(t.MarkupContent={}));var C;(function(l){l.Text=1,l.Method=2,l.Function=3,l.Constructor=4,l.Field=5,l.Variable=6,l.Class=7,l.Interface=8,l.Module=9,l.Property=10,l.Unit=11,l.Value=12,l.Enum=13,l.Keyword=14,l.Snippet=15,l.Color=16,l.File=17,l.Reference=18,l.Folder=19,l.EnumMember=20,l.Constant=21,l.Struct=22,l.Event=23,l.Operator=24,l.TypeParameter=25})(C=t.CompletionItemKind||(t.CompletionItemKind={}));var w;(function(l){l.PlainText=1,l.Snippet=2})(w=t.InsertTextFormat||(t.InsertTextFormat={}));var M;(function(l){l.Deprecated=1})(M=t.CompletionItemTag||(t.CompletionItemTag={}));var ye;(function(l){function h(p,u,E){return{newText:p,insert:u,replace:E}}l.create=h;function v(p){var u=p;return u&&b.string(u.newText)&&s.is(u.insert)&&s.is(u.replace)}l.is=v})(ye=t.InsertReplaceEdit||(t.InsertReplaceEdit={}));var je;(function(l){l.asIs=1,l.adjustIndentation=2})(je=t.InsertTextMode||(t.InsertTextMode={}));var ge;(function(l){function h(v){var p=v;return p&&(b.string(p.detail)||p.detail===void 0)&&(b.string(p.description)||p.description===void 0)}l.is=h})(ge=t.CompletionItemLabelDetails||(t.CompletionItemLabelDetails={}));var ln;(function(l){function h(v){return{label:v}}l.create=h})(ln=t.CompletionItem||(t.CompletionItem={}));var Nr;(function(l){function h(v,p){return{items:v||[],isIncomplete:!!p}}l.create=h})(Nr=t.CompletionList||(t.CompletionList={}));var Nn;(function(l){function h(p){return p.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}l.fromPlainText=h;function v(p){var u=p;return b.string(u)||b.objectLiteral(u)&&b.string(u.language)&&b.string(u.value)}l.is=v})(Nn=t.MarkedString||(t.MarkedString={}));var ut;(function(l){function h(v){var p=v;return!!p&&b.objectLiteral(p)&&(N.is(p.contents)||Nn.is(p.contents)||b.typedArray(p.contents,Nn.is))&&(v.range===void 0||s.is(v.range))}l.is=h})(ut=t.Hover||(t.Hover={}));var My;(function(l){function h(v,p){return p?{label:v,documentation:p}:{label:v}}l.create=h})(My=t.ParameterInformation||(t.ParameterInformation={}));var Gy;(function(l){function h(v,p){for(var u=[],E=2;E<arguments.length;E++)u[E-2]=arguments[E];var I={label:v};return b.defined(p)&&(I.documentation=p),b.defined(u)?I.parameters=u:I.parameters=[],I}l.create=h})(Gy=t.SignatureInformation||(t.SignatureInformation={}));var Uy;(function(l){l.Text=1,l.Read=2,l.Write=3})(Uy=t.DocumentHighlightKind||(t.DocumentHighlightKind={}));var Qy;(function(l){function h(v,p){var u={range:v};return b.number(p)&&(u.kind=p),u}l.create=h})(Qy=t.DocumentHighlight||(t.DocumentHighlight={}));var Ky;(function(l){l.File=1,l.Module=2,l.Namespace=3,l.Package=4,l.Class=5,l.Method=6,l.Property=7,l.Field=8,l.Constructor=9,l.Enum=10,l.Interface=11,l.Function=12,l.Variable=13,l.Constant=14,l.String=15,l.Number=16,l.Boolean=17,l.Array=18,l.Object=19,l.Key=20,l.Null=21,l.EnumMember=22,l.Struct=23,l.Event=24,l.Operator=25,l.TypeParameter=26})(Ky=t.SymbolKind||(t.SymbolKind={}));var xy;(function(l){l.Deprecated=1})(xy=t.SymbolTag||(t.SymbolTag={}));var $y;(function(l){function h(v,p,u,E,I){var B={name:v,kind:p,location:{uri:E,range:u}};return I&&(B.containerName=I),B}l.create=h})($y=t.SymbolInformation||(t.SymbolInformation={}));var By;(function(l){function h(v,p,u,E){return E!==void 0?{name:v,kind:p,location:{uri:u,range:E}}:{name:v,kind:p,location:{uri:u}}}l.create=h})(By=t.WorkspaceSymbol||(t.WorkspaceSymbol={}));var Yy;(function(l){function h(p,u,E,I,B,Le){var be={name:p,detail:u,kind:E,range:I,selectionRange:B};return Le!==void 0&&(be.children=Le),be}l.create=h;function v(p){var u=p;return u&&b.string(u.name)&&b.number(u.kind)&&s.is(u.range)&&s.is(u.selectionRange)&&(u.detail===void 0||b.string(u.detail))&&(u.deprecated===void 0||b.boolean(u.deprecated))&&(u.children===void 0||Array.isArray(u.children))&&(u.tags===void 0||Array.isArray(u.tags))}l.is=v})(Yy=t.DocumentSymbol||(t.DocumentSymbol={}));var Jy;(function(l){l.Empty="",l.QuickFix="quickfix",l.Refactor="refactor",l.RefactorExtract="refactor.extract",l.RefactorInline="refactor.inline",l.RefactorRewrite="refactor.rewrite",l.Source="source",l.SourceOrganizeImports="source.organizeImports",l.SourceFixAll="source.fixAll"})(Jy=t.CodeActionKind||(t.CodeActionKind={}));var uo;(function(l){l.Invoked=1,l.Automatic=2})(uo=t.CodeActionTriggerKind||(t.CodeActionTriggerKind={}));var Xy;(function(l){function h(p,u,E){var I={diagnostics:p};return u!=null&&(I.only=u),E!=null&&(I.triggerKind=E),I}l.create=h;function v(p){var u=p;return b.defined(u)&&b.typedArray(u.diagnostics,z.is)&&(u.only===void 0||b.typedArray(u.only,b.string))&&(u.triggerKind===void 0||u.triggerKind===uo.Invoked||u.triggerKind===uo.Automatic)}l.is=v})(Xy=t.CodeActionContext||(t.CodeActionContext={}));var Wy;(function(l){function h(p,u,E){var I={title:p},B=!0;return typeof u=="string"?(B=!1,I.kind=u):Oe.is(u)?I.command=u:I.edit=u,B&&E!==void 0&&(I.kind=E),I}l.create=h;function v(p){var u=p;return u&&b.string(u.title)&&(u.diagnostics===void 0||b.typedArray(u.diagnostics,z.is))&&(u.kind===void 0||b.string(u.kind))&&(u.edit!==void 0||u.command!==void 0)&&(u.command===void 0||Oe.is(u.command))&&(u.isPreferred===void 0||b.boolean(u.isPreferred))&&(u.edit===void 0||un.is(u.edit))}l.is=v})(Wy=t.CodeAction||(t.CodeAction={}));var Hy;(function(l){function h(p,u){var E={range:p};return b.defined(u)&&(E.data=u),E}l.create=h;function v(p){var u=p;return b.defined(u)&&s.is(u.range)&&(b.undefined(u.command)||Oe.is(u.command))}l.is=v})(Hy=t.CodeLens||(t.CodeLens={}));var zy;(function(l){function h(p,u){return{tabSize:p,insertSpaces:u}}l.create=h;function v(p){var u=p;return b.defined(u)&&b.uinteger(u.tabSize)&&b.boolean(u.insertSpaces)}l.is=v})(zy=t.FormattingOptions||(t.FormattingOptions={}));var Zy;(function(l){function h(p,u,E){return{range:p,target:u,data:E}}l.create=h;function v(p){var u=p;return b.defined(u)&&s.is(u.range)&&(b.undefined(u.target)||b.string(u.target))}l.is=v})(Zy=t.DocumentLink||(t.DocumentLink={}));var eh;(function(l){function h(p,u){return{range:p,parent:u}}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&s.is(u.range)&&(u.parent===void 0||l.is(u.parent))}l.is=v})(eh=t.SelectionRange||(t.SelectionRange={}));var th;(function(l){l.namespace="namespace",l.type="type",l.class="class",l.enum="enum",l.interface="interface",l.struct="struct",l.typeParameter="typeParameter",l.parameter="parameter",l.variable="variable",l.property="property",l.enumMember="enumMember",l.event="event",l.function="function",l.method="method",l.macro="macro",l.keyword="keyword",l.modifier="modifier",l.comment="comment",l.string="string",l.number="number",l.regexp="regexp",l.operator="operator",l.decorator="decorator"})(th=t.SemanticTokenTypes||(t.SemanticTokenTypes={}));var nh;(function(l){l.declaration="declaration",l.definition="definition",l.readonly="readonly",l.static="static",l.deprecated="deprecated",l.abstract="abstract",l.async="async",l.modification="modification",l.documentation="documentation",l.defaultLibrary="defaultLibrary"})(nh=t.SemanticTokenModifiers||(t.SemanticTokenModifiers={}));var rh;(function(l){function h(v){var p=v;return b.objectLiteral(p)&&(p.resultId===void 0||typeof p.resultId=="string")&&Array.isArray(p.data)&&(p.data.length===0||typeof p.data[0]=="number")}l.is=h})(rh=t.SemanticTokens||(t.SemanticTokens={}));var ih;(function(l){function h(p,u){return{range:p,text:u}}l.create=h;function v(p){var u=p;return u!=null&&s.is(u.range)&&b.string(u.text)}l.is=v})(ih=t.InlineValueText||(t.InlineValueText={}));var ah;(function(l){function h(p,u,E){return{range:p,variableName:u,caseSensitiveLookup:E}}l.create=h;function v(p){var u=p;return u!=null&&s.is(u.range)&&b.boolean(u.caseSensitiveLookup)&&(b.string(u.variableName)||u.variableName===void 0)}l.is=v})(ah=t.InlineValueVariableLookup||(t.InlineValueVariableLookup={}));var oh;(function(l){function h(p,u){return{range:p,expression:u}}l.create=h;function v(p){var u=p;return u!=null&&s.is(u.range)&&(b.string(u.expression)||u.expression===void 0)}l.is=v})(oh=t.InlineValueEvaluatableExpression||(t.InlineValueEvaluatableExpression={}));var sh;(function(l){function h(p,u){return{frameId:p,stoppedLocation:u}}l.create=h;function v(p){var u=p;return b.defined(u)&&s.is(p.stoppedLocation)}l.is=v})(sh=t.InlineValueContext||(t.InlineValueContext={}));var vl;(function(l){l.Type=1,l.Parameter=2;function h(v){return v===1||v===2}l.is=h})(vl=t.InlayHintKind||(t.InlayHintKind={}));var Tl;(function(l){function h(p){return{value:p}}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&(u.tooltip===void 0||b.string(u.tooltip)||N.is(u.tooltip))&&(u.location===void 0||c.is(u.location))&&(u.command===void 0||Oe.is(u.command))}l.is=v})(Tl=t.InlayHintLabelPart||(t.InlayHintLabelPart={}));var uh;(function(l){function h(p,u,E){var I={position:p,label:u};return E!==void 0&&(I.kind=E),I}l.create=h;function v(p){var u=p;return b.objectLiteral(u)&&o.is(u.position)&&(b.string(u.label)||b.typedArray(u.label,Tl.is))&&(u.kind===void 0||vl.is(u.kind))&&u.textEdits===void 0||b.typedArray(u.textEdits,ke.is)&&(u.tooltip===void 0||b.string(u.tooltip)||N.is(u.tooltip))&&(u.paddingLeft===void 0||b.boolean(u.paddingLeft))&&(u.paddingRight===void 0||b.boolean(u.paddingRight))}l.is=v})(uh=t.InlayHint||(t.InlayHint={}));var ch;(function(l){function h(v){var p=v;return b.objectLiteral(p)&&r.is(p.uri)&&b.string(p.name)}l.is=h})(ch=t.WorkspaceFolder||(t.WorkspaceFolder={})),t.EOL=[`
`,`\r
`,"\r"];var lh;(function(l){function h(E,I,B,Le){return new dh(E,I,B,Le)}l.create=h;function v(E){var I=E;return!!(b.defined(I)&&b.string(I.uri)&&(b.undefined(I.languageId)||b.string(I.languageId))&&b.uinteger(I.lineCount)&&b.func(I.getText)&&b.func(I.positionAt)&&b.func(I.offsetAt))}l.is=v;function p(E,I){for(var B=E.getText(),Le=u(I,function(Xn,Ci){var gl=Xn.range.start.line-Ci.range.start.line;return gl===0?Xn.range.start.character-Ci.range.start.character:gl}),be=B.length,St=Le.length-1;St>=0;St--){var Lt=Le[St],zt=E.offsetAt(Lt.range.start),ee=E.offsetAt(Lt.range.end);if(ee<=be)B=B.substring(0,zt)+Lt.newText+B.substring(ee,B.length);else throw new Error("Overlapping edit");be=zt}return B}l.applyEdits=p;function u(E,I){if(E.length<=1)return E;var B=E.length/2|0,Le=E.slice(0,B),be=E.slice(B);u(Le,I),u(be,I);for(var St=0,Lt=0,zt=0;St<Le.length&&Lt<be.length;){var ee=I(Le[St],be[Lt]);ee<=0?E[zt++]=Le[St++]:E[zt++]=be[Lt++]}for(;St<Le.length;)E[zt++]=Le[St++];for(;Lt<be.length;)E[zt++]=be[Lt++];return E}})(lh=t.TextDocument||(t.TextDocument={}));var dh=function(){function l(h,v,p,u){this._uri=h,this._languageId=v,this._version=p,this._content=u,this._lineOffsets=void 0}return Object.defineProperty(l.prototype,"uri",{get:function(){return this._uri},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"languageId",{get:function(){return this._languageId},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"version",{get:function(){return this._version},enumerable:!1,configurable:!0}),l.prototype.getText=function(h){if(h){var v=this.offsetAt(h.start),p=this.offsetAt(h.end);return this._content.substring(v,p)}return this._content},l.prototype.update=function(h,v){this._content=h.text,this._version=v,this._lineOffsets=void 0},l.prototype.getLineOffsets=function(){if(this._lineOffsets===void 0){for(var h=[],v=this._content,p=!0,u=0;u<v.length;u++){p&&(h.push(u),p=!1);var E=v.charAt(u);p=E==="\r"||E===`
`,E==="\r"&&u+1<v.length&&v.charAt(u+1)===`
`&&u++}p&&v.length>0&&h.push(v.length),this._lineOffsets=h}return this._lineOffsets},l.prototype.positionAt=function(h){h=Math.max(Math.min(h,this._content.length),0);var v=this.getLineOffsets(),p=0,u=v.length;if(u===0)return o.create(0,h);for(;p<u;){var E=Math.floor((p+u)/2);v[E]>h?u=E:p=E+1}var I=p-1;return o.create(I,h-v[I])},l.prototype.offsetAt=function(h){var v=this.getLineOffsets();if(h.line>=v.length)return this._content.length;if(h.line<0)return 0;var p=v[h.line],u=h.line+1<v.length?v[h.line+1]:this._content.length;return Math.max(Math.min(p+h.character,u),p)},Object.defineProperty(l.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!1,configurable:!0}),l}(),b;(function(l){var h=Object.prototype.toString;function v(ee){return typeof ee!="undefined"}l.defined=v;function p(ee){return typeof ee=="undefined"}l.undefined=p;function u(ee){return ee===!0||ee===!1}l.boolean=u;function E(ee){return h.call(ee)==="[object String]"}l.string=E;function I(ee){return h.call(ee)==="[object Number]"}l.number=I;function B(ee,Xn,Ci){return h.call(ee)==="[object Number]"&&Xn<=ee&&ee<=Ci}l.numberRange=B;function Le(ee){return h.call(ee)==="[object Number]"&&-2147483648<=ee&&ee<=2147483647}l.integer=Le;function be(ee){return h.call(ee)==="[object Number]"&&0<=ee&&ee<=2147483647}l.uinteger=be;function St(ee){return h.call(ee)==="[object Function]"}l.func=St;function Lt(ee){return ee!==null&&typeof ee=="object"}l.objectLiteral=Lt;function zt(ee,Xn){return Array.isArray(ee)&&ee.every(Xn)}l.typedArray=zt})(b||(b={}))})});var Jc=_(on=>{"use strict";Object.defineProperty(on,"__esModule",{value:!0});on.CompletionItemKind=on.FileChangeTypeKind=on.InsertTextFormat=void 0;var rI=Gm();Object.defineProperty(on,"InsertTextFormat",{enumerable:!0,get:function(){return rI.InsertTextFormat}});on.FileChangeTypeKind={Created:1,Changed:2,Deleted:3};var iI;(function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25})(iI=on.CompletionItemKind||(on.CompletionItemKind={}))});var Qm=_(Xc=>{"use strict";Object.defineProperty(Xc,"__esModule",{value:!0});var Um=class{constructor(t){this.getStartOfToken=()=>this._start,this.getCurrentPosition=()=>this._pos,this.eol=()=>this._sourceText.length===this._pos,this.sol=()=>this._pos===0,this.peek=()=>this._sourceText.charAt(this._pos)||null,this.next=()=>{let n=this._sourceText.charAt(this._pos);return this._pos++,n},this.eat=n=>{if(this._testNextCharacter(n))return this._start=this._pos,this._pos++,this._sourceText.charAt(this._pos-1)},this.eatWhile=n=>{let r=this._testNextCharacter(n),i=!1;for(r&&(i=r,this._start=this._pos);r;)this._pos++,r=this._testNextCharacter(n),i=!0;return i},this.eatSpace=()=>this.eatWhile(/[\s\u00a0]/),this.skipToEnd=()=>{this._pos=this._sourceText.length},this.skipTo=n=>{this._pos=n},this.match=(n,r=!0,i=!1)=>{let a=null,o=null;return typeof n=="string"?(o=new RegExp(n,i?"i":"g").test(this._sourceText.slice(this._pos,this._pos+n.length)),a=n):n instanceof RegExp&&(o=this._sourceText.slice(this._pos).match(n),a=o==null?void 0:o[0]),o!=null&&(typeof n=="string"||o instanceof Array&&this._sourceText.startsWith(o[0],this._pos))?(r&&(this._start=this._pos,a&&a.length&&(this._pos+=a.length)),o):!1},this.backUp=n=>{this._pos-=n},this.column=()=>this._pos,this.indentation=()=>{let n=this._sourceText.match(/\s*/),r=0;if(n&&n.length!==0){let i=n[0],a=0;for(;i.length>a;)i.charCodeAt(a)===9?r+=2:r++,a++}return r},this.current=()=>this._sourceText.slice(this._start,this._pos),this._start=0,this._pos=0,this._sourceText=t}_testNextCharacter(t){let n=this._sourceText.charAt(this._pos),r=!1;return typeof t=="string"?r=n===t:r=t instanceof RegExp?t.test(n):t(n),r}};Xc.default=Um});var Wc=_(Ot=>{"use strict";Object.defineProperty(Ot,"__esModule",{value:!0});Ot.p=Ot.t=Ot.butNot=Ot.list=Ot.opt=void 0;function aI(e){return{ofRule:e}}Ot.opt=aI;function oI(e,t){return{ofRule:e,isList:!0,separator:t}}Ot.list=oI;function sI(e,t){let n=e.match;return e.match=r=>{let i=!1;return n&&(i=n(r)),i&&t.every(a=>a.match&&!a.match(r))},e}Ot.butNot=sI;function uI(e,t){return{style:t,match:n=>n.kind===e}}Ot.t=uI;function cI(e,t){return{style:t||"punctuation",match:n=>n.kind==="Punctuation"&&n.value===e}}Ot.p=cI});var Hc=_(bn=>{"use strict";Object.defineProperty(bn,"__esModule",{value:!0});bn.ParseRules=bn.LexRules=bn.isIgnored=void 0;var S=Wc(),rt=nt(),lI=e=>e===" "||e==="	"||e===","||e===`
`||e==="\r"||e==="\uFEFF"||e==="\xA0";bn.isIgnored=lI;bn.LexRules={Name:/^[_A-Za-z][_0-9A-Za-z]*/,Punctuation:/^(?:!|\$|\(|\)|\.\.\.|:|=|&|@|\[|]|\{|\||\})/,Number:/^-?(?:0|(?:[1-9][0-9]*))(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,String:/^(?:"""(?:\\"""|[^"]|"[^"]|""[^"])*(?:""")?|"(?:[^"\\]|\\(?:"|\/|\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*"?)/,Comment:/^#.*/};bn.ParseRules={Document:[(0,S.list)("Definition")],Definition(e){switch(e.value){case"{":return"ShortQuery";case"query":return"Query";case"mutation":return"Mutation";case"subscription":return"Subscription";case"fragment":return rt.Kind.FRAGMENT_DEFINITION;case"schema":return"SchemaDef";case"scalar":return"ScalarDef";case"type":return"ObjectTypeDef";case"interface":return"InterfaceDef";case"union":return"UnionDef";case"enum":return"EnumDef";case"input":return"InputDef";case"extend":return"ExtendDef";case"directive":return"DirectiveDef"}},ShortQuery:["SelectionSet"],Query:[Xe("query"),(0,S.opt)(Ee("def")),(0,S.opt)("VariableDefinitions"),(0,S.list)("Directive"),"SelectionSet"],Mutation:[Xe("mutation"),(0,S.opt)(Ee("def")),(0,S.opt)("VariableDefinitions"),(0,S.list)("Directive"),"SelectionSet"],Subscription:[Xe("subscription"),(0,S.opt)(Ee("def")),(0,S.opt)("VariableDefinitions"),(0,S.list)("Directive"),"SelectionSet"],VariableDefinitions:[(0,S.p)("("),(0,S.list)("VariableDefinition"),(0,S.p)(")")],VariableDefinition:["Variable",(0,S.p)(":"),"Type",(0,S.opt)("DefaultValue")],Variable:[(0,S.p)("$","variable"),Ee("variable")],DefaultValue:[(0,S.p)("="),"Value"],SelectionSet:[(0,S.p)("{"),(0,S.list)("Selection"),(0,S.p)("}")],Selection(e,t){return e.value==="..."?t.match(/[\s\u00a0,]*(on\b|@|{)/,!1)?"InlineFragment":"FragmentSpread":t.match(/[\s\u00a0,]*:/,!1)?"AliasedField":"Field"},AliasedField:[Ee("property"),(0,S.p)(":"),Ee("qualifier"),(0,S.opt)("Arguments"),(0,S.list)("Directive"),(0,S.opt)("SelectionSet")],Field:[Ee("property"),(0,S.opt)("Arguments"),(0,S.list)("Directive"),(0,S.opt)("SelectionSet")],Arguments:[(0,S.p)("("),(0,S.list)("Argument"),(0,S.p)(")")],Argument:[Ee("attribute"),(0,S.p)(":"),"Value"],FragmentSpread:[(0,S.p)("..."),Ee("def"),(0,S.list)("Directive")],InlineFragment:[(0,S.p)("..."),(0,S.opt)("TypeCondition"),(0,S.list)("Directive"),"SelectionSet"],FragmentDefinition:[Xe("fragment"),(0,S.opt)((0,S.butNot)(Ee("def"),[Xe("on")])),"TypeCondition",(0,S.list)("Directive"),"SelectionSet"],TypeCondition:[Xe("on"),"NamedType"],Value(e){switch(e.kind){case"Number":return"NumberValue";case"String":return"StringValue";case"Punctuation":switch(e.value){case"[":return"ListValue";case"{":return"ObjectValue";case"$":return"Variable";case"&":return"NamedType"}return null;case"Name":switch(e.value){case"true":case"false":return"BooleanValue"}return e.value==="null"?"NullValue":"EnumValue"}},NumberValue:[(0,S.t)("Number","number")],StringValue:[{style:"string",match:e=>e.kind==="String",update(e,t){t.value.startsWith('"""')&&(e.inBlockstring=!t.value.slice(3).endsWith('"""'))}}],BooleanValue:[(0,S.t)("Name","builtin")],NullValue:[(0,S.t)("Name","keyword")],EnumValue:[Ee("string-2")],ListValue:[(0,S.p)("["),(0,S.list)("Value"),(0,S.p)("]")],ObjectValue:[(0,S.p)("{"),(0,S.list)("ObjectField"),(0,S.p)("}")],ObjectField:[Ee("attribute"),(0,S.p)(":"),"Value"],Type(e){return e.value==="["?"ListType":"NonNullType"},ListType:[(0,S.p)("["),"Type",(0,S.p)("]"),(0,S.opt)((0,S.p)("!"))],NonNullType:["NamedType",(0,S.opt)((0,S.p)("!"))],NamedType:[dI("atom")],Directive:[(0,S.p)("@","meta"),Ee("meta"),(0,S.opt)("Arguments")],DirectiveDef:[Xe("directive"),(0,S.p)("@","meta"),Ee("meta"),(0,S.opt)("ArgumentsDef"),Xe("on"),(0,S.list)("DirectiveLocation",(0,S.p)("|"))],InterfaceDef:[Xe("interface"),Ee("atom"),(0,S.opt)("Implements"),(0,S.list)("Directive"),(0,S.p)("{"),(0,S.list)("FieldDef"),(0,S.p)("}")],Implements:[Xe("implements"),(0,S.list)("NamedType",(0,S.p)("&"))],DirectiveLocation:[Ee("string-2")],SchemaDef:[Xe("schema"),(0,S.list)("Directive"),(0,S.p)("{"),(0,S.list)("OperationTypeDef"),(0,S.p)("}")],OperationTypeDef:[Ee("keyword"),(0,S.p)(":"),Ee("atom")],ScalarDef:[Xe("scalar"),Ee("atom"),(0,S.list)("Directive")],ObjectTypeDef:[Xe("type"),Ee("atom"),(0,S.opt)("Implements"),(0,S.list)("Directive"),(0,S.p)("{"),(0,S.list)("FieldDef"),(0,S.p)("}")],FieldDef:[Ee("property"),(0,S.opt)("ArgumentsDef"),(0,S.p)(":"),"Type",(0,S.list)("Directive")],ArgumentsDef:[(0,S.p)("("),(0,S.list)("InputValueDef"),(0,S.p)(")")],InputValueDef:[Ee("attribute"),(0,S.p)(":"),"Type",(0,S.opt)("DefaultValue"),(0,S.list)("Directive")],UnionDef:[Xe("union"),Ee("atom"),(0,S.list)("Directive"),(0,S.p)("="),(0,S.list)("UnionMember",(0,S.p)("|"))],UnionMember:["NamedType"],EnumDef:[Xe("enum"),Ee("atom"),(0,S.list)("Directive"),(0,S.p)("{"),(0,S.list)("EnumValueDef"),(0,S.p)("}")],EnumValueDef:[Ee("string-2"),(0,S.list)("Directive")],InputDef:[Xe("input"),Ee("atom"),(0,S.list)("Directive"),(0,S.p)("{"),(0,S.list)("InputValueDef"),(0,S.p)("}")],ExtendDef:[Xe("extend"),"ExtensionDefinition"],ExtensionDefinition(e){switch(e.value){case"schema":return rt.Kind.SCHEMA_EXTENSION;case"scalar":return rt.Kind.SCALAR_TYPE_EXTENSION;case"type":return rt.Kind.OBJECT_TYPE_EXTENSION;case"interface":return rt.Kind.INTERFACE_TYPE_EXTENSION;case"union":return rt.Kind.UNION_TYPE_EXTENSION;case"enum":return rt.Kind.ENUM_TYPE_EXTENSION;case"input":return rt.Kind.INPUT_OBJECT_TYPE_EXTENSION}},[rt.Kind.SCHEMA_EXTENSION]:["SchemaDef"],[rt.Kind.SCALAR_TYPE_EXTENSION]:["ScalarDef"],[rt.Kind.OBJECT_TYPE_EXTENSION]:["ObjectTypeDef"],[rt.Kind.INTERFACE_TYPE_EXTENSION]:["InterfaceDef"],[rt.Kind.UNION_TYPE_EXTENSION]:["UnionDef"],[rt.Kind.ENUM_TYPE_EXTENSION]:["EnumDef"],[rt.Kind.INPUT_OBJECT_TYPE_EXTENSION]:["InputDef"]};function Xe(e){return{style:"keyword",match:t=>t.kind==="Name"&&t.value===e}}function Ee(e){return{style:e,match:t=>t.kind==="Name",update(t,n){t.name=n.value}}}function dI(e){return{style:e,match:t=>t.kind==="Name",update(t,n){var r;((r=t.prevState)===null||r===void 0?void 0:r.prevState)&&(t.name=n.value,t.prevState.prevState.type=n.value)}}}});var $m=_(nl=>{"use strict";Object.defineProperty(nl,"__esModule",{value:!0});var zc=Hc(),pI=nt();function fI(e={eatWhitespace:t=>t.eatWhile(zc.isIgnored),lexRules:zc.LexRules,parseRules:zc.ParseRules,editorConfig:{}}){return{startState(){let t={level:0,step:0,name:null,kind:null,type:null,rule:null,needsSeparator:!1,prevState:null};return bi(e.parseRules,t,pI.Kind.DOCUMENT),t},token(t,n){return mI(t,n,e)}}}nl.default=fI;function mI(e,t,n){var r;if(t.inBlockstring)return e.match(/.*"""/)?(t.inBlockstring=!1,"string"):(e.skipToEnd(),"string");let{lexRules:i,parseRules:a,eatWhitespace:o,editorConfig:s}=n;if(t.rule&&t.rule.length===0?el(t):t.needsAdvance&&(t.needsAdvance=!1,tl(t,!0)),e.sol()){let f=(s==null?void 0:s.tabSize)||2;t.indentLevel=Math.floor(e.indentation()/f)}if(o(e))return"ws";let c=hI(i,e);if(!c)return e.match(/\S+/)||e.match(/\s/),bi(Zc,t,"Invalid"),"invalidchar";if(c.kind==="Comment")return bi(Zc,t,"Comment"),"comment";let d=Km({},t);if(c.kind==="Punctuation"){if(/^[{([]/.test(c.value))t.indentLevel!==void 0&&(t.levels=(t.levels||[]).concat(t.indentLevel+1));else if(/^[})\]]/.test(c.value)){let f=t.levels=(t.levels||[]).slice(0,-1);t.indentLevel&&f.length>0&&f.at(-1)<t.indentLevel&&(t.indentLevel=f.at(-1))}}for(;t.rule;){let f=typeof t.rule=="function"?t.step===0?t.rule(c,e):null:t.rule[t.step];if(t.needsSeparator&&(f=f==null?void 0:f.separator),f){if(f.ofRule&&(f=f.ofRule),typeof f=="string"){bi(a,t,f);continue}if((r=f.match)===null||r===void 0?void 0:r.call(f,c))return f.update&&f.update(t,c),c.kind==="Punctuation"?tl(t,!0):t.needsAdvance=!0,f.style}yI(t)}return Km(t,d),bi(Zc,t,"Invalid"),"invalidchar"}function Km(e,t){let n=Object.keys(t);for(let r=0;r<n.length;r++)e[n[r]]=t[n[r]];return e}var Zc={Invalid:[],Comment:[]};function bi(e,t,n){if(!e[n])throw new TypeError("Unknown rule: "+n);t.prevState=Object.assign({},t),t.kind=n,t.name=null,t.type=null,t.rule=e[n],t.step=0,t.needsSeparator=!1}function el(e){!e.prevState||(e.kind=e.prevState.kind,e.name=e.prevState.name,e.type=e.prevState.type,e.rule=e.prevState.rule,e.step=e.prevState.step,e.needsSeparator=e.prevState.needsSeparator,e.prevState=e.prevState.prevState)}function tl(e,t){var n;if(xm(e)&&e.rule){let r=e.rule[e.step];if(r.separator){let{separator:i}=r;if(e.needsSeparator=!e.needsSeparator,!e.needsSeparator&&i.ofRule)return}if(t)return}for(e.needsSeparator=!1,e.step++;e.rule&&!(Array.isArray(e.rule)&&e.step<e.rule.length);)el(e),e.rule&&(xm(e)?((n=e.rule)===null||n===void 0?void 0:n[e.step].separator)&&(e.needsSeparator=!e.needsSeparator):(e.needsSeparator=!1,e.step++))}function xm(e){let t=Array.isArray(e.rule)&&typeof e.rule[e.step]!="string"&&e.rule[e.step];return t&&t.isList}function yI(e){for(;e.rule&&!(Array.isArray(e.rule)&&e.rule[e.step].ofRule);)el(e);e.rule&&tl(e,!1)}function hI(e,t){let n=Object.keys(e);for(let r=0;r<n.length;r++){let i=t.match(e[n[r]]);if(i&&i instanceof Array)return{kind:n[r],value:i[0]}}}});var Bm=_($n=>{"use strict";Object.defineProperty($n,"__esModule",{value:!0});$n.RuleKinds=$n.AdditionalRuleKinds=void 0;var vI=nt();$n.AdditionalRuleKinds={ALIASED_FIELD:"AliasedField",ARGUMENTS:"Arguments",SHORT_QUERY:"ShortQuery",QUERY:"Query",MUTATION:"Mutation",SUBSCRIPTION:"Subscription",TYPE_CONDITION:"TypeCondition",INVALID:"Invalid",COMMENT:"Comment",SCHEMA_DEF:"SchemaDef",SCALAR_DEF:"ScalarDef",OBJECT_TYPE_DEF:"ObjectTypeDef",OBJECT_VALUE:"ObjectValue",LIST_VALUE:"ListValue",INTERFACE_DEF:"InterfaceDef",UNION_DEF:"UnionDef",ENUM_DEF:"EnumDef",ENUM_VALUE:"EnumValue",FIELD_DEF:"FieldDef",INPUT_DEF:"InputDef",INPUT_VALUE_DEF:"InputValueDef",ARGUMENTS_DEF:"ArgumentsDef",EXTEND_DEF:"ExtendDef",EXTENSION_DEFINITION:"ExtensionDefinition",DIRECTIVE_DEF:"DirectiveDef",IMPLEMENTS:"Implements",VARIABLE_DEFINITIONS:"VariableDefinitions",TYPE:"Type"};$n.RuleKinds=Object.assign(Object.assign({},vI.Kind),$n.AdditionalRuleKinds)});var Xa=_(me=>{"use strict";var TI=me&&me.__createBinding||(Object.create?function(e,t,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]}),gI=me&&me.__exportStar||function(e,t){for(var n in e)n!=="default"&&!Object.prototype.hasOwnProperty.call(t,n)&&TI(t,e,n)},Ym=me&&me.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(me,"__esModule",{value:!0});me.onlineParser=me.t=me.p=me.opt=me.list=me.butNot=me.isIgnored=me.ParseRules=me.LexRules=me.CharacterStream=void 0;var bI=Qm();Object.defineProperty(me,"CharacterStream",{enumerable:!0,get:function(){return Ym(bI).default}});var rl=Hc();Object.defineProperty(me,"LexRules",{enumerable:!0,get:function(){return rl.LexRules}});Object.defineProperty(me,"ParseRules",{enumerable:!0,get:function(){return rl.ParseRules}});Object.defineProperty(me,"isIgnored",{enumerable:!0,get:function(){return rl.isIgnored}});var Ei=Wc();Object.defineProperty(me,"butNot",{enumerable:!0,get:function(){return Ei.butNot}});Object.defineProperty(me,"list",{enumerable:!0,get:function(){return Ei.list}});Object.defineProperty(me,"opt",{enumerable:!0,get:function(){return Ei.opt}});Object.defineProperty(me,"p",{enumerable:!0,get:function(){return Ei.p}});Object.defineProperty(me,"t",{enumerable:!0,get:function(){return Ei.t}});var EI=$m();Object.defineProperty(me,"onlineParser",{enumerable:!0,get:function(){return Ym(EI).default}});gI(Bm(),me)});var al=_(Se=>{"use strict";Object.defineProperty(Se,"__esModule",{value:!0});Se.GraphQLDocumentMode=Se.getTypeInfo=Se.canUseDirective=Se.runOnlineParser=Se.getTokenAtPosition=Se.getFragmentDefinitions=Se.getVariableCompletions=Se.getAutocompleteSuggestions=Se.SuggestionCommand=void 0;var L=nt(),X=Jc(),R=Xa(),re=Yc();Se.SuggestionCommand={command:"editor.action.triggerSuggest",title:"Suggestions"};var _I=e=>{let t=[];if(e)try{(0,L.visit)((0,L.parse)(e),{FragmentDefinition(n){t.push(n)}})}catch{return[]}return t},NI=[L.Kind.SCHEMA_DEFINITION,L.Kind.OPERATION_TYPE_DEFINITION,L.Kind.SCALAR_TYPE_DEFINITION,L.Kind.OBJECT_TYPE_DEFINITION,L.Kind.INTERFACE_TYPE_DEFINITION,L.Kind.UNION_TYPE_DEFINITION,L.Kind.ENUM_TYPE_DEFINITION,L.Kind.INPUT_OBJECT_TYPE_DEFINITION,L.Kind.DIRECTIVE_DEFINITION,L.Kind.SCHEMA_EXTENSION,L.Kind.SCALAR_TYPE_EXTENSION,L.Kind.OBJECT_TYPE_EXTENSION,L.Kind.INTERFACE_TYPE_EXTENSION,L.Kind.UNION_TYPE_EXTENSION,L.Kind.ENUM_TYPE_EXTENSION,L.Kind.INPUT_OBJECT_TYPE_EXTENSION],OI=e=>{let t=!1;if(e)try{(0,L.visit)((0,L.parse)(e),{enter(n){if(n.kind!=="Document")return NI.includes(n.kind)?(t=!0,L.BREAK):!1}})}catch{return t}return t};function II(e,t,n,r,i,a){var o;let s=Object.assign(Object.assign({},a),{schema:e}),c=r||Xm(t,n,1),d=c.state.kind==="Invalid"?c.state.prevState:c.state,f=(a==null?void 0:a.mode)||VI(t,a==null?void 0:a.uri);if(!d)return[];let{kind:m,step:g,prevState:T}=d,O=Hm(e,c.state);if(m===R.RuleKinds.DOCUMENT)return f===vr.TYPE_SYSTEM?SI(c):LI(c);if(m===R.RuleKinds.EXTEND_DEF)return RI(c);if(((o=T==null?void 0:T.prevState)===null||o===void 0?void 0:o.kind)===R.RuleKinds.EXTENSION_DEFINITION&&d.name)return(0,re.hintList)(c,[]);if((T==null?void 0:T.kind)===L.Kind.SCALAR_TYPE_EXTENSION)return(0,re.hintList)(c,Object.values(e.getTypeMap()).filter(L.isScalarType).map(k=>({label:k.name,kind:X.CompletionItemKind.Function})));if((T==null?void 0:T.kind)===L.Kind.OBJECT_TYPE_EXTENSION)return(0,re.hintList)(c,Object.values(e.getTypeMap()).filter(k=>(0,L.isObjectType)(k)&&!k.name.startsWith("__")).map(k=>({label:k.name,kind:X.CompletionItemKind.Function})));if((T==null?void 0:T.kind)===L.Kind.INTERFACE_TYPE_EXTENSION)return(0,re.hintList)(c,Object.values(e.getTypeMap()).filter(L.isInterfaceType).map(k=>({label:k.name,kind:X.CompletionItemKind.Function})));if((T==null?void 0:T.kind)===L.Kind.UNION_TYPE_EXTENSION)return(0,re.hintList)(c,Object.values(e.getTypeMap()).filter(L.isUnionType).map(k=>({label:k.name,kind:X.CompletionItemKind.Function})));if((T==null?void 0:T.kind)===L.Kind.ENUM_TYPE_EXTENSION)return(0,re.hintList)(c,Object.values(e.getTypeMap()).filter(k=>(0,L.isEnumType)(k)&&!k.name.startsWith("__")).map(k=>({label:k.name,kind:X.CompletionItemKind.Function})));if((T==null?void 0:T.kind)===L.Kind.INPUT_OBJECT_TYPE_EXTENSION)return(0,re.hintList)(c,Object.values(e.getTypeMap()).filter(L.isInputObjectType).map(k=>({label:k.name,kind:X.CompletionItemKind.Function})));if(m===R.RuleKinds.IMPLEMENTS||m===R.RuleKinds.NAMED_TYPE&&(T==null?void 0:T.kind)===R.RuleKinds.IMPLEMENTS)return jI(c,d,e,t,O);if(m===R.RuleKinds.SELECTION_SET||m===R.RuleKinds.FIELD||m===R.RuleKinds.ALIASED_FIELD)return AI(c,O,s);if(m===R.RuleKinds.ARGUMENTS||m===R.RuleKinds.ARGUMENT&&g===0){let{argDefs:k}=O;if(k)return(0,re.hintList)(c,k.map(Y=>{var se;return{label:Y.name,insertText:Y.name+": ",command:Se.SuggestionCommand,detail:String(Y.type),documentation:(se=Y.description)!==null&&se!==void 0?se:void 0,kind:X.CompletionItemKind.Variable,type:Y.type}}))}if((m===R.RuleKinds.OBJECT_VALUE||m===R.RuleKinds.OBJECT_FIELD&&g===0)&&O.objectFieldDefs){let k=(0,re.objectValues)(O.objectFieldDefs),Y=m===R.RuleKinds.OBJECT_VALUE?X.CompletionItemKind.Value:X.CompletionItemKind.Field;return(0,re.hintList)(c,k.map(se=>{var z;return{label:se.name,detail:String(se.type),documentation:(z=se.description)!==null&&z!==void 0?z:void 0,kind:Y,type:se.type}}))}if(m===R.RuleKinds.ENUM_VALUE||m===R.RuleKinds.LIST_VALUE&&g===1||m===R.RuleKinds.OBJECT_FIELD&&g===2||m===R.RuleKinds.ARGUMENT&&g===2)return PI(c,O,t,e);if(m===R.RuleKinds.VARIABLE&&g===1){let k=(0,L.getNamedType)(O.inputType),Y=il(t,e,c);return(0,re.hintList)(c,Y.filter(se=>se.detail===(k==null?void 0:k.name)))}if(m===R.RuleKinds.TYPE_CONDITION&&g===1||m===R.RuleKinds.NAMED_TYPE&&T!=null&&T.kind===R.RuleKinds.TYPE_CONDITION)return FI(c,O,e,m);if(m===R.RuleKinds.FRAGMENT_SPREAD&&g===1)return kI(c,O,e,t,Array.isArray(i)?i:_I(i));let U=zm(d);if(f===vr.TYPE_SYSTEM&&!U.needsAdvance&&m===R.RuleKinds.NAMED_TYPE||m===R.RuleKinds.LIST_TYPE){if(U.kind===R.RuleKinds.FIELD_DEF)return(0,re.hintList)(c,Object.values(e.getTypeMap()).filter(k=>(0,L.isOutputType)(k)&&!k.name.startsWith("__")).map(k=>({label:k.name,kind:X.CompletionItemKind.Function})));if(U.kind===R.RuleKinds.INPUT_VALUE_DEF)return(0,re.hintList)(c,Object.values(e.getTypeMap()).filter(k=>(0,L.isInputType)(k)&&!k.name.startsWith("__")).map(k=>({label:k.name,kind:X.CompletionItemKind.Function})))}return m===R.RuleKinds.VARIABLE_DEFINITION&&g===2||m===R.RuleKinds.LIST_TYPE&&g===1||m===R.RuleKinds.NAMED_TYPE&&T&&(T.kind===R.RuleKinds.VARIABLE_DEFINITION||T.kind===R.RuleKinds.LIST_TYPE||T.kind===R.RuleKinds.NON_NULL_TYPE)?CI(c,e,m):m===R.RuleKinds.DIRECTIVE?qI(c,d,e,m):[]}Se.getAutocompleteSuggestions=II;var Wa=` {
  $1
}`,DI=e=>{let{type:t}=e;return(0,L.isCompositeType)(t)||(0,L.isListType)(t)&&(0,L.isCompositeType)(t.ofType)||(0,L.isNonNullType)(t)&&((0,L.isCompositeType)(t.ofType)||(0,L.isListType)(t.ofType)&&(0,L.isCompositeType)(t.ofType.ofType))?Wa:null};function SI(e){return(0,re.hintList)(e,[{label:"extend",kind:X.CompletionItemKind.Function},{label:"type",kind:X.CompletionItemKind.Function},{label:"interface",kind:X.CompletionItemKind.Function},{label:"union",kind:X.CompletionItemKind.Function},{label:"input",kind:X.CompletionItemKind.Function},{label:"scalar",kind:X.CompletionItemKind.Function},{label:"schema",kind:X.CompletionItemKind.Function}])}function LI(e){return(0,re.hintList)(e,[{label:"query",kind:X.CompletionItemKind.Function},{label:"mutation",kind:X.CompletionItemKind.Function},{label:"subscription",kind:X.CompletionItemKind.Function},{label:"fragment",kind:X.CompletionItemKind.Function},{label:"{",kind:X.CompletionItemKind.Constructor}])}function RI(e){return(0,re.hintList)(e,[{label:"type",kind:X.CompletionItemKind.Function},{label:"interface",kind:X.CompletionItemKind.Function},{label:"union",kind:X.CompletionItemKind.Function},{label:"input",kind:X.CompletionItemKind.Function},{label:"scalar",kind:X.CompletionItemKind.Function},{label:"schema",kind:X.CompletionItemKind.Function}])}function AI(e,t,n){var r;if(t.parentType){let{parentType:i}=t,a=[];return"getFields"in i&&(a=(0,re.objectValues)(i.getFields())),(0,L.isCompositeType)(i)&&a.push(L.TypeNameMetaFieldDef),i===((r=n==null?void 0:n.schema)===null||r===void 0?void 0:r.getQueryType())&&a.push(L.SchemaMetaFieldDef,L.TypeMetaFieldDef),(0,re.hintList)(e,a.map((o,s)=>{var c;let d={sortText:String(s)+o.name,label:o.name,detail:String(o.type),documentation:(c=o.description)!==null&&c!==void 0?c:void 0,deprecated:Boolean(o.deprecationReason),isDeprecated:Boolean(o.deprecationReason),deprecationReason:o.deprecationReason,kind:X.CompletionItemKind.Field,type:o.type};if(n==null?void 0:n.fillLeafsOnComplete){let f=DI(o);f&&(d.insertText=o.name+f,d.insertTextFormat=X.InsertTextFormat.Snippet,d.command=Se.SuggestionCommand)}return d}))}return[]}function PI(e,t,n,r){let i=(0,L.getNamedType)(t.inputType),a=il(n,r,e).filter(o=>o.detail===i.name);if(i instanceof L.GraphQLEnumType){let o=i.getValues();return(0,re.hintList)(e,o.map(s=>{var c;return{label:s.name,detail:String(i),documentation:(c=s.description)!==null&&c!==void 0?c:void 0,deprecated:Boolean(s.deprecationReason),isDeprecated:Boolean(s.deprecationReason),deprecationReason:s.deprecationReason,kind:X.CompletionItemKind.EnumMember,type:i}}).concat(a))}return i===L.GraphQLBoolean?(0,re.hintList)(e,a.concat([{label:"true",detail:String(L.GraphQLBoolean),documentation:"Not false.",kind:X.CompletionItemKind.Variable,type:L.GraphQLBoolean},{label:"false",detail:String(L.GraphQLBoolean),documentation:"Not true.",kind:X.CompletionItemKind.Variable,type:L.GraphQLBoolean}])):a}function jI(e,t,n,r,i){if(t.needsSeparator)return[];let a=n.getTypeMap(),o=(0,re.objectValues)(a).filter(L.isInterfaceType),s=o.map(({name:T})=>T),c=new Set;_i(r,(T,O)=>{var U,k,Y,se,z;if(O.name&&(O.kind===R.RuleKinds.INTERFACE_DEF&&!s.includes(O.name)&&c.add(O.name),O.kind===R.RuleKinds.NAMED_TYPE&&((U=O.prevState)===null||U===void 0?void 0:U.kind)===R.RuleKinds.IMPLEMENTS)){if(i.interfaceDef){if((k=i.interfaceDef)===null||k===void 0?void 0:k.getInterfaces().find(({name:Te})=>Te===O.name))return;let ke=n.getType(O.name),we=(Y=i.interfaceDef)===null||Y===void 0?void 0:Y.toConfig();i.interfaceDef=new L.GraphQLInterfaceType(Object.assign(Object.assign({},we),{interfaces:[...we.interfaces,ke||new L.GraphQLInterfaceType({name:O.name,fields:{}})]}))}else if(i.objectTypeDef){if((se=i.objectTypeDef)===null||se===void 0?void 0:se.getInterfaces().find(({name:Te})=>Te===O.name))return;let ke=n.getType(O.name),we=(z=i.objectTypeDef)===null||z===void 0?void 0:z.toConfig();i.objectTypeDef=new L.GraphQLObjectType(Object.assign(Object.assign({},we),{interfaces:[...we.interfaces,ke||new L.GraphQLInterfaceType({name:O.name,fields:{}})]}))}}});let d=i.interfaceDef||i.objectTypeDef,m=((d==null?void 0:d.getInterfaces())||[]).map(({name:T})=>T),g=o.concat([...c].map(T=>({name:T}))).filter(({name:T})=>T!==(d==null?void 0:d.name)&&!m.includes(T));return(0,re.hintList)(e,g.map(T=>{let O={label:T.name,kind:X.CompletionItemKind.Interface,type:T};return(T==null?void 0:T.description)&&(O.documentation=T.description),O}))}function FI(e,t,n,r){let i;if(t.parentType)if((0,L.isAbstractType)(t.parentType)){let a=(0,L.assertAbstractType)(t.parentType),o=n.getPossibleTypes(a),s=Object.create(null);for(let c of o)for(let d of c.getInterfaces())s[d.name]=d;i=o.concat((0,re.objectValues)(s))}else i=[t.parentType];else{let a=n.getTypeMap();i=(0,re.objectValues)(a).filter(o=>(0,L.isCompositeType)(o)&&!o.name.startsWith("__"))}return(0,re.hintList)(e,i.map(a=>{let o=(0,L.getNamedType)(a);return{label:String(a),documentation:(o==null?void 0:o.description)||"",kind:X.CompletionItemKind.Field}}))}function kI(e,t,n,r,i){if(!r)return[];let a=n.getTypeMap(),o=(0,re.getDefinitionState)(e.state),s=Jm(r);i&&i.length>0&&s.push(...i);let c=s.filter(d=>a[d.typeCondition.name.value]&&!(o&&o.kind===R.RuleKinds.FRAGMENT_DEFINITION&&o.name===d.name.value)&&(0,L.isCompositeType)(t.parentType)&&(0,L.isCompositeType)(a[d.typeCondition.name.value])&&(0,L.doTypesOverlap)(n,t.parentType,a[d.typeCondition.name.value]));return(0,re.hintList)(e,c.map(d=>({label:d.name.value,detail:String(a[d.typeCondition.name.value]),documentation:`fragment ${d.name.value} on ${d.typeCondition.name.value}`,kind:X.CompletionItemKind.Field,type:a[d.typeCondition.name.value]})))}var wI=(e,t)=>{var n,r,i,a,o,s,c,d,f,m;if(((n=e.prevState)===null||n===void 0?void 0:n.kind)===t)return e.prevState;if(((i=(r=e.prevState)===null||r===void 0?void 0:r.prevState)===null||i===void 0?void 0:i.kind)===t)return e.prevState.prevState;if(((s=(o=(a=e.prevState)===null||a===void 0?void 0:a.prevState)===null||o===void 0?void 0:o.prevState)===null||s===void 0?void 0:s.kind)===t)return e.prevState.prevState.prevState;if(((m=(f=(d=(c=e.prevState)===null||c===void 0?void 0:c.prevState)===null||d===void 0?void 0:d.prevState)===null||f===void 0?void 0:f.prevState)===null||m===void 0?void 0:m.kind)===t)return e.prevState.prevState.prevState.prevState};function il(e,t,n){let r=null,i,a=Object.create({});return _i(e,(o,s)=>{if((s==null?void 0:s.kind)===R.RuleKinds.VARIABLE&&s.name&&(r=s.name),(s==null?void 0:s.kind)===R.RuleKinds.NAMED_TYPE&&r){let c=wI(s,R.RuleKinds.TYPE);(c==null?void 0:c.type)&&(i=t.getType(c==null?void 0:c.type))}r&&i&&!a[r]&&(a[r]={detail:i.toString(),insertText:n.string==="$"?r:"$"+r,label:r,type:i,kind:X.CompletionItemKind.Variable},r=null,i=null)}),(0,re.objectValues)(a)}Se.getVariableCompletions=il;function Jm(e){let t=[];return _i(e,(n,r)=>{r.kind===R.RuleKinds.FRAGMENT_DEFINITION&&r.name&&r.type&&t.push({kind:R.RuleKinds.FRAGMENT_DEFINITION,name:{kind:L.Kind.NAME,value:r.name},selectionSet:{kind:R.RuleKinds.SELECTION_SET,selections:[]},typeCondition:{kind:R.RuleKinds.NAMED_TYPE,name:{kind:L.Kind.NAME,value:r.type}}})}),t}Se.getFragmentDefinitions=Jm;function CI(e,t,n){let r=t.getTypeMap(),i=(0,re.objectValues)(r).filter(L.isInputType);return(0,re.hintList)(e,i.map(a=>({label:a.name,documentation:a.description,kind:X.CompletionItemKind.Variable})))}function qI(e,t,n,r){var i;if((i=t.prevState)===null||i===void 0?void 0:i.kind){let a=n.getDirectives().filter(o=>Wm(t.prevState,o));return(0,re.hintList)(e,a.map(o=>({label:o.name,documentation:o.description||"",kind:X.CompletionItemKind.Function})))}return[]}function Xm(e,t,n=0){let r=null,i=null,a=null,o=_i(e,(s,c,d,f)=>{if(f===t.line&&s.getCurrentPosition()+n>=t.character+1)return r=d,i=Object.assign({},c),a=s.current(),"BREAK"});return{start:o.start,end:o.end,string:a||o.string,state:i||o.state,style:r||o.style}}Se.getTokenAtPosition=Xm;function _i(e,t){let n=e.split(`
`),r=(0,R.onlineParser)(),i=r.startState(),a="",o=new R.CharacterStream("");for(let s=0;s<n.length;s++){for(o=new R.CharacterStream(n[s]);!o.eol()&&(a=r.token(o,i),t(o,i,a,s)!=="BREAK"););t(o,i,a,s),i.kind||(i=r.startState())}return{start:o.getStartOfToken(),end:o.getCurrentPosition(),string:o.current(),state:i,style:a}}Se.runOnlineParser=_i;function Wm(e,t){if(!(e==null?void 0:e.kind))return!1;let{kind:n,prevState:r}=e,{locations:i}=t;switch(n){case R.RuleKinds.QUERY:return i.includes(L.DirectiveLocation.QUERY);case R.RuleKinds.MUTATION:return i.includes(L.DirectiveLocation.MUTATION);case R.RuleKinds.SUBSCRIPTION:return i.includes(L.DirectiveLocation.SUBSCRIPTION);case R.RuleKinds.FIELD:case R.RuleKinds.ALIASED_FIELD:return i.includes(L.DirectiveLocation.FIELD);case R.RuleKinds.FRAGMENT_DEFINITION:return i.includes(L.DirectiveLocation.FRAGMENT_DEFINITION);case R.RuleKinds.FRAGMENT_SPREAD:return i.includes(L.DirectiveLocation.FRAGMENT_SPREAD);case R.RuleKinds.INLINE_FRAGMENT:return i.includes(L.DirectiveLocation.INLINE_FRAGMENT);case R.RuleKinds.SCHEMA_DEF:return i.includes(L.DirectiveLocation.SCHEMA);case R.RuleKinds.SCALAR_DEF:return i.includes(L.DirectiveLocation.SCALAR);case R.RuleKinds.OBJECT_TYPE_DEF:return i.includes(L.DirectiveLocation.OBJECT);case R.RuleKinds.FIELD_DEF:return i.includes(L.DirectiveLocation.FIELD_DEFINITION);case R.RuleKinds.INTERFACE_DEF:return i.includes(L.DirectiveLocation.INTERFACE);case R.RuleKinds.UNION_DEF:return i.includes(L.DirectiveLocation.UNION);case R.RuleKinds.ENUM_DEF:return i.includes(L.DirectiveLocation.ENUM);case R.RuleKinds.ENUM_VALUE:return i.includes(L.DirectiveLocation.ENUM_VALUE);case R.RuleKinds.INPUT_DEF:return i.includes(L.DirectiveLocation.INPUT_OBJECT);case R.RuleKinds.INPUT_VALUE_DEF:switch(r==null?void 0:r.kind){case R.RuleKinds.ARGUMENTS_DEF:return i.includes(L.DirectiveLocation.ARGUMENT_DEFINITION);case R.RuleKinds.INPUT_DEF:return i.includes(L.DirectiveLocation.INPUT_FIELD_DEFINITION)}}return!1}Se.canUseDirective=Wm;function Hm(e,t){let n,r,i,a,o,s,c,d,f,m,g;return(0,re.forEachState)(t,T=>{var O;switch(T.kind){case R.RuleKinds.QUERY:case"ShortQuery":m=e.getQueryType();break;case R.RuleKinds.MUTATION:m=e.getMutationType();break;case R.RuleKinds.SUBSCRIPTION:m=e.getSubscriptionType();break;case R.RuleKinds.INLINE_FRAGMENT:case R.RuleKinds.FRAGMENT_DEFINITION:T.type&&(m=e.getType(T.type));break;case R.RuleKinds.FIELD:case R.RuleKinds.ALIASED_FIELD:{!m||!T.name?o=null:(o=f?(0,re.getFieldDef)(e,f,T.name):null,m=o?o.type:null);break}case R.RuleKinds.SELECTION_SET:f=(0,L.getNamedType)(m);break;case R.RuleKinds.DIRECTIVE:i=T.name?e.getDirective(T.name):null;break;case R.RuleKinds.INTERFACE_DEF:T.name&&(c=null,g=new L.GraphQLInterfaceType({name:T.name,interfaces:[],fields:{}}));break;case R.RuleKinds.OBJECT_TYPE_DEF:T.name&&(g=null,c=new L.GraphQLObjectType({name:T.name,interfaces:[],fields:{}}));break;case R.RuleKinds.ARGUMENTS:{if(T.prevState)switch(T.prevState.kind){case R.RuleKinds.FIELD:r=o&&o.args;break;case R.RuleKinds.DIRECTIVE:r=i&&i.args;break;case R.RuleKinds.ALIASED_FIELD:{let z=(O=T.prevState)===null||O===void 0?void 0:O.name;if(!z){r=null;break}let Oe=f?(0,re.getFieldDef)(e,f,z):null;if(!Oe){r=null;break}r=Oe.args;break}default:r=null;break}else r=null;break}case R.RuleKinds.ARGUMENT:if(r){for(let z=0;z<r.length;z++)if(r[z].name===T.name){n=r[z];break}}s=n==null?void 0:n.type;break;case R.RuleKinds.ENUM_VALUE:let U=(0,L.getNamedType)(s);a=U instanceof L.GraphQLEnumType?U.getValues().find(z=>z.value===T.name):null;break;case R.RuleKinds.LIST_VALUE:let k=(0,L.getNullableType)(s);s=k instanceof L.GraphQLList?k.ofType:null;break;case R.RuleKinds.OBJECT_VALUE:let Y=(0,L.getNamedType)(s);d=Y instanceof L.GraphQLInputObjectType?Y.getFields():null;break;case R.RuleKinds.OBJECT_FIELD:let se=T.name&&d?d[T.name]:null;s=se==null?void 0:se.type;break;case R.RuleKinds.NAMED_TYPE:T.name&&(m=e.getType(T.name));break}}),{argDef:n,argDefs:r,directiveDef:i,enumValue:a,fieldDef:o,inputType:s,objectFieldDefs:d,parentType:f,type:m,interfaceDef:g,objectTypeDef:c}}Se.getTypeInfo=Hm;var vr;(function(e){e.TYPE_SYSTEM="TYPE_SYSTEM",e.EXECUTABLE="EXECUTABLE"})(vr=Se.GraphQLDocumentMode||(Se.GraphQLDocumentMode={}));function VI(e,t){return(t==null?void 0:t.endsWith(".graphqls"))||OI(e)?vr.TYPE_SYSTEM:vr.EXECUTABLE}function zm(e){return e.prevState&&e.kind&&[R.RuleKinds.NAMED_TYPE,R.RuleKinds.LIST_TYPE,R.RuleKinds.TYPE,R.RuleKinds.NON_NULL_TYPE].includes(e.kind)?zm(e.prevState):e}});var ey=_((zL,Ha)=>{"use strict";function Zm(e,t){if(e!=null)return e;var n=new Error(t!==void 0?t:"Got unexpected "+e);throw n.framesToPop=1,n}Ha.exports=Zm;Ha.exports.default=Zm;Object.defineProperty(Ha.exports,"__esModule",{value:!0})});var ny=_(sn=>{"use strict";var MI=sn&&sn.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(sn,"__esModule",{value:!0});sn.getFragmentDependenciesForAST=sn.getFragmentDependencies=void 0;var ol=nt(),ty=MI(ey()),GI=(e,t)=>{if(!t)return[];let n;try{n=(0,ol.parse)(e)}catch{return[]}return(0,sn.getFragmentDependenciesForAST)(n,t)};sn.getFragmentDependencies=GI;var UI=(e,t)=>{if(!t)return[];let n=new Map,r=new Set;(0,ol.visit)(e,{FragmentDefinition(o){n.set(o.name.value,!0)},FragmentSpread(o){r.has(o.name.value)||r.add(o.name.value)}});let i=new Set;for(let o of r)!n.has(o)&&t.has(o)&&i.add((0,ty.default)(t.get(o)));let a=[];for(let o of i)(0,ol.visit)(o,{FragmentSpread(s){!r.has(s.name.value)&&t.get(s.name.value)&&(i.add((0,ty.default)(t.get(s.name.value))),r.add(s.name.value))}}),n.has(o.name.value)||a.push(o);return a};sn.getFragmentDependenciesForAST=UI});var ay=_(gr=>{"use strict";Object.defineProperty(gr,"__esModule",{value:!0});gr.getVariablesJSONSchema=gr.defaultJSONSchemaOptions=void 0;var En=nt();gr.defaultJSONSchemaOptions={useMarkdownDescription:!1};function Tr(e,t){e.push(t)}function sl(e,t){(0,En.isNonNullType)(t)?(sl(e,t.ofType),Tr(e,"!")):(0,En.isListType)(t)?(Tr(e,"["),sl(e,t.ofType),Tr(e,"]")):Tr(e,t.name)}function Xt(e,t){let n=[];return t&&Tr(n,"```graphql\n"),sl(n,e),t&&Tr(n,"\n```"),n.join("")}var ry={Int:"integer",String:"string",Float:"number",ID:"string",Boolean:"boolean",DateTime:"string"},iy=class{constructor(){this.set=new Set}mark(t){return this.set.has(t)?!1:(this.set.add(t),!0)}};function Ni(e,t){let n=!1,r=Object.create(null),i=Object.create(null);if("defaultValue"in e&&e.defaultValue!==void 0&&(r.default=e.defaultValue),(0,En.isEnumType)(e)&&(r.type="string",r.enum=e.getValues().map(a=>a.name)),(0,En.isScalarType)(e)&&ry[e.name]&&(r.type=ry[e.name]),(0,En.isListType)(e)){r.type="array";let{definition:a,definitions:o}=Ni(e.ofType,t);if(a.$ref?r.items={$ref:a.$ref}:r.items=a,o)for(let s of Object.keys(o))i[s]=o[s]}if((0,En.isNonNullType)(e)){n=!0;let{definition:a,definitions:o}=Ni(e.ofType,t);if(r=a,o)for(let s of Object.keys(o))i[s]=o[s]}if((0,En.isInputObjectType)(e)&&(r.$ref=`#/definitions/${e.name}`,t==null?void 0:t.definitionMarker.mark(e.name))){let a=e.getFields(),o={type:"object",properties:{},required:[]};e.description?(o.description=e.description+`
`+Xt(e),(t==null?void 0:t.useMarkdownDescription)&&(o.markdownDescription=e.description+`
`+Xt(e,!0))):(o.description=Xt(e),(t==null?void 0:t.useMarkdownDescription)&&(o.markdownDescription=Xt(e,!0)));for(let s of Object.keys(a)){let c=a[s],{required:d,definition:f,definitions:m}=Ni(c.type,t),{definition:g}=Ni(c,t);o.properties[s]=Object.assign(Object.assign({},f),g);let T=Xt(c.type);if(o.properties[s].description=c.description?c.description+`
`+T:T,t==null?void 0:t.useMarkdownDescription){let O=Xt(c.type,!0);o.properties[s].markdownDescription=c.description?c.description+`
`+O:O}if(d&&o.required.push(s),m)for(let[O,U]of Object.entries(m))i[O]=U}i[e.name]=o}return"description"in e&&!(0,En.isScalarType)(e)&&e.description&&!r.description?(r.description=e.description+`
`+Xt(e),(t==null?void 0:t.useMarkdownDescription)&&(r.markdownDescription=e.description+`
`+Xt(e,!0))):(r.description=Xt(e),(t==null?void 0:t.useMarkdownDescription)&&(r.markdownDescription=Xt(e,!0))),{required:n,definition:r,definitions:i}}function QI(e,t){var n;let r={$schema:"https://json-schema.org/draft/2020-12/schema",type:"object",properties:{},required:[]},i=Object.assign(Object.assign({},t),{definitionMarker:new iy});if(e)for(let[a,o]of Object.entries(e)){let{definition:s,required:c,definitions:d}=Ni(o,i);r.properties[a]=s,c&&((n=r.required)===null||n===void 0||n.push(a)),d&&(r.definitions=Object.assign(Object.assign({},r==null?void 0:r.definitions),d))}return r}gr.getVariablesJSONSchema=QI});var sy=_(br=>{"use strict";Object.defineProperty(br,"__esModule",{value:!0});br.pointToOffset=br.getASTNodeAtPosition=void 0;var KI=nt();function xI(e,t,n){let r=oy(e,n),i;return(0,KI.visit)(t,{enter(a){if(a.kind!=="Name"&&a.loc&&a.loc.start<=r&&r<=a.loc.end)i=a;else return!1},leave(a){if(a.loc&&a.loc.start<=r&&r<=a.loc.end)return!1}}),i}br.getASTNodeAtPosition=xI;function oy(e,t){let n=e.split(`
`).slice(0,t.line);return t.character+n.map(r=>r.length+1).reduce((r,i)=>r+i,0)}br.pointToOffset=oy});var uy=_(Wt=>{"use strict";Object.defineProperty(Wt,"__esModule",{value:!0});Wt.locToRange=Wt.offsetToPosition=Wt.Position=Wt.Range=void 0;var ul=class{constructor(t,n){this.containsPosition=r=>this.start.line===r.line?this.start.character<=r.character:this.end.line===r.line?this.end.character>=r.character:this.start.line<=r.line&&this.end.line>=r.line,this.start=t,this.end=n}setStart(t,n){this.start=new Oi(t,n)}setEnd(t,n){this.end=new Oi(t,n)}};Wt.Range=ul;var Oi=class{constructor(t,n){this.lessThanOrEqualTo=r=>this.line<r.line||this.line===r.line&&this.character<=r.character,this.line=t,this.character=n}setLine(t){this.line=t}setCharacter(t){this.character=t}};Wt.Position=Oi;function cl(e,t){let n=`
`,r=e.slice(0,t),i=r.split(n).length-1,a=r.lastIndexOf(n);return new Oi(i,t-a-1)}Wt.offsetToPosition=cl;function $I(e,t){let n=cl(e,t.start),r=cl(e,t.end);return new ul(n,r)}Wt.locToRange=$I});var cy=_(za=>{"use strict";Object.defineProperty(za,"__esModule",{value:!0});za.validateWithCustomRules=void 0;var Je=nt(),BI=[Je.LoneSchemaDefinitionRule,Je.UniqueOperationTypesRule,Je.UniqueTypeNamesRule,Je.UniqueEnumValueNamesRule,Je.UniqueFieldDefinitionNamesRule,Je.UniqueDirectiveNamesRule,Je.KnownTypeNamesRule,Je.KnownDirectivesRule,Je.UniqueDirectivesPerLocationRule,Je.PossibleTypeExtensionsRule,Je.UniqueArgumentNamesRule,Je.UniqueInputFieldNamesRule];function YI(e,t,n,r,i){let a=Je.specifiedRules.filter(s=>!(s===Je.NoUnusedFragmentsRule||s===Je.ExecutableDefinitionsRule||r&&s===Je.KnownFragmentNamesRule));return n&&Array.prototype.push.apply(a,n),i&&Array.prototype.push.apply(a,BI),(0,Je.validate)(e,t,a).filter(s=>{if(s.message.includes("Unknown directive")&&s.nodes){let c=s.nodes[0];if(c&&c.kind===Je.Kind.DIRECTIVE){let d=c.name.value;if(d==="arguments"||d==="argumentDefinitions")return!1}}return!0})}za.validateWithCustomRules=YI});var dl=_(Za=>{"use strict";Object.defineProperty(Za,"__esModule",{value:!0});Za.collectVariables=void 0;var ll=nt();function JI(e,t){let n=Object.create(null);for(let r of t.definitions)if(r.kind==="OperationDefinition"){let{variableDefinitions:i}=r;if(i)for(let{variable:a,type:o}of i){let s=(0,ll.typeFromAST)(e,o);s?n[a.name.value]=s:o.kind===ll.Kind.NAMED_TYPE&&o.name.value==="Float"&&(n[a.name.value]=ll.GraphQLFloat)}}return n}Za.collectVariables=JI});var fy=_(Bn=>{"use strict";Object.defineProperty(Bn,"__esModule",{value:!0});Bn.getQueryFacts=Bn.getOperationASTFacts=void 0;var ly=nt(),XI=dl();function dy(e,t){let n=t?(0,XI.collectVariables)(t,e):void 0,r=[];return(0,ly.visit)(e,{OperationDefinition(i){r.push(i)}}),{variableToType:n,operations:r}}Bn.getOperationASTFacts=dy;function py(e,t){if(!!t)try{let n=(0,ly.parse)(t);return Object.assign(Object.assign({},dy(n,e)),{documentAST:n})}catch{return}}Bn.default=py;Bn.getQueryFacts=py});var Ii=_(ce=>{"use strict";var WI=ce&&ce.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ce,"__esModule",{value:!0});ce.getQueryFacts=ce.getOperationASTFacts=ce.getOperationFacts=ce.collectVariables=ce.validateWithCustomRules=ce.offsetToPosition=ce.locToRange=ce.Range=ce.Position=ce.pointToOffset=ce.getASTNodeAtPosition=ce.getVariablesJSONSchema=ce.getFragmentDependenciesForAST=ce.getFragmentDependencies=void 0;var my=ny();Object.defineProperty(ce,"getFragmentDependencies",{enumerable:!0,get:function(){return my.getFragmentDependencies}});Object.defineProperty(ce,"getFragmentDependenciesForAST",{enumerable:!0,get:function(){return my.getFragmentDependenciesForAST}});var HI=ay();Object.defineProperty(ce,"getVariablesJSONSchema",{enumerable:!0,get:function(){return HI.getVariablesJSONSchema}});var yy=sy();Object.defineProperty(ce,"getASTNodeAtPosition",{enumerable:!0,get:function(){return yy.getASTNodeAtPosition}});Object.defineProperty(ce,"pointToOffset",{enumerable:!0,get:function(){return yy.pointToOffset}});var eo=uy();Object.defineProperty(ce,"Position",{enumerable:!0,get:function(){return eo.Position}});Object.defineProperty(ce,"Range",{enumerable:!0,get:function(){return eo.Range}});Object.defineProperty(ce,"locToRange",{enumerable:!0,get:function(){return eo.locToRange}});Object.defineProperty(ce,"offsetToPosition",{enumerable:!0,get:function(){return eo.offsetToPosition}});var zI=cy();Object.defineProperty(ce,"validateWithCustomRules",{enumerable:!0,get:function(){return zI.validateWithCustomRules}});var ZI=dl();Object.defineProperty(ce,"collectVariables",{enumerable:!0,get:function(){return ZI.collectVariables}});var pl=fy();Object.defineProperty(ce,"getOperationFacts",{enumerable:!0,get:function(){return WI(pl).default}});Object.defineProperty(ce,"getOperationASTFacts",{enumerable:!0,get:function(){return pl.getOperationASTFacts}});Object.defineProperty(ce,"getQueryFacts",{enumerable:!0,get:function(){return pl.getQueryFacts}})});var Ty=_(We=>{"use strict";var fl=We&&We.__awaiter||function(e,t,n,r){function i(a){return a instanceof n?a:new n(function(o){o(a)})}return new(n||(n=Promise))(function(a,o){function s(f){try{d(r.next(f))}catch(m){o(m)}}function c(f){try{d(r.throw(f))}catch(m){o(m)}}function d(f){f.done?a(f.value):i(f.value).then(s,c)}d((r=r.apply(e,t||[])).next())})};Object.defineProperty(We,"__esModule",{value:!0});We.getDefinitionQueryResultForDefinitionNode=We.getDefinitionQueryResultForFragmentSpread=We.getDefinitionQueryResultForField=We.getDefinitionQueryResultForNamedType=We.LANGUAGE=void 0;var hy=Ii();We.LANGUAGE="GraphQL";function to(e,t){if(!e)throw new Error(t)}function Er(e,t){let n=t.loc;return to(n,"Expected ASTNode to have a location."),(0,hy.locToRange)(e,n)}function ml(e,t){let n=t.loc;return to(n,"Expected ASTNode to have a location."),(0,hy.offsetToPosition)(e,n.start)}function eD(e,t,n){return fl(this,void 0,void 0,function*(){let r=t.name.value,i=n.filter(({definition:o})=>o.name&&o.name.value===r);if(i.length===0)throw new Error(`Definition not found for GraphQL type ${r}`);let a=i.map(({filePath:o,content:s,definition:c})=>iD(o||"",s,c));return{definitions:a,queryRange:a.map(o=>Er(e,t))}})}We.getDefinitionQueryResultForNamedType=eD;function tD(e,t,n){var r;return fl(this,void 0,void 0,function*(){let i=n.filter(({definition:o})=>o.name&&o.name.value===t);if(i.length===0)throw new Error(`Definition not found for GraphQL type ${t}`);let a=[];for(let{filePath:o,content:s,definition:c}of i){let d=(r=c.fields)===null||r===void 0?void 0:r.find(f=>f.name.value===e);d!=null&&a.push(aD(o||"",s,d))}return{definitions:a,queryRange:[]}})}We.getDefinitionQueryResultForField=tD;function nD(e,t,n){return fl(this,void 0,void 0,function*(){let r=t.name.value,i=n.filter(({definition:o})=>o.name.value===r);if(i.length===0)throw new Error(`Definition not found for GraphQL fragment ${r}`);let a=i.map(({filePath:o,content:s,definition:c})=>vy(o||"",s,c));return{definitions:a,queryRange:a.map(o=>Er(e,t))}})}We.getDefinitionQueryResultForFragmentSpread=nD;function rD(e,t,n){return{definitions:[vy(e,t,n)],queryRange:n.name?[Er(t,n.name)]:[]}}We.getDefinitionQueryResultForDefinitionNode=rD;function vy(e,t,n){let{name:r}=n;if(!r)throw new Error("Expected ASTNode to have a Name.");return{path:e,position:ml(t,n),range:Er(t,n),name:r.value||"",language:We.LANGUAGE,projectRoot:e}}function iD(e,t,n){let{name:r}=n;return to(r,"Expected ASTNode to have a Name."),{path:e,position:ml(t,n),range:Er(t,n),name:r.value||"",language:We.LANGUAGE,projectRoot:e}}function aD(e,t,n){let{name:r}=n;return to(r,"Expected ASTNode to have a Name."),{path:e,position:ml(t,n),range:Er(t,n),name:r.value||"",language:We.LANGUAGE,projectRoot:e}}});var Ny=_(xe=>{"use strict";Object.defineProperty(xe,"__esModule",{value:!0});xe.getRange=xe.validateQuery=xe.getDiagnostics=xe.DIAGNOSTIC_SEVERITY=xe.SEVERITY=void 0;var Di=nt(),gy=Xa(),Yn=Ii();xe.SEVERITY={Error:"Error",Warning:"Warning",Information:"Information",Hint:"Hint"};xe.DIAGNOSTIC_SEVERITY={[xe.SEVERITY.Error]:1,[xe.SEVERITY.Warning]:2,[xe.SEVERITY.Information]:3,[xe.SEVERITY.Hint]:4};var no=(e,t)=>{if(!e)throw new Error(t)};function oD(e,t=null,n,r,i){var a,o;let s=null,c="";i&&(c=typeof i=="string"?i:i.reduce((f,m)=>f+(0,Di.print)(m)+`

`,""));let d=c?`${e}

${c}`:e;try{s=(0,Di.parse)(d)}catch(f){if(f instanceof Di.GraphQLError){let m=_y((o=(a=f.locations)===null||a===void 0?void 0:a[0])!==null&&o!==void 0?o:{line:0,column:0},d);return[{severity:xe.DIAGNOSTIC_SEVERITY.Error,message:f.message,source:"GraphQL: Syntax",range:m}]}throw f}return by(s,t,n,r)}xe.getDiagnostics=oD;function by(e,t=null,n,r){if(!t)return[];let i=(0,Yn.validateWithCustomRules)(t,e,n,r).flatMap(o=>Ey(o,xe.DIAGNOSTIC_SEVERITY.Error,"Validation")),a=(0,Di.validate)(t,e,[Di.NoDeprecatedCustomRule]).flatMap(o=>Ey(o,xe.DIAGNOSTIC_SEVERITY.Warning,"Deprecation"));return i.concat(a)}xe.validateQuery=by;function Ey(e,t,n){if(!e.nodes)return[];let r=[];for(let[i,a]of e.nodes.entries()){let o=a.kind!=="Variable"&&"name"in a&&a.name!==void 0?a.name:"variable"in a&&a.variable!==void 0?a.variable:a;if(o){no(e.locations,"GraphQL validation error requires locations.");let s=e.locations[i],c=sD(o),d=s.column+(c.end-c.start);r.push({source:`GraphQL: ${n}`,message:e.message,severity:t,range:new Yn.Range(new Yn.Position(s.line-1,s.column-1),new Yn.Position(s.line-1,d))})}}return r}function _y(e,t){let n=(0,gy.onlineParser)(),r=n.startState(),i=t.split(`
`);no(i.length>=e.line,"Query text must have more lines than where the error happened");let a=null;for(let d=0;d<e.line;d++)for(a=new gy.CharacterStream(i[d]);!a.eol()&&n.token(a,r)!=="invalidchar";);no(a,"Expected Parser stream to be available.");let o=e.line-1,s=a.getStartOfToken(),c=a.getCurrentPosition();return new Yn.Range(new Yn.Position(o,s),new Yn.Position(o,c))}xe.getRange=_y;function sD(e){let n=e.loc;return no(n,"Expected ASTNode to have a location."),n}});var Iy=_(ro=>{"use strict";Object.defineProperty(ro,"__esModule",{value:!0});ro.getOutline=void 0;var yl=nt(),Oy=Ii(),{INLINE_FRAGMENT:uD}=yl.Kind;function cD(e){let t;try{t=(0,yl.parse)(e)}catch{return null}let n=lD(e);return{outlineTrees:(0,yl.visit)(t,{leave(i){return n!==void 0&&i.kind in n?n[i.kind](i):null}})}}ro.getOutline=cD;function lD(e){let t=n=>({representativeName:n.name,startPosition:(0,Oy.offsetToPosition)(e,n.loc.start),endPosition:(0,Oy.offsetToPosition)(e,n.loc.end),kind:n.kind,children:n.selectionSet||n.fields||n.values||n.arguments||[]});return{Field:n=>{let r=n.alias?[_e("plain",n.alias),_e("plain",": ")]:[];return r.push(_e("plain",n.name)),Object.assign({tokenizedText:r},t(n))},OperationDefinition:n=>Object.assign({tokenizedText:[_e("keyword",n.operation),_e("whitespace"," "),_e("class-name",n.name)]},t(n)),Document:n=>n.definitions,SelectionSet:n=>dD(n.selections,r=>r.kind===uD?r.selectionSet:r),Name:n=>n.value,FragmentDefinition:n=>Object.assign({tokenizedText:[_e("keyword","fragment"),_e("whitespace"," "),_e("class-name",n.name)]},t(n)),InterfaceTypeDefinition:n=>Object.assign({tokenizedText:[_e("keyword","interface"),_e("whitespace"," "),_e("class-name",n.name)]},t(n)),EnumTypeDefinition:n=>Object.assign({tokenizedText:[_e("keyword","enum"),_e("whitespace"," "),_e("class-name",n.name)]},t(n)),EnumValueDefinition:n=>Object.assign({tokenizedText:[_e("plain",n.name)]},t(n)),ObjectTypeDefinition:n=>Object.assign({tokenizedText:[_e("keyword","type"),_e("whitespace"," "),_e("class-name",n.name)]},t(n)),InputObjectTypeDefinition:n=>Object.assign({tokenizedText:[_e("keyword","input"),_e("whitespace"," "),_e("class-name",n.name)]},t(n)),FragmentSpread:n=>Object.assign({tokenizedText:[_e("plain","..."),_e("class-name",n.name)]},t(n)),InputValueDefinition:n=>Object.assign({tokenizedText:[_e("plain",n.name)]},t(n)),FieldDefinition:n=>Object.assign({tokenizedText:[_e("plain",n.name)]},t(n)),InlineFragment:n=>n.selectionSet}}function _e(e,t){return{kind:e,value:t}}function dD(e,t){let n=[];for(let r=0;r<e.length;r++){let i=t(e[r],r);Array.isArray(i)?n.push(...i):n.push(i)}return n}});var Py=_(io=>{"use strict";Object.defineProperty(io,"__esModule",{value:!0});io.getHoverInformation=void 0;var Dy=nt(),Sy=al();function pD(e,t,n,r,i){let a=r||(0,Sy.getTokenAtPosition)(t,n);if(!e||!a||!a.state)return"";let{kind:o,step:s}=a.state,c=(0,Sy.getTypeInfo)(e,a.state),d=Object.assign(Object.assign({},i),{schema:e});if(o==="Field"&&s===0&&c.fieldDef||o==="AliasedField"&&s===2&&c.fieldDef){let f=[];return Si(f,d),fD(f,c,d),Li(f,d),Ri(f,d,c.fieldDef),f.join("").trim()}if(o==="Directive"&&s===1&&c.directiveDef){let f=[];return Si(f,d),Ry(f,c,d),Li(f,d),Ri(f,d,c.directiveDef),f.join("").trim()}if(o==="Argument"&&s===0&&c.argDef){let f=[];return Si(f,d),mD(f,c,d),Li(f,d),Ri(f,d,c.argDef),f.join("").trim()}if(o==="EnumValue"&&c.enumValue&&"description"in c.enumValue){let f=[];return Si(f,d),yD(f,c,d),Li(f,d),Ri(f,d,c.enumValue),f.join("").trim()}if(o==="NamedType"&&c.type&&"description"in c.type){let f=[];return Si(f,d),_r(f,c,d,c.type),Li(f,d),Ri(f,d,c.type),f.join("").trim()}return""}io.getHoverInformation=pD;function Si(e,t){t.useMarkdown&&qe(e,"```graphql\n")}function Li(e,t){t.useMarkdown&&qe(e,"\n```")}function fD(e,t,n){Ly(e,t,n),Ay(e,t,n,t.type)}function Ly(e,t,n){if(!t.fieldDef)return;let r=t.fieldDef.name;r.slice(0,2)!=="__"&&(_r(e,t,n,t.parentType),qe(e,".")),qe(e,r)}function Ry(e,t,n){if(!t.directiveDef)return;let r="@"+t.directiveDef.name;qe(e,r)}function mD(e,t,n){if(t.directiveDef?Ry(e,t,n):t.fieldDef&&Ly(e,t,n),!t.argDef)return;let{name:r}=t.argDef;qe(e,"("),qe(e,r),Ay(e,t,n,t.inputType),qe(e,")")}function Ay(e,t,n,r){qe(e,": "),_r(e,t,n,r)}function yD(e,t,n){if(!t.enumValue)return;let{name:r}=t.enumValue;_r(e,t,n,t.inputType),qe(e,"."),qe(e,r)}function _r(e,t,n,r){!r||(r instanceof Dy.GraphQLNonNull?(_r(e,t,n,r.ofType),qe(e,"!")):r instanceof Dy.GraphQLList?(qe(e,"["),_r(e,t,n,r.ofType),qe(e,"]")):qe(e,r.name))}function Ri(e,t,n){if(!n)return;let r=typeof n.description=="string"?n.description:null;r&&(qe(e,`

`),qe(e,r)),hD(e,t,n)}function hD(e,t,n){if(!n)return;let r=n.deprecationReason||null;!r||(qe(e,`

`),qe(e,"Deprecated: "),qe(e,r))}function qe(e,t){e.push(t)}});var jy=_(st=>{"use strict";var vD=st&&st.__createBinding||(Object.create?function(e,t,n,r){r===void 0&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]}),ao=st&&st.__exportStar||function(e,t){for(var n in e)n!=="default"&&!Object.prototype.hasOwnProperty.call(t,n)&&vD(t,e,n)};Object.defineProperty(st,"__esModule",{value:!0});st.getHoverInformation=st.getOutline=void 0;ao(Yc(),st);ao(al(),st);ao(Ty(),st);ao(Ny(),st);var TD=Iy();Object.defineProperty(st,"getOutline",{enumerable:!0,get:function(){return TD.getOutline}});var gD=Py();Object.defineProperty(st,"getHoverInformation",{enumerable:!0,get:function(){return gD.getHoverInformation}})});var ky=_(A=>{"use strict";Object.defineProperty(A,"__esModule",{value:!0});A.Range=A.validateWithCustomRules=A.collectVariables=A.Position=A.pointToOffset=A.offsetToPosition=A.getVariablesJSONSchema=A.getQueryFacts=A.getOperationFacts=A.getOperationASTFacts=A.getFragmentDependenciesForAST=A.getFragmentDependencies=A.getASTNodeAtPosition=A.FileChangeTypeKind=A.CompletionItemKind=A.opt=A.t=A.list=A.p=A.isIgnored=A.LexRules=A.RuleKinds=A.CharacterStream=A.ParseRules=A.onlineParser=A.validateQuery=A.SuggestionCommand=A.canUseDirective=A.DIAGNOSTIC_SEVERITY=A.SEVERITY=A.getVariableCompletions=A.getTypeInfo=A.getTokenAtPosition=A.getRange=A.getOutline=A.getHoverInformation=A.getFragmentDefinitions=A.getFieldDef=A.getDiagnostics=A.getDefinitionState=A.getDefinitionQueryResultForField=A.getDefinitionQueryResultForNamedType=A.getDefinitionQueryResultForFragmentSpread=A.getDefinitionQueryResultForDefinitionNode=A.getAutocompleteSuggestions=void 0;var Ve=jy();Object.defineProperty(A,"getAutocompleteSuggestions",{enumerable:!0,get:function(){return Ve.getAutocompleteSuggestions}});Object.defineProperty(A,"getDefinitionQueryResultForDefinitionNode",{enumerable:!0,get:function(){return Ve.getDefinitionQueryResultForDefinitionNode}});Object.defineProperty(A,"getDefinitionQueryResultForFragmentSpread",{enumerable:!0,get:function(){return Ve.getDefinitionQueryResultForFragmentSpread}});Object.defineProperty(A,"getDefinitionQueryResultForNamedType",{enumerable:!0,get:function(){return Ve.getDefinitionQueryResultForNamedType}});Object.defineProperty(A,"getDefinitionQueryResultForField",{enumerable:!0,get:function(){return Ve.getDefinitionQueryResultForField}});Object.defineProperty(A,"getDefinitionState",{enumerable:!0,get:function(){return Ve.getDefinitionState}});Object.defineProperty(A,"getDiagnostics",{enumerable:!0,get:function(){return Ve.getDiagnostics}});Object.defineProperty(A,"getFieldDef",{enumerable:!0,get:function(){return Ve.getFieldDef}});Object.defineProperty(A,"getFragmentDefinitions",{enumerable:!0,get:function(){return Ve.getFragmentDefinitions}});Object.defineProperty(A,"getHoverInformation",{enumerable:!0,get:function(){return Ve.getHoverInformation}});Object.defineProperty(A,"getOutline",{enumerable:!0,get:function(){return Ve.getOutline}});Object.defineProperty(A,"getRange",{enumerable:!0,get:function(){return Ve.getRange}});Object.defineProperty(A,"getTokenAtPosition",{enumerable:!0,get:function(){return Ve.getTokenAtPosition}});Object.defineProperty(A,"getTypeInfo",{enumerable:!0,get:function(){return Ve.getTypeInfo}});Object.defineProperty(A,"getVariableCompletions",{enumerable:!0,get:function(){return Ve.getVariableCompletions}});Object.defineProperty(A,"SEVERITY",{enumerable:!0,get:function(){return Ve.SEVERITY}});Object.defineProperty(A,"DIAGNOSTIC_SEVERITY",{enumerable:!0,get:function(){return Ve.DIAGNOSTIC_SEVERITY}});Object.defineProperty(A,"canUseDirective",{enumerable:!0,get:function(){return Ve.canUseDirective}});Object.defineProperty(A,"SuggestionCommand",{enumerable:!0,get:function(){return Ve.SuggestionCommand}});Object.defineProperty(A,"validateQuery",{enumerable:!0,get:function(){return Ve.validateQuery}});var Ht=Xa();Object.defineProperty(A,"onlineParser",{enumerable:!0,get:function(){return Ht.onlineParser}});Object.defineProperty(A,"ParseRules",{enumerable:!0,get:function(){return Ht.ParseRules}});Object.defineProperty(A,"CharacterStream",{enumerable:!0,get:function(){return Ht.CharacterStream}});Object.defineProperty(A,"RuleKinds",{enumerable:!0,get:function(){return Ht.RuleKinds}});Object.defineProperty(A,"LexRules",{enumerable:!0,get:function(){return Ht.LexRules}});Object.defineProperty(A,"isIgnored",{enumerable:!0,get:function(){return Ht.isIgnored}});Object.defineProperty(A,"p",{enumerable:!0,get:function(){return Ht.p}});Object.defineProperty(A,"list",{enumerable:!0,get:function(){return Ht.list}});Object.defineProperty(A,"t",{enumerable:!0,get:function(){return Ht.t}});Object.defineProperty(A,"opt",{enumerable:!0,get:function(){return Ht.opt}});var Fy=Jc();Object.defineProperty(A,"CompletionItemKind",{enumerable:!0,get:function(){return Fy.CompletionItemKind}});Object.defineProperty(A,"FileChangeTypeKind",{enumerable:!0,get:function(){return Fy.FileChangeTypeKind}});var ft=Ii();Object.defineProperty(A,"getASTNodeAtPosition",{enumerable:!0,get:function(){return ft.getASTNodeAtPosition}});Object.defineProperty(A,"getFragmentDependencies",{enumerable:!0,get:function(){return ft.getFragmentDependencies}});Object.defineProperty(A,"getFragmentDependenciesForAST",{enumerable:!0,get:function(){return ft.getFragmentDependenciesForAST}});Object.defineProperty(A,"getOperationASTFacts",{enumerable:!0,get:function(){return ft.getOperationASTFacts}});Object.defineProperty(A,"getOperationFacts",{enumerable:!0,get:function(){return ft.getOperationFacts}});Object.defineProperty(A,"getQueryFacts",{enumerable:!0,get:function(){return ft.getQueryFacts}});Object.defineProperty(A,"getVariablesJSONSchema",{enumerable:!0,get:function(){return ft.getVariablesJSONSchema}});Object.defineProperty(A,"offsetToPosition",{enumerable:!0,get:function(){return ft.offsetToPosition}});Object.defineProperty(A,"pointToOffset",{enumerable:!0,get:function(){return ft.pointToOffset}});Object.defineProperty(A,"Position",{enumerable:!0,get:function(){return ft.Position}});Object.defineProperty(A,"collectVariables",{enumerable:!0,get:function(){return ft.collectVariables}});Object.defineProperty(A,"validateWithCustomRules",{enumerable:!0,get:function(){return ft.validateWithCustomRules}});Object.defineProperty(A,"Range",{enumerable:!0,get:function(){return ft.Range}})});var wy=_(hl=>{"use strict";Object.defineProperty(hl,"__esModule",{value:!0});function bD(e,t){var n,r,i=e.levels,a=e.indentLevel,o=!i||i.length===0?a:i.at(-1)-(((n=this.electricInput)===null||n===void 0?void 0:n.test(t))?1:0);return(o||0)*(((r=this.config)===null||r===void 0?void 0:r.indentUnit)||0)}hl.default=bD});var Cy=_(Ai=>{"use strict";var ED=Ai&&Ai.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ai,"__esModule",{value:!0});var oo=ky(),_D=ED(wy()),ND=function(e){var t=(0,oo.onlineParser)({eatWhitespace:function(n){return n.eatWhile(oo.isIgnored)},lexRules:oo.LexRules,parseRules:oo.ParseRules,editorConfig:{tabSize:e.tabSize}});return{config:e,startState:t.startState,token:t.token,indent:_D.default,electricInput:/^\s*[})\]]/,fold:"brace",lineComment:"#",closeBrackets:{pairs:'()[]{}""',explode:"()[]{}"}}};Ai.default=ND});var Vy=_(so=>{"use strict";var qy=so&&so.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(so,"__esModule",{value:!0});var OD=qy(lo()),ID=qy(Cy());OD.default.defineMode("graphql",ID.default)});var hR=El(lo()),TR=El(Vy());})();

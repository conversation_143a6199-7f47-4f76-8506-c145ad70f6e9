<?php

return array(
  'abeezee' => array(
    'source' => 'google',
    'family' => 'ABee<PERSON><PERSON>',
    'stack' => '"ABeeZee", sans-serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'adlamdisplay' => array(
    'source' => 'google',
    'family' => 'ADLaM Display',
    'stack' => '"ADLaM Display", display',
    'weights' => array(
      '400'
    )
  ),
  'aronesans' => array(
    'source' => 'google',
    'family' => 'AR One Sans',
    'stack' => '"AR One Sans", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'abel' => array(
    'source' => 'google',
    'family' => 'Abel',
    'stack' => '"Abel", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'abhayalibre' => array(
    'source' => 'google',
    'family' => 'Abhaya Libre',
    'stack' => '"Abhaya Libre", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'aboreto' => array(
    'source' => 'google',
    'family' => 'Aboreto',
    'stack' => '"Aboreto", display',
    'weights' => array(
      '400'
    )
  ),
  'abrilfatface' => array(
    'source' => 'google',
    'family' => 'Abril Fatface',
    'stack' => '"Abril Fatface", display',
    'weights' => array(
      '400'
    )
  ),
  'abyssinicasil' => array(
    'source' => 'google',
    'family' => 'Abyssinica SIL',
    'stack' => '"Abyssinica SIL", serif',
    'weights' => array(
      '400'
    )
  ),
  'aclonica' => array(
    'source' => 'google',
    'family' => 'Aclonica',
    'stack' => '"Aclonica", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'acme' => array(
    'source' => 'google',
    'family' => 'Acme',
    'stack' => '"Acme", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'actor' => array(
    'source' => 'google',
    'family' => 'Actor',
    'stack' => '"Actor", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'adamina' => array(
    'source' => 'google',
    'family' => 'Adamina',
    'stack' => '"Adamina", serif',
    'weights' => array(
      '400'
    )
  ),
  'adventpro' => array(
    'source' => 'google',
    'family' => 'Advent Pro',
    'stack' => '"Advent Pro", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'afacad' => array(
    'source' => 'google',
    'family' => 'Afacad',
    'stack' => '"Afacad", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'afacadflux' => array(
    'source' => 'google',
    'family' => 'Afacad Flux',
    'stack' => '"Afacad Flux", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'agbalumo' => array(
    'source' => 'google',
    'family' => 'Agbalumo',
    'stack' => '"Agbalumo", display',
    'weights' => array(
      '400'
    )
  ),
  'agdasima' => array(
    'source' => 'google',
    'family' => 'Agdasima',
    'stack' => '"Agdasima", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'agudisplay' => array(
    'source' => 'google',
    'family' => 'Agu Display',
    'stack' => '"Agu Display", display',
    'weights' => array(
      '400'
    )
  ),
  'aguafinascript' => array(
    'source' => 'google',
    'family' => 'Aguafina Script',
    'stack' => '"Aguafina Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'akatab' => array(
    'source' => 'google',
    'family' => 'Akatab',
    'stack' => '"Akatab", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'akayakanadaka' => array(
    'source' => 'google',
    'family' => 'Akaya Kanadaka',
    'stack' => '"Akaya Kanadaka", display',
    'weights' => array(
      '400'
    )
  ),
  'akayatelivigala' => array(
    'source' => 'google',
    'family' => 'Akaya Telivigala',
    'stack' => '"Akaya Telivigala", display',
    'weights' => array(
      '400'
    )
  ),
  'akronim' => array(
    'source' => 'google',
    'family' => 'Akronim',
    'stack' => '"Akronim", display',
    'weights' => array(
      '400'
    )
  ),
  'akshar' => array(
    'source' => 'google',
    'family' => 'Akshar',
    'stack' => '"Akshar", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'aladin' => array(
    'source' => 'google',
    'family' => 'Aladin',
    'stack' => '"Aladin", display',
    'weights' => array(
      '400'
    )
  ),
  'alata' => array(
    'source' => 'google',
    'family' => 'Alata',
    'stack' => '"Alata", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'alatsi' => array(
    'source' => 'google',
    'family' => 'Alatsi',
    'stack' => '"Alatsi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'albertsans' => array(
    'source' => 'google',
    'family' => 'Albert Sans',
    'stack' => '"Albert Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'aldrich' => array(
    'source' => 'google',
    'family' => 'Aldrich',
    'stack' => '"Aldrich", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'alef' => array(
    'source' => 'google',
    'family' => 'Alef',
    'stack' => '"Alef", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'alegreya' => array(
    'source' => 'google',
    'family' => 'Alegreya',
    'stack' => '"Alegreya", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'alegreyasc' => array(
    'source' => 'google',
    'family' => 'Alegreya SC',
    'stack' => '"Alegreya SC", serif',
    'weights' => array(
      '400',
      '400i',
      '500',
      '500i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'alegreyasans' => array(
    'source' => 'google',
    'family' => 'Alegreya Sans',
    'stack' => '"Alegreya Sans", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'alegreyasanssc' => array(
    'source' => 'google',
    'family' => 'Alegreya Sans SC',
    'stack' => '"Alegreya Sans SC", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'aleo' => array(
    'source' => 'google',
    'family' => 'Aleo',
    'stack' => '"Aleo", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'alexbrush' => array(
    'source' => 'google',
    'family' => 'Alex Brush',
    'stack' => '"Alex Brush", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'alexandria' => array(
    'source' => 'google',
    'family' => 'Alexandria',
    'stack' => '"Alexandria", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'alfaslabone' => array(
    'source' => 'google',
    'family' => 'Alfa Slab One',
    'stack' => '"Alfa Slab One", display',
    'weights' => array(
      '400'
    )
  ),
  'alice' => array(
    'source' => 'google',
    'family' => 'Alice',
    'stack' => '"Alice", serif',
    'weights' => array(
      '400'
    )
  ),
  'alike' => array(
    'source' => 'google',
    'family' => 'Alike',
    'stack' => '"Alike", serif',
    'weights' => array(
      '400'
    )
  ),
  'alikeangular' => array(
    'source' => 'google',
    'family' => 'Alike Angular',
    'stack' => '"Alike Angular", serif',
    'weights' => array(
      '400'
    )
  ),
  'alkalami' => array(
    'source' => 'google',
    'family' => 'Alkalami',
    'stack' => '"Alkalami", serif',
    'weights' => array(
      '400'
    )
  ),
  'alkatra' => array(
    'source' => 'google',
    'family' => 'Alkatra',
    'stack' => '"Alkatra", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'allan' => array(
    'source' => 'google',
    'family' => 'Allan',
    'stack' => '"Allan", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'allerta' => array(
    'source' => 'google',
    'family' => 'Allerta',
    'stack' => '"Allerta", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'allertastencil' => array(
    'source' => 'google',
    'family' => 'Allerta Stencil',
    'stack' => '"Allerta Stencil", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'allison' => array(
    'source' => 'google',
    'family' => 'Allison',
    'stack' => '"Allison", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'allura' => array(
    'source' => 'google',
    'family' => 'Allura',
    'stack' => '"Allura", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'almarai' => array(
    'source' => 'google',
    'family' => 'Almarai',
    'stack' => '"Almarai", sans-serif',
    'weights' => array(
      '300',
      '400',
      '700',
      '800'
    )
  ),
  'almendra' => array(
    'source' => 'google',
    'family' => 'Almendra',
    'stack' => '"Almendra", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'almendradisplay' => array(
    'source' => 'google',
    'family' => 'Almendra Display',
    'stack' => '"Almendra Display", display',
    'weights' => array(
      '400'
    )
  ),
  'almendrasc' => array(
    'source' => 'google',
    'family' => 'Almendra SC',
    'stack' => '"Almendra SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'alumnisans' => array(
    'source' => 'google',
    'family' => 'Alumni Sans',
    'stack' => '"Alumni Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'alumnisanscollegiateone' => array(
    'source' => 'google',
    'family' => 'Alumni Sans Collegiate One',
    'stack' => '"Alumni Sans Collegiate One", sans-serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'alumnisansinlineone' => array(
    'source' => 'google',
    'family' => 'Alumni Sans Inline One',
    'stack' => '"Alumni Sans Inline One", display',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'alumnisanspinstripe' => array(
    'source' => 'google',
    'family' => 'Alumni Sans Pinstripe',
    'stack' => '"Alumni Sans Pinstripe", sans-serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'alumnisanssc' => array(
    'source' => 'google',
    'family' => 'Alumni Sans SC',
    'stack' => '"Alumni Sans SC", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'amarante' => array(
    'source' => 'google',
    'family' => 'Amarante',
    'stack' => '"Amarante", display',
    'weights' => array(
      '400'
    )
  ),
  'amaranth' => array(
    'source' => 'google',
    'family' => 'Amaranth',
    'stack' => '"Amaranth", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'amaticsc' => array(
    'source' => 'google',
    'family' => 'Amatic SC',
    'stack' => '"Amatic SC", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'amethysta' => array(
    'source' => 'google',
    'family' => 'Amethysta',
    'stack' => '"Amethysta", serif',
    'weights' => array(
      '400'
    )
  ),
  'amiko' => array(
    'source' => 'google',
    'family' => 'Amiko',
    'stack' => '"Amiko", sans-serif',
    'weights' => array(
      '400',
      '600',
      '700'
    )
  ),
  'amiri' => array(
    'source' => 'google',
    'family' => 'Amiri',
    'stack' => '"Amiri", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'amiriquran' => array(
    'source' => 'google',
    'family' => 'Amiri Quran',
    'stack' => '"Amiri Quran", serif',
    'weights' => array(
      '400'
    )
  ),
  'amita' => array(
    'source' => 'google',
    'family' => 'Amita',
    'stack' => '"Amita", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'anaheim' => array(
    'source' => 'google',
    'family' => 'Anaheim',
    'stack' => '"Anaheim", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'ancizarsans' => array(
    'source' => 'google',
    'family' => 'Ancizar Sans',
    'stack' => '"Ancizar Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'ancizarserif' => array(
    'source' => 'google',
    'family' => 'Ancizar Serif',
    'stack' => '"Ancizar Serif", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'andadapro' => array(
    'source' => 'google',
    'family' => 'Andada Pro',
    'stack' => '"Andada Pro", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'andika' => array(
    'source' => 'google',
    'family' => 'Andika',
    'stack' => '"Andika", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'anekbangla' => array(
    'source' => 'google',
    'family' => 'Anek Bangla',
    'stack' => '"Anek Bangla", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'anekdevanagari' => array(
    'source' => 'google',
    'family' => 'Anek Devanagari',
    'stack' => '"Anek Devanagari", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'anekgujarati' => array(
    'source' => 'google',
    'family' => 'Anek Gujarati',
    'stack' => '"Anek Gujarati", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'anekgurmukhi' => array(
    'source' => 'google',
    'family' => 'Anek Gurmukhi',
    'stack' => '"Anek Gurmukhi", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'anekkannada' => array(
    'source' => 'google',
    'family' => 'Anek Kannada',
    'stack' => '"Anek Kannada", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'aneklatin' => array(
    'source' => 'google',
    'family' => 'Anek Latin',
    'stack' => '"Anek Latin", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'anekmalayalam' => array(
    'source' => 'google',
    'family' => 'Anek Malayalam',
    'stack' => '"Anek Malayalam", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'anekodia' => array(
    'source' => 'google',
    'family' => 'Anek Odia',
    'stack' => '"Anek Odia", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'anektamil' => array(
    'source' => 'google',
    'family' => 'Anek Tamil',
    'stack' => '"Anek Tamil", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'anektelugu' => array(
    'source' => 'google',
    'family' => 'Anek Telugu',
    'stack' => '"Anek Telugu", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'angkor' => array(
    'source' => 'google',
    'family' => 'Angkor',
    'stack' => '"Angkor", display',
    'weights' => array(
      '400'
    )
  ),
  'annapurnasil' => array(
    'source' => 'google',
    'family' => 'Annapurna SIL',
    'stack' => '"Annapurna SIL", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'annieuseyourtelescope' => array(
    'source' => 'google',
    'family' => 'Annie Use Your Telescope',
    'stack' => '"Annie Use Your Telescope", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'anonymouspro' => array(
    'source' => 'google',
    'family' => 'Anonymous Pro',
    'stack' => '"Anonymous Pro", monospace',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'anta' => array(
    'source' => 'google',
    'family' => 'Anta',
    'stack' => '"Anta", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'antic' => array(
    'source' => 'google',
    'family' => 'Antic',
    'stack' => '"Antic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'anticdidone' => array(
    'source' => 'google',
    'family' => 'Antic Didone',
    'stack' => '"Antic Didone", serif',
    'weights' => array(
      '400'
    )
  ),
  'anticslab' => array(
    'source' => 'google',
    'family' => 'Antic Slab',
    'stack' => '"Antic Slab", serif',
    'weights' => array(
      '400'
    )
  ),
  'anton' => array(
    'source' => 'google',
    'family' => 'Anton',
    'stack' => '"Anton", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'antonsc' => array(
    'source' => 'google',
    'family' => 'Anton SC',
    'stack' => '"Anton SC", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'antonio' => array(
    'source' => 'google',
    'family' => 'Antonio',
    'stack' => '"Antonio", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'anuphan' => array(
    'source' => 'google',
    'family' => 'Anuphan',
    'stack' => '"Anuphan", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'anybody' => array(
    'source' => 'google',
    'family' => 'Anybody',
    'stack' => '"Anybody", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'aoboshione' => array(
    'source' => 'google',
    'family' => 'Aoboshi One',
    'stack' => '"Aoboshi One", serif',
    'weights' => array(
      '400'
    )
  ),
  'arapey' => array(
    'source' => 'google',
    'family' => 'Arapey',
    'stack' => '"Arapey", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'arbutus' => array(
    'source' => 'google',
    'family' => 'Arbutus',
    'stack' => '"Arbutus", serif',
    'weights' => array(
      '400'
    )
  ),
  'arbutusslab' => array(
    'source' => 'google',
    'family' => 'Arbutus Slab',
    'stack' => '"Arbutus Slab", serif',
    'weights' => array(
      '400'
    )
  ),
  'architectsdaughter' => array(
    'source' => 'google',
    'family' => 'Architects Daughter',
    'stack' => '"Architects Daughter", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'archivo' => array(
    'source' => 'google',
    'family' => 'Archivo',
    'stack' => '"Archivo", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'archivoblack' => array(
    'source' => 'google',
    'family' => 'Archivo Black',
    'stack' => '"Archivo Black", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'archivonarrow' => array(
    'source' => 'google',
    'family' => 'Archivo Narrow',
    'stack' => '"Archivo Narrow", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'areyouserious' => array(
    'source' => 'google',
    'family' => 'Are You Serious',
    'stack' => '"Are You Serious", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'arefruqaa' => array(
    'source' => 'google',
    'family' => 'Aref Ruqaa',
    'stack' => '"Aref Ruqaa", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'arefruqaaink' => array(
    'source' => 'google',
    'family' => 'Aref Ruqaa Ink',
    'stack' => '"Aref Ruqaa Ink", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'arima' => array(
    'source' => 'google',
    'family' => 'Arima',
    'stack' => '"Arima", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'arimo' => array(
    'source' => 'google',
    'family' => 'Arimo',
    'stack' => '"Arimo", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'arizonia' => array(
    'source' => 'google',
    'family' => 'Arizonia',
    'stack' => '"Arizonia", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'armata' => array(
    'source' => 'google',
    'family' => 'Armata',
    'stack' => '"Armata", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'arsenal' => array(
    'source' => 'google',
    'family' => 'Arsenal',
    'stack' => '"Arsenal", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'arsenalsc' => array(
    'source' => 'google',
    'family' => 'Arsenal SC',
    'stack' => '"Arsenal SC", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'artifika' => array(
    'source' => 'google',
    'family' => 'Artifika',
    'stack' => '"Artifika", serif',
    'weights' => array(
      '400'
    )
  ),
  'arvo' => array(
    'source' => 'google',
    'family' => 'Arvo',
    'stack' => '"Arvo", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'arya' => array(
    'source' => 'google',
    'family' => 'Arya',
    'stack' => '"Arya", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'asap' => array(
    'source' => 'google',
    'family' => 'Asap',
    'stack' => '"Asap", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'asapcondensed' => array(
    'source' => 'google',
    'family' => 'Asap Condensed',
    'stack' => '"Asap Condensed", sans-serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'asar' => array(
    'source' => 'google',
    'family' => 'Asar',
    'stack' => '"Asar", serif',
    'weights' => array(
      '400'
    )
  ),
  'asset' => array(
    'source' => 'google',
    'family' => 'Asset',
    'stack' => '"Asset", display',
    'weights' => array(
      '400'
    )
  ),
  'assistant' => array(
    'source' => 'google',
    'family' => 'Assistant',
    'stack' => '"Assistant", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'astasans' => array(
    'source' => 'google',
    'family' => 'Asta Sans',
    'stack' => '"Asta Sans", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'astloch' => array(
    'source' => 'google',
    'family' => 'Astloch',
    'stack' => '"Astloch", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'asul' => array(
    'source' => 'google',
    'family' => 'Asul',
    'stack' => '"Asul", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'athiti' => array(
    'source' => 'google',
    'family' => 'Athiti',
    'stack' => '"Athiti", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'atkinsonhyperlegible' => array(
    'source' => 'google',
    'family' => 'Atkinson Hyperlegible',
    'stack' => '"Atkinson Hyperlegible", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'atkinsonhyperlegiblemono' => array(
    'source' => 'google',
    'family' => 'Atkinson Hyperlegible Mono',
    'stack' => '"Atkinson Hyperlegible Mono", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'atkinsonhyperlegiblenext' => array(
    'source' => 'google',
    'family' => 'Atkinson Hyperlegible Next',
    'stack' => '"Atkinson Hyperlegible Next", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'atma' => array(
    'source' => 'google',
    'family' => 'Atma',
    'stack' => '"Atma", display',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'atomicage' => array(
    'source' => 'google',
    'family' => 'Atomic Age',
    'stack' => '"Atomic Age", display',
    'weights' => array(
      '400'
    )
  ),
  'aubrey' => array(
    'source' => 'google',
    'family' => 'Aubrey',
    'stack' => '"Aubrey", display',
    'weights' => array(
      '400'
    )
  ),
  'audiowide' => array(
    'source' => 'google',
    'family' => 'Audiowide',
    'stack' => '"Audiowide", display',
    'weights' => array(
      '400'
    )
  ),
  'autourone' => array(
    'source' => 'google',
    'family' => 'Autour One',
    'stack' => '"Autour One", display',
    'weights' => array(
      '400'
    )
  ),
  'average' => array(
    'source' => 'google',
    'family' => 'Average',
    'stack' => '"Average", serif',
    'weights' => array(
      '400'
    )
  ),
  'averagesans' => array(
    'source' => 'google',
    'family' => 'Average Sans',
    'stack' => '"Average Sans", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'averiagruesalibre' => array(
    'source' => 'google',
    'family' => 'Averia Gruesa Libre',
    'stack' => '"Averia Gruesa Libre", display',
    'weights' => array(
      '400'
    )
  ),
  'averialibre' => array(
    'source' => 'google',
    'family' => 'Averia Libre',
    'stack' => '"Averia Libre", display',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'averiasanslibre' => array(
    'source' => 'google',
    'family' => 'Averia Sans Libre',
    'stack' => '"Averia Sans Libre", display',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'averiaseriflibre' => array(
    'source' => 'google',
    'family' => 'Averia Serif Libre',
    'stack' => '"Averia Serif Libre", display',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'azeretmono' => array(
    'source' => 'google',
    'family' => 'Azeret Mono',
    'stack' => '"Azeret Mono", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'b612' => array(
    'source' => 'google',
    'family' => 'B612',
    'stack' => '"B612", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'b612mono' => array(
    'source' => 'google',
    'family' => 'B612 Mono',
    'stack' => '"B612 Mono", monospace',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'bizudgothic' => array(
    'source' => 'google',
    'family' => 'BIZ UDGothic',
    'stack' => '"BIZ UDGothic", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'bizudmincho' => array(
    'source' => 'google',
    'family' => 'BIZ UDMincho',
    'stack' => '"BIZ UDMincho", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'bizudpgothic' => array(
    'source' => 'google',
    'family' => 'BIZ UDPGothic',
    'stack' => '"BIZ UDPGothic", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'bizudpmincho' => array(
    'source' => 'google',
    'family' => 'BIZ UDPMincho',
    'stack' => '"BIZ UDPMincho", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'babylonica' => array(
    'source' => 'google',
    'family' => 'Babylonica',
    'stack' => '"Babylonica", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'bacasimeantique' => array(
    'source' => 'google',
    'family' => 'Bacasime Antique',
    'stack' => '"Bacasime Antique", serif',
    'weights' => array(
      '400'
    )
  ),
  'badscript' => array(
    'source' => 'google',
    'family' => 'Bad Script',
    'stack' => '"Bad Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'badeendisplay' => array(
    'source' => 'google',
    'family' => 'Badeen Display',
    'stack' => '"Badeen Display", display',
    'weights' => array(
      '400'
    )
  ),
  'bagelfatone' => array(
    'source' => 'google',
    'family' => 'Bagel Fat One',
    'stack' => '"Bagel Fat One", display',
    'weights' => array(
      '400'
    )
  ),
  'bahiana' => array(
    'source' => 'google',
    'family' => 'Bahiana',
    'stack' => '"Bahiana", display',
    'weights' => array(
      '400'
    )
  ),
  'bahianita' => array(
    'source' => 'google',
    'family' => 'Bahianita',
    'stack' => '"Bahianita", display',
    'weights' => array(
      '400'
    )
  ),
  'baijamjuree' => array(
    'source' => 'google',
    'family' => 'Bai Jamjuree',
    'stack' => '"Bai Jamjuree", sans-serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'bakbakone' => array(
    'source' => 'google',
    'family' => 'Bakbak One',
    'stack' => '"Bakbak One", display',
    'weights' => array(
      '400'
    )
  ),
  'ballet' => array(
    'source' => 'google',
    'family' => 'Ballet',
    'stack' => '"Ballet", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'baloo2' => array(
    'source' => 'google',
    'family' => 'Baloo 2',
    'stack' => '"Baloo 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'baloobhai2' => array(
    'source' => 'google',
    'family' => 'Baloo Bhai 2',
    'stack' => '"Baloo Bhai 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'baloobhaijaan2' => array(
    'source' => 'google',
    'family' => 'Baloo Bhaijaan 2',
    'stack' => '"Baloo Bhaijaan 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'baloobhaina2' => array(
    'source' => 'google',
    'family' => 'Baloo Bhaina 2',
    'stack' => '"Baloo Bhaina 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'baloochettan2' => array(
    'source' => 'google',
    'family' => 'Baloo Chettan 2',
    'stack' => '"Baloo Chettan 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'balooda2' => array(
    'source' => 'google',
    'family' => 'Baloo Da 2',
    'stack' => '"Baloo Da 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'baloopaaji2' => array(
    'source' => 'google',
    'family' => 'Baloo Paaji 2',
    'stack' => '"Baloo Paaji 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'balootamma2' => array(
    'source' => 'google',
    'family' => 'Baloo Tamma 2',
    'stack' => '"Baloo Tamma 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'balootammudu2' => array(
    'source' => 'google',
    'family' => 'Baloo Tammudu 2',
    'stack' => '"Baloo Tammudu 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'baloothambi2' => array(
    'source' => 'google',
    'family' => 'Baloo Thambi 2',
    'stack' => '"Baloo Thambi 2", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'balsamiqsans' => array(
    'source' => 'google',
    'family' => 'Balsamiq Sans',
    'stack' => '"Balsamiq Sans", display',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'balthazar' => array(
    'source' => 'google',
    'family' => 'Balthazar',
    'stack' => '"Balthazar", serif',
    'weights' => array(
      '400'
    )
  ),
  'bangers' => array(
    'source' => 'google',
    'family' => 'Bangers',
    'stack' => '"Bangers", display',
    'weights' => array(
      '400'
    )
  ),
  'barlow' => array(
    'source' => 'google',
    'family' => 'Barlow',
    'stack' => '"Barlow", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'barlowcondensed' => array(
    'source' => 'google',
    'family' => 'Barlow Condensed',
    'stack' => '"Barlow Condensed", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'barlowsemicondensed' => array(
    'source' => 'google',
    'family' => 'Barlow Semi Condensed',
    'stack' => '"Barlow Semi Condensed", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'barriecito' => array(
    'source' => 'google',
    'family' => 'Barriecito',
    'stack' => '"Barriecito", display',
    'weights' => array(
      '400'
    )
  ),
  'barrio' => array(
    'source' => 'google',
    'family' => 'Barrio',
    'stack' => '"Barrio", display',
    'weights' => array(
      '400'
    )
  ),
  'basic' => array(
    'source' => 'google',
    'family' => 'Basic',
    'stack' => '"Basic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'baskervville' => array(
    'source' => 'google',
    'family' => 'Baskervville',
    'stack' => '"Baskervville", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'baskervvillesc' => array(
    'source' => 'google',
    'family' => 'Baskervville SC',
    'stack' => '"Baskervville SC", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'battambang' => array(
    'source' => 'google',
    'family' => 'Battambang',
    'stack' => '"Battambang", display',
    'weights' => array(
      '100',
      '300',
      '400',
      '700',
      '900'
    )
  ),
  'baumans' => array(
    'source' => 'google',
    'family' => 'Baumans',
    'stack' => '"Baumans", display',
    'weights' => array(
      '400'
    )
  ),
  'bayon' => array(
    'source' => 'google',
    'family' => 'Bayon',
    'stack' => '"Bayon", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'bevietnampro' => array(
    'source' => 'google',
    'family' => 'Be Vietnam Pro',
    'stack' => '"Be Vietnam Pro", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'beaurivage' => array(
    'source' => 'google',
    'family' => 'Beau Rivage',
    'stack' => '"Beau Rivage", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'bebasneue' => array(
    'source' => 'google',
    'family' => 'Bebas Neue',
    'stack' => '"Bebas Neue", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'beiruti' => array(
    'source' => 'google',
    'family' => 'Beiruti',
    'stack' => '"Beiruti", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'belanosima' => array(
    'source' => 'google',
    'family' => 'Belanosima',
    'stack' => '"Belanosima", sans-serif',
    'weights' => array(
      '400',
      '600',
      '700'
    )
  ),
  'belgrano' => array(
    'source' => 'google',
    'family' => 'Belgrano',
    'stack' => '"Belgrano", serif',
    'weights' => array(
      '400'
    )
  ),
  'bellefair' => array(
    'source' => 'google',
    'family' => 'Bellefair',
    'stack' => '"Bellefair", serif',
    'weights' => array(
      '400'
    )
  ),
  'belleza' => array(
    'source' => 'google',
    'family' => 'Belleza',
    'stack' => '"Belleza", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'bellota' => array(
    'source' => 'google',
    'family' => 'Bellota',
    'stack' => '"Bellota", display',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'bellotatext' => array(
    'source' => 'google',
    'family' => 'Bellota Text',
    'stack' => '"Bellota Text", display',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'benchnine' => array(
    'source' => 'google',
    'family' => 'BenchNine',
    'stack' => '"BenchNine", sans-serif',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'benne' => array(
    'source' => 'google',
    'family' => 'Benne',
    'stack' => '"Benne", serif',
    'weights' => array(
      '400'
    )
  ),
  'bentham' => array(
    'source' => 'google',
    'family' => 'Bentham',
    'stack' => '"Bentham", serif',
    'weights' => array(
      '400'
    )
  ),
  'berkshireswash' => array(
    'source' => 'google',
    'family' => 'Berkshire Swash',
    'stack' => '"Berkshire Swash", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'besley' => array(
    'source' => 'google',
    'family' => 'Besley',
    'stack' => '"Besley", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'bethellen' => array(
    'source' => 'google',
    'family' => 'Beth Ellen',
    'stack' => '"Beth Ellen", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'bevan' => array(
    'source' => 'google',
    'family' => 'Bevan',
    'stack' => '"Bevan", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'bhutukaexpandedone' => array(
    'source' => 'google',
    'family' => 'BhuTuka Expanded One',
    'stack' => '"BhuTuka Expanded One", serif',
    'weights' => array(
      '400'
    )
  ),
  'bigshoulders' => array(
    'source' => 'google',
    'family' => 'Big Shoulders',
    'stack' => '"Big Shoulders", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'bigshouldersinline' => array(
    'source' => 'google',
    'family' => 'Big Shoulders Inline',
    'stack' => '"Big Shoulders Inline", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'bigshouldersstencil' => array(
    'source' => 'google',
    'family' => 'Big Shoulders Stencil',
    'stack' => '"Big Shoulders Stencil", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'bigelowrules' => array(
    'source' => 'google',
    'family' => 'Bigelow Rules',
    'stack' => '"Bigelow Rules", display',
    'weights' => array(
      '400'
    )
  ),
  'bigshotone' => array(
    'source' => 'google',
    'family' => 'Bigshot One',
    'stack' => '"Bigshot One", display',
    'weights' => array(
      '400'
    )
  ),
  'bilbo' => array(
    'source' => 'google',
    'family' => 'Bilbo',
    'stack' => '"Bilbo", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'bilboswashcaps' => array(
    'source' => 'google',
    'family' => 'Bilbo Swash Caps',
    'stack' => '"Bilbo Swash Caps", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'biorhyme' => array(
    'source' => 'google',
    'family' => 'BioRhyme',
    'stack' => '"BioRhyme", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'biorhymeexpanded' => array(
    'source' => 'google',
    'family' => 'BioRhyme Expanded',
    'stack' => '"BioRhyme Expanded", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '700',
      '800'
    )
  ),
  'birthstone' => array(
    'source' => 'google',
    'family' => 'Birthstone',
    'stack' => '"Birthstone", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'birthstonebounce' => array(
    'source' => 'google',
    'family' => 'Birthstone Bounce',
    'stack' => '"Birthstone Bounce", handwriting',
    'weights' => array(
      '400',
      '500'
    )
  ),
  'biryani' => array(
    'source' => 'google',
    'family' => 'Biryani',
    'stack' => '"Biryani", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'bitcountgriddouble' => array(
    'source' => 'google',
    'family' => 'Bitcount Grid Double',
    'stack' => '"Bitcount Grid Double", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'bitter' => array(
    'source' => 'google',
    'family' => 'Bitter',
    'stack' => '"Bitter", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'blackandwhitepicture' => array(
    'source' => 'google',
    'family' => 'Black And White Picture',
    'stack' => '"Black And White Picture", display',
    'weights' => array(
      '400'
    )
  ),
  'blackhansans' => array(
    'source' => 'google',
    'family' => 'Black Han Sans',
    'stack' => '"Black Han Sans", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'blackopsone' => array(
    'source' => 'google',
    'family' => 'Black Ops One',
    'stack' => '"Black Ops One", display',
    'weights' => array(
      '400'
    )
  ),
  'blaka' => array(
    'source' => 'google',
    'family' => 'Blaka',
    'stack' => '"Blaka", display',
    'weights' => array(
      '400'
    )
  ),
  'blakahollow' => array(
    'source' => 'google',
    'family' => 'Blaka Hollow',
    'stack' => '"Blaka Hollow", display',
    'weights' => array(
      '400'
    )
  ),
  'blakaink' => array(
    'source' => 'google',
    'family' => 'Blaka Ink',
    'stack' => '"Blaka Ink", display',
    'weights' => array(
      '400'
    )
  ),
  'blinker' => array(
    'source' => 'google',
    'family' => 'Blinker',
    'stack' => '"Blinker", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'bodonimoda' => array(
    'source' => 'google',
    'family' => 'Bodoni Moda',
    'stack' => '"Bodoni Moda", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'bodonimodasc' => array(
    'source' => 'google',
    'family' => 'Bodoni Moda SC',
    'stack' => '"Bodoni Moda SC", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'bokor' => array(
    'source' => 'google',
    'family' => 'Bokor',
    'stack' => '"Bokor", display',
    'weights' => array(
      '400'
    )
  ),
  'boldonse' => array(
    'source' => 'google',
    'family' => 'Boldonse',
    'stack' => '"Boldonse", display',
    'weights' => array(
      '400'
    )
  ),
  'bonanova' => array(
    'source' => 'google',
    'family' => 'Bona Nova',
    'stack' => '"Bona Nova", serif',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'bonanovasc' => array(
    'source' => 'google',
    'family' => 'Bona Nova SC',
    'stack' => '"Bona Nova SC", serif',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'bonbon' => array(
    'source' => 'google',
    'family' => 'Bonbon',
    'stack' => '"Bonbon", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'bonheurroyale' => array(
    'source' => 'google',
    'family' => 'Bonheur Royale',
    'stack' => '"Bonheur Royale", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'boogaloo' => array(
    'source' => 'google',
    'family' => 'Boogaloo',
    'stack' => '"Boogaloo", display',
    'weights' => array(
      '400'
    )
  ),
  'borel' => array(
    'source' => 'google',
    'family' => 'Borel',
    'stack' => '"Borel", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'bowlbyone' => array(
    'source' => 'google',
    'family' => 'Bowlby One',
    'stack' => '"Bowlby One", display',
    'weights' => array(
      '400'
    )
  ),
  'bowlbyonesc' => array(
    'source' => 'google',
    'family' => 'Bowlby One SC',
    'stack' => '"Bowlby One SC", display',
    'weights' => array(
      '400'
    )
  ),
  'braahone' => array(
    'source' => 'google',
    'family' => 'Braah One',
    'stack' => '"Braah One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'brawler' => array(
    'source' => 'google',
    'family' => 'Brawler',
    'stack' => '"Brawler", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'breeserif' => array(
    'source' => 'google',
    'family' => 'Bree Serif',
    'stack' => '"Bree Serif", serif',
    'weights' => array(
      '400'
    )
  ),
  'bricolagegrotesque' => array(
    'source' => 'google',
    'family' => 'Bricolage Grotesque',
    'stack' => '"Bricolage Grotesque", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'brunoace' => array(
    'source' => 'google',
    'family' => 'Bruno Ace',
    'stack' => '"Bruno Ace", display',
    'weights' => array(
      '400'
    )
  ),
  'brunoacesc' => array(
    'source' => 'google',
    'family' => 'Bruno Ace SC',
    'stack' => '"Bruno Ace SC", display',
    'weights' => array(
      '400'
    )
  ),
  'brygada1918' => array(
    'source' => 'google',
    'family' => 'Brygada 1918',
    'stack' => '"Brygada 1918", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'bubblegumsans' => array(
    'source' => 'google',
    'family' => 'Bubblegum Sans',
    'stack' => '"Bubblegum Sans", display',
    'weights' => array(
      '400'
    )
  ),
  'bubblerone' => array(
    'source' => 'google',
    'family' => 'Bubbler One',
    'stack' => '"Bubbler One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'buda' => array(
    'source' => 'google',
    'family' => 'Buda',
    'stack' => '"Buda", display',
    'weights' => array(
      '300'
    )
  ),
  'buenard' => array(
    'source' => 'google',
    'family' => 'Buenard',
    'stack' => '"Buenard", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'bungee' => array(
    'source' => 'google',
    'family' => 'Bungee',
    'stack' => '"Bungee", display',
    'weights' => array(
      '400'
    )
  ),
  'bungeehairline' => array(
    'source' => 'google',
    'family' => 'Bungee Hairline',
    'stack' => '"Bungee Hairline", display',
    'weights' => array(
      '400'
    )
  ),
  'bungeeinline' => array(
    'source' => 'google',
    'family' => 'Bungee Inline',
    'stack' => '"Bungee Inline", display',
    'weights' => array(
      '400'
    )
  ),
  'bungeeoutline' => array(
    'source' => 'google',
    'family' => 'Bungee Outline',
    'stack' => '"Bungee Outline", display',
    'weights' => array(
      '400'
    )
  ),
  'bungeeshade' => array(
    'source' => 'google',
    'family' => 'Bungee Shade',
    'stack' => '"Bungee Shade", display',
    'weights' => array(
      '400'
    )
  ),
  'bungeespice' => array(
    'source' => 'google',
    'family' => 'Bungee Spice',
    'stack' => '"Bungee Spice", display',
    'weights' => array(
      '400'
    )
  ),
  'bungeetint' => array(
    'source' => 'google',
    'family' => 'Bungee Tint',
    'stack' => '"Bungee Tint", display',
    'weights' => array(
      '400'
    )
  ),
  'butcherman' => array(
    'source' => 'google',
    'family' => 'Butcherman',
    'stack' => '"Butcherman", display',
    'weights' => array(
      '400'
    )
  ),
  'butterflykids' => array(
    'source' => 'google',
    'family' => 'Butterfly Kids',
    'stack' => '"Butterfly Kids", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'bytesized' => array(
    'source' => 'google',
    'family' => 'Bytesized',
    'stack' => '"Bytesized", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'cabin' => array(
    'source' => 'google',
    'family' => 'Cabin',
    'stack' => '"Cabin", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'cabincondensed' => array(
    'source' => 'google',
    'family' => 'Cabin Condensed',
    'stack' => '"Cabin Condensed", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'cabinsketch' => array(
    'source' => 'google',
    'family' => 'Cabin Sketch',
    'stack' => '"Cabin Sketch", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'cactusclassicalserif' => array(
    'source' => 'google',
    'family' => 'Cactus Classical Serif',
    'stack' => '"Cactus Classical Serif", serif',
    'weights' => array(
      '400'
    )
  ),
  'caesardressing' => array(
    'source' => 'google',
    'family' => 'Caesar Dressing',
    'stack' => '"Caesar Dressing", display',
    'weights' => array(
      '400'
    )
  ),
  'cagliostro' => array(
    'source' => 'google',
    'family' => 'Cagliostro',
    'stack' => '"Cagliostro", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'cairo' => array(
    'source' => 'google',
    'family' => 'Cairo',
    'stack' => '"Cairo", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'cairoplay' => array(
    'source' => 'google',
    'family' => 'Cairo Play',
    'stack' => '"Cairo Play", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'calsans' => array(
    'source' => 'google',
    'family' => 'Cal Sans',
    'stack' => '"Cal Sans", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'caladea' => array(
    'source' => 'google',
    'family' => 'Caladea',
    'stack' => '"Caladea", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'calistoga' => array(
    'source' => 'google',
    'family' => 'Calistoga',
    'stack' => '"Calistoga", display',
    'weights' => array(
      '400'
    )
  ),
  'calligraffitti' => array(
    'source' => 'google',
    'family' => 'Calligraffitti',
    'stack' => '"Calligraffitti", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'cambay' => array(
    'source' => 'google',
    'family' => 'Cambay',
    'stack' => '"Cambay", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'cambo' => array(
    'source' => 'google',
    'family' => 'Cambo',
    'stack' => '"Cambo", serif',
    'weights' => array(
      '400'
    )
  ),
  'candal' => array(
    'source' => 'google',
    'family' => 'Candal',
    'stack' => '"Candal", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'cantarell' => array(
    'source' => 'google',
    'family' => 'Cantarell',
    'stack' => '"Cantarell", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'cantataone' => array(
    'source' => 'google',
    'family' => 'Cantata One',
    'stack' => '"Cantata One", serif',
    'weights' => array(
      '400'
    )
  ),
  'cantoraone' => array(
    'source' => 'google',
    'family' => 'Cantora One',
    'stack' => '"Cantora One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'caprasimo' => array(
    'source' => 'google',
    'family' => 'Caprasimo',
    'stack' => '"Caprasimo", display',
    'weights' => array(
      '400'
    )
  ),
  'capriola' => array(
    'source' => 'google',
    'family' => 'Capriola',
    'stack' => '"Capriola", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'caramel' => array(
    'source' => 'google',
    'family' => 'Caramel',
    'stack' => '"Caramel", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'carattere' => array(
    'source' => 'google',
    'family' => 'Carattere',
    'stack' => '"Carattere", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'cardo' => array(
    'source' => 'google',
    'family' => 'Cardo',
    'stack' => '"Cardo", serif',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'carlito' => array(
    'source' => 'google',
    'family' => 'Carlito',
    'stack' => '"Carlito", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'carme' => array(
    'source' => 'google',
    'family' => 'Carme',
    'stack' => '"Carme", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'carroisgothic' => array(
    'source' => 'google',
    'family' => 'Carrois Gothic',
    'stack' => '"Carrois Gothic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'carroisgothicsc' => array(
    'source' => 'google',
    'family' => 'Carrois Gothic SC',
    'stack' => '"Carrois Gothic SC", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'carterone' => array(
    'source' => 'google',
    'family' => 'Carter One',
    'stack' => '"Carter One", display',
    'weights' => array(
      '400'
    )
  ),
  'cascadiacode' => array(
    'source' => 'google',
    'family' => 'Cascadia Code',
    'stack' => '"Cascadia Code", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'cascadiamono' => array(
    'source' => 'google',
    'family' => 'Cascadia Mono',
    'stack' => '"Cascadia Mono", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'castoro' => array(
    'source' => 'google',
    'family' => 'Castoro',
    'stack' => '"Castoro", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'castorotitling' => array(
    'source' => 'google',
    'family' => 'Castoro Titling',
    'stack' => '"Castoro Titling", display',
    'weights' => array(
      '400'
    )
  ),
  'catamaran' => array(
    'source' => 'google',
    'family' => 'Catamaran',
    'stack' => '"Catamaran", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'caudex' => array(
    'source' => 'google',
    'family' => 'Caudex',
    'stack' => '"Caudex", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'caveat' => array(
    'source' => 'google',
    'family' => 'Caveat',
    'stack' => '"Caveat", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'caveatbrush' => array(
    'source' => 'google',
    'family' => 'Caveat Brush',
    'stack' => '"Caveat Brush", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'cedarvillecursive' => array(
    'source' => 'google',
    'family' => 'Cedarville Cursive',
    'stack' => '"Cedarville Cursive", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'cevicheone' => array(
    'source' => 'google',
    'family' => 'Ceviche One',
    'stack' => '"Ceviche One", display',
    'weights' => array(
      '400'
    )
  ),
  'chakrapetch' => array(
    'source' => 'google',
    'family' => 'Chakra Petch',
    'stack' => '"Chakra Petch", sans-serif',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'changa' => array(
    'source' => 'google',
    'family' => 'Changa',
    'stack' => '"Changa", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'changaone' => array(
    'source' => 'google',
    'family' => 'Changa One',
    'stack' => '"Changa One", display',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'chango' => array(
    'source' => 'google',
    'family' => 'Chango',
    'stack' => '"Chango", display',
    'weights' => array(
      '400'
    )
  ),
  'charissil' => array(
    'source' => 'google',
    'family' => 'Charis SIL',
    'stack' => '"Charis SIL", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'charm' => array(
    'source' => 'google',
    'family' => 'Charm',
    'stack' => '"Charm", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'charmonman' => array(
    'source' => 'google',
    'family' => 'Charmonman',
    'stack' => '"Charmonman", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'chathura' => array(
    'source' => 'google',
    'family' => 'Chathura',
    'stack' => '"Chathura", sans-serif',
    'weights' => array(
      '100',
      '300',
      '400',
      '700',
      '800'
    )
  ),
  'chauphilomeneone' => array(
    'source' => 'google',
    'family' => 'Chau Philomene One',
    'stack' => '"Chau Philomene One", sans-serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'chelaone' => array(
    'source' => 'google',
    'family' => 'Chela One',
    'stack' => '"Chela One", display',
    'weights' => array(
      '400'
    )
  ),
  'chelseamarket' => array(
    'source' => 'google',
    'family' => 'Chelsea Market',
    'stack' => '"Chelsea Market", display',
    'weights' => array(
      '400'
    )
  ),
  'chenla' => array(
    'source' => 'google',
    'family' => 'Chenla',
    'stack' => '"Chenla", display',
    'weights' => array(
      '400'
    )
  ),
  'cherish' => array(
    'source' => 'google',
    'family' => 'Cherish',
    'stack' => '"Cherish", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'cherrybombone' => array(
    'source' => 'google',
    'family' => 'Cherry Bomb One',
    'stack' => '"Cherry Bomb One", display',
    'weights' => array(
      '400'
    )
  ),
  'cherrycreamsoda' => array(
    'source' => 'google',
    'family' => 'Cherry Cream Soda',
    'stack' => '"Cherry Cream Soda", display',
    'weights' => array(
      '400'
    )
  ),
  'cherryswash' => array(
    'source' => 'google',
    'family' => 'Cherry Swash',
    'stack' => '"Cherry Swash", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'chewy' => array(
    'source' => 'google',
    'family' => 'Chewy',
    'stack' => '"Chewy", display',
    'weights' => array(
      '400'
    )
  ),
  'chicle' => array(
    'source' => 'google',
    'family' => 'Chicle',
    'stack' => '"Chicle", display',
    'weights' => array(
      '400'
    )
  ),
  'chilanka' => array(
    'source' => 'google',
    'family' => 'Chilanka',
    'stack' => '"Chilanka", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'chironheihk' => array(
    'source' => 'google',
    'family' => 'Chiron Hei HK',
    'stack' => '"Chiron Hei HK", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'chironsunghk' => array(
    'source' => 'google',
    'family' => 'Chiron Sung HK',
    'stack' => '"Chiron Sung HK", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'chivo' => array(
    'source' => 'google',
    'family' => 'Chivo',
    'stack' => '"Chivo", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'chivomono' => array(
    'source' => 'google',
    'family' => 'Chivo Mono',
    'stack' => '"Chivo Mono", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'chocolateclassicalsans' => array(
    'source' => 'google',
    'family' => 'Chocolate Classical Sans',
    'stack' => '"Chocolate Classical Sans", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'chokokutai' => array(
    'source' => 'google',
    'family' => 'Chokokutai',
    'stack' => '"Chokokutai", display',
    'weights' => array(
      '400'
    )
  ),
  'chonburi' => array(
    'source' => 'google',
    'family' => 'Chonburi',
    'stack' => '"Chonburi", display',
    'weights' => array(
      '400'
    )
  ),
  'cinzel' => array(
    'source' => 'google',
    'family' => 'Cinzel',
    'stack' => '"Cinzel", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'cinzeldecorative' => array(
    'source' => 'google',
    'family' => 'Cinzel Decorative',
    'stack' => '"Cinzel Decorative", display',
    'weights' => array(
      '400',
      '700',
      '900'
    )
  ),
  'clickerscript' => array(
    'source' => 'google',
    'family' => 'Clicker Script',
    'stack' => '"Clicker Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'climatecrisis' => array(
    'source' => 'google',
    'family' => 'Climate Crisis',
    'stack' => '"Climate Crisis", display',
    'weights' => array(
      '400'
    )
  ),
  'coda' => array(
    'source' => 'google',
    'family' => 'Coda',
    'stack' => '"Coda", display',
    'weights' => array(
      '400',
      '800'
    )
  ),
  'codystar' => array(
    'source' => 'google',
    'family' => 'Codystar',
    'stack' => '"Codystar", display',
    'weights' => array(
      '300',
      '400'
    )
  ),
  'coiny' => array(
    'source' => 'google',
    'family' => 'Coiny',
    'stack' => '"Coiny", display',
    'weights' => array(
      '400'
    )
  ),
  'combo' => array(
    'source' => 'google',
    'family' => 'Combo',
    'stack' => '"Combo", display',
    'weights' => array(
      '400'
    )
  ),
  'comfortaa' => array(
    'source' => 'google',
    'family' => 'Comfortaa',
    'stack' => '"Comfortaa", display',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'comforter' => array(
    'source' => 'google',
    'family' => 'Comforter',
    'stack' => '"Comforter", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'comforterbrush' => array(
    'source' => 'google',
    'family' => 'Comforter Brush',
    'stack' => '"Comforter Brush", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'comicneue' => array(
    'source' => 'google',
    'family' => 'Comic Neue',
    'stack' => '"Comic Neue", handwriting',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'comicrelief' => array(
    'source' => 'google',
    'family' => 'Comic Relief',
    'stack' => '"Comic Relief", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'comingsoon' => array(
    'source' => 'google',
    'family' => 'Coming Soon',
    'stack' => '"Coming Soon", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'comme' => array(
    'source' => 'google',
    'family' => 'Comme',
    'stack' => '"Comme", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'commissioner' => array(
    'source' => 'google',
    'family' => 'Commissioner',
    'stack' => '"Commissioner", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'concertone' => array(
    'source' => 'google',
    'family' => 'Concert One',
    'stack' => '"Concert One", display',
    'weights' => array(
      '400'
    )
  ),
  'condiment' => array(
    'source' => 'google',
    'family' => 'Condiment',
    'stack' => '"Condiment", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'content' => array(
    'source' => 'google',
    'family' => 'Content',
    'stack' => '"Content", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'contrailone' => array(
    'source' => 'google',
    'family' => 'Contrail One',
    'stack' => '"Contrail One", display',
    'weights' => array(
      '400'
    )
  ),
  'convergence' => array(
    'source' => 'google',
    'family' => 'Convergence',
    'stack' => '"Convergence", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'cookie' => array(
    'source' => 'google',
    'family' => 'Cookie',
    'stack' => '"Cookie", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'copse' => array(
    'source' => 'google',
    'family' => 'Copse',
    'stack' => '"Copse", serif',
    'weights' => array(
      '400'
    )
  ),
  'coralpixels' => array(
    'source' => 'google',
    'family' => 'Coral Pixels',
    'stack' => '"Coral Pixels", display',
    'weights' => array(
      '400'
    )
  ),
  'corben' => array(
    'source' => 'google',
    'family' => 'Corben',
    'stack' => '"Corben", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'corinthia' => array(
    'source' => 'google',
    'family' => 'Corinthia',
    'stack' => '"Corinthia", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'cormorant' => array(
    'source' => 'google',
    'family' => 'Cormorant',
    'stack' => '"Cormorant", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'cormorantgaramond' => array(
    'source' => 'google',
    'family' => 'Cormorant Garamond',
    'stack' => '"Cormorant Garamond", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'cormorantinfant' => array(
    'source' => 'google',
    'family' => 'Cormorant Infant',
    'stack' => '"Cormorant Infant", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'cormorantsc' => array(
    'source' => 'google',
    'family' => 'Cormorant SC',
    'stack' => '"Cormorant SC", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'cormorantunicase' => array(
    'source' => 'google',
    'family' => 'Cormorant Unicase',
    'stack' => '"Cormorant Unicase", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'cormorantupright' => array(
    'source' => 'google',
    'family' => 'Cormorant Upright',
    'stack' => '"Cormorant Upright", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'courgette' => array(
    'source' => 'google',
    'family' => 'Courgette',
    'stack' => '"Courgette", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'courierprime' => array(
    'source' => 'google',
    'family' => 'Courier Prime',
    'stack' => '"Courier Prime", monospace',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'cousine' => array(
    'source' => 'google',
    'family' => 'Cousine',
    'stack' => '"Cousine", monospace',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'coustard' => array(
    'source' => 'google',
    'family' => 'Coustard',
    'stack' => '"Coustard", serif',
    'weights' => array(
      '400',
      '900'
    )
  ),
  'coveredbyyourgrace' => array(
    'source' => 'google',
    'family' => 'Covered By Your Grace',
    'stack' => '"Covered By Your Grace", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'craftygirls' => array(
    'source' => 'google',
    'family' => 'Crafty Girls',
    'stack' => '"Crafty Girls", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'creepster' => array(
    'source' => 'google',
    'family' => 'Creepster',
    'stack' => '"Creepster", display',
    'weights' => array(
      '400'
    )
  ),
  'creteround' => array(
    'source' => 'google',
    'family' => 'Crete Round',
    'stack' => '"Crete Round", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'crimsonpro' => array(
    'source' => 'google',
    'family' => 'Crimson Pro',
    'stack' => '"Crimson Pro", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'crimsontext' => array(
    'source' => 'google',
    'family' => 'Crimson Text',
    'stack' => '"Crimson Text", serif',
    'weights' => array(
      '400',
      '400i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'croissantone' => array(
    'source' => 'google',
    'family' => 'Croissant One',
    'stack' => '"Croissant One", display',
    'weights' => array(
      '400'
    )
  ),
  'crushed' => array(
    'source' => 'google',
    'family' => 'Crushed',
    'stack' => '"Crushed", display',
    'weights' => array(
      '400'
    )
  ),
  'cuprum' => array(
    'source' => 'google',
    'family' => 'Cuprum',
    'stack' => '"Cuprum", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'cutefont' => array(
    'source' => 'google',
    'family' => 'Cute Font',
    'stack' => '"Cute Font", display',
    'weights' => array(
      '400'
    )
  ),
  'cutive' => array(
    'source' => 'google',
    'family' => 'Cutive',
    'stack' => '"Cutive", serif',
    'weights' => array(
      '400'
    )
  ),
  'cutivemono' => array(
    'source' => 'google',
    'family' => 'Cutive Mono',
    'stack' => '"Cutive Mono", monospace',
    'weights' => array(
      '400'
    )
  ),
  'dmmono' => array(
    'source' => 'google',
    'family' => 'DM Mono',
    'stack' => '"DM Mono", monospace',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i'
    )
  ),
  'dmsans' => array(
    'source' => 'google',
    'family' => 'DM Sans',
    'stack' => '"DM Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'dmserifdisplay' => array(
    'source' => 'google',
    'family' => 'DM Serif Display',
    'stack' => '"DM Serif Display", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'dmseriftext' => array(
    'source' => 'google',
    'family' => 'DM Serif Text',
    'stack' => '"DM Serif Text", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'daibannasil' => array(
    'source' => 'google',
    'family' => 'Dai Banna SIL',
    'stack' => '"Dai Banna SIL", serif',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'damion' => array(
    'source' => 'google',
    'family' => 'Damion',
    'stack' => '"Damion", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'dancingscript' => array(
    'source' => 'google',
    'family' => 'Dancing Script',
    'stack' => '"Dancing Script", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'danfo' => array(
    'source' => 'google',
    'family' => 'Danfo',
    'stack' => '"Danfo", serif',
    'weights' => array(
      '400'
    )
  ),
  'dangrek' => array(
    'source' => 'google',
    'family' => 'Dangrek',
    'stack' => '"Dangrek", display',
    'weights' => array(
      '400'
    )
  ),
  'darkergrotesque' => array(
    'source' => 'google',
    'family' => 'Darker Grotesque',
    'stack' => '"Darker Grotesque", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'darumadropone' => array(
    'source' => 'google',
    'family' => 'Darumadrop One',
    'stack' => '"Darumadrop One", display',
    'weights' => array(
      '400'
    )
  ),
  'davidlibre' => array(
    'source' => 'google',
    'family' => 'David Libre',
    'stack' => '"David Libre", serif',
    'weights' => array(
      '400',
      '500',
      '700'
    )
  ),
  'dawningofanewday' => array(
    'source' => 'google',
    'family' => 'Dawning of a New Day',
    'stack' => '"Dawning of a New Day", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'daysone' => array(
    'source' => 'google',
    'family' => 'Days One',
    'stack' => '"Days One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'dekko' => array(
    'source' => 'google',
    'family' => 'Dekko',
    'stack' => '"Dekko", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'delagothicone' => array(
    'source' => 'google',
    'family' => 'Dela Gothic One',
    'stack' => '"Dela Gothic One", display',
    'weights' => array(
      '400'
    )
  ),
  'delicioushandrawn' => array(
    'source' => 'google',
    'family' => 'Delicious Handrawn',
    'stack' => '"Delicious Handrawn", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'delius' => array(
    'source' => 'google',
    'family' => 'Delius',
    'stack' => '"Delius", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'deliusswashcaps' => array(
    'source' => 'google',
    'family' => 'Delius Swash Caps',
    'stack' => '"Delius Swash Caps", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'deliusunicase' => array(
    'source' => 'google',
    'family' => 'Delius Unicase',
    'stack' => '"Delius Unicase", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'dellarespira' => array(
    'source' => 'google',
    'family' => 'Della Respira',
    'stack' => '"Della Respira", serif',
    'weights' => array(
      '400'
    )
  ),
  'denkone' => array(
    'source' => 'google',
    'family' => 'Denk One',
    'stack' => '"Denk One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'devonshire' => array(
    'source' => 'google',
    'family' => 'Devonshire',
    'stack' => '"Devonshire", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'dhurjati' => array(
    'source' => 'google',
    'family' => 'Dhurjati',
    'stack' => '"Dhurjati", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'didactgothic' => array(
    'source' => 'google',
    'family' => 'Didact Gothic',
    'stack' => '"Didact Gothic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'diphylleia' => array(
    'source' => 'google',
    'family' => 'Diphylleia',
    'stack' => '"Diphylleia", serif',
    'weights' => array(
      '400'
    )
  ),
  'diplomata' => array(
    'source' => 'google',
    'family' => 'Diplomata',
    'stack' => '"Diplomata", display',
    'weights' => array(
      '400'
    )
  ),
  'diplomatasc' => array(
    'source' => 'google',
    'family' => 'Diplomata SC',
    'stack' => '"Diplomata SC", display',
    'weights' => array(
      '400'
    )
  ),
  'dohyeon' => array(
    'source' => 'google',
    'family' => 'Do Hyeon',
    'stack' => '"Do Hyeon", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'dokdo' => array(
    'source' => 'google',
    'family' => 'Dokdo',
    'stack' => '"Dokdo", display',
    'weights' => array(
      '400'
    )
  ),
  'domine' => array(
    'source' => 'google',
    'family' => 'Domine',
    'stack' => '"Domine", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'donegalone' => array(
    'source' => 'google',
    'family' => 'Donegal One',
    'stack' => '"Donegal One", serif',
    'weights' => array(
      '400'
    )
  ),
  'dongle' => array(
    'source' => 'google',
    'family' => 'Dongle',
    'stack' => '"Dongle", sans-serif',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'doppioone' => array(
    'source' => 'google',
    'family' => 'Doppio One',
    'stack' => '"Doppio One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'dorsa' => array(
    'source' => 'google',
    'family' => 'Dorsa',
    'stack' => '"Dorsa", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'dosis' => array(
    'source' => 'google',
    'family' => 'Dosis',
    'stack' => '"Dosis", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'dotgothic16' => array(
    'source' => 'google',
    'family' => 'DotGothic16',
    'stack' => '"DotGothic16", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'doto' => array(
    'source' => 'google',
    'family' => 'Doto',
    'stack' => '"Doto", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'drsugiyama' => array(
    'source' => 'google',
    'family' => 'Dr Sugiyama',
    'stack' => '"Dr Sugiyama", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'durusans' => array(
    'source' => 'google',
    'family' => 'Duru Sans',
    'stack' => '"Duru Sans", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'dynapuff' => array(
    'source' => 'google',
    'family' => 'DynaPuff',
    'stack' => '"DynaPuff", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'dynalight' => array(
    'source' => 'google',
    'family' => 'Dynalight',
    'stack' => '"Dynalight", display',
    'weights' => array(
      '400'
    )
  ),
  'ebgaramond' => array(
    'source' => 'google',
    'family' => 'EB Garamond',
    'stack' => '"EB Garamond", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'eaglelake' => array(
    'source' => 'google',
    'family' => 'Eagle Lake',
    'stack' => '"Eagle Lake", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'eastseadokdo' => array(
    'source' => 'google',
    'family' => 'East Sea Dokdo',
    'stack' => '"East Sea Dokdo", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'eater' => array(
    'source' => 'google',
    'family' => 'Eater',
    'stack' => '"Eater", display',
    'weights' => array(
      '400'
    )
  ),
  'economica' => array(
    'source' => 'google',
    'family' => 'Economica',
    'stack' => '"Economica", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'eczar' => array(
    'source' => 'google',
    'family' => 'Eczar',
    'stack' => '"Eczar", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'eduauvicwantarrows' => array(
    'source' => 'google',
    'family' => 'Edu AU VIC WA NT Arrows',
    'stack' => '"Edu AU VIC WA NT Arrows", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'eduauvicwantdots' => array(
    'source' => 'google',
    'family' => 'Edu AU VIC WA NT Dots',
    'stack' => '"Edu AU VIC WA NT Dots", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'eduauvicwantguides' => array(
    'source' => 'google',
    'family' => 'Edu AU VIC WA NT Guides',
    'stack' => '"Edu AU VIC WA NT Guides", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'eduauvicwanthand' => array(
    'source' => 'google',
    'family' => 'Edu AU VIC WA NT Hand',
    'stack' => '"Edu AU VIC WA NT Hand", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'eduauvicwantpre' => array(
    'source' => 'google',
    'family' => 'Edu AU VIC WA NT Pre',
    'stack' => '"Edu AU VIC WA NT Pre", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'edunswactcursive' => array(
    'source' => 'google',
    'family' => 'Edu NSW ACT Cursive',
    'stack' => '"Edu NSW ACT Cursive", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'edunswactfoundation' => array(
    'source' => 'google',
    'family' => 'Edu NSW ACT Foundation',
    'stack' => '"Edu NSW ACT Foundation", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'edunswacthandpre' => array(
    'source' => 'google',
    'family' => 'Edu NSW ACT Hand Pre',
    'stack' => '"Edu NSW ACT Hand Pre", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'eduqldbeginner' => array(
    'source' => 'google',
    'family' => 'Edu QLD Beginner',
    'stack' => '"Edu QLD Beginner", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'eduqldhand' => array(
    'source' => 'google',
    'family' => 'Edu QLD Hand',
    'stack' => '"Edu QLD Hand", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'edusabeginner' => array(
    'source' => 'google',
    'family' => 'Edu SA Beginner',
    'stack' => '"Edu SA Beginner", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'edusahand' => array(
    'source' => 'google',
    'family' => 'Edu SA Hand',
    'stack' => '"Edu SA Hand", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'edutasbeginner' => array(
    'source' => 'google',
    'family' => 'Edu TAS Beginner',
    'stack' => '"Edu TAS Beginner", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'eduvicwantbeginner' => array(
    'source' => 'google',
    'family' => 'Edu VIC WA NT Beginner',
    'stack' => '"Edu VIC WA NT Beginner", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'eduvicwanthand' => array(
    'source' => 'google',
    'family' => 'Edu VIC WA NT Hand',
    'stack' => '"Edu VIC WA NT Hand", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'eduvicwanthandpre' => array(
    'source' => 'google',
    'family' => 'Edu VIC WA NT Hand Pre',
    'stack' => '"Edu VIC WA NT Hand Pre", handwriting',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'elmessiri' => array(
    'source' => 'google',
    'family' => 'El Messiri',
    'stack' => '"El Messiri", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'electrolize' => array(
    'source' => 'google',
    'family' => 'Electrolize',
    'stack' => '"Electrolize", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'elsie' => array(
    'source' => 'google',
    'family' => 'Elsie',
    'stack' => '"Elsie", display',
    'weights' => array(
      '400',
      '900'
    )
  ),
  'elsieswashcaps' => array(
    'source' => 'google',
    'family' => 'Elsie Swash Caps',
    'stack' => '"Elsie Swash Caps", display',
    'weights' => array(
      '400',
      '900'
    )
  ),
  'emblemaone' => array(
    'source' => 'google',
    'family' => 'Emblema One',
    'stack' => '"Emblema One", display',
    'weights' => array(
      '400'
    )
  ),
  'emilyscandy' => array(
    'source' => 'google',
    'family' => 'Emilys Candy',
    'stack' => '"Emilys Candy", display',
    'weights' => array(
      '400'
    )
  ),
  'encodesans' => array(
    'source' => 'google',
    'family' => 'Encode Sans',
    'stack' => '"Encode Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'encodesanscondensed' => array(
    'source' => 'google',
    'family' => 'Encode Sans Condensed',
    'stack' => '"Encode Sans Condensed", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'encodesansexpanded' => array(
    'source' => 'google',
    'family' => 'Encode Sans Expanded',
    'stack' => '"Encode Sans Expanded", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'encodesanssc' => array(
    'source' => 'google',
    'family' => 'Encode Sans SC',
    'stack' => '"Encode Sans SC", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'encodesanssemicondensed' => array(
    'source' => 'google',
    'family' => 'Encode Sans Semi Condensed',
    'stack' => '"Encode Sans Semi Condensed", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'encodesanssemiexpanded' => array(
    'source' => 'google',
    'family' => 'Encode Sans Semi Expanded',
    'stack' => '"Encode Sans Semi Expanded", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'engagement' => array(
    'source' => 'google',
    'family' => 'Engagement',
    'stack' => '"Engagement", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'englebert' => array(
    'source' => 'google',
    'family' => 'Englebert',
    'stack' => '"Englebert", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'enriqueta' => array(
    'source' => 'google',
    'family' => 'Enriqueta',
    'stack' => '"Enriqueta", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'ephesis' => array(
    'source' => 'google',
    'family' => 'Ephesis',
    'stack' => '"Ephesis", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'epilogue' => array(
    'source' => 'google',
    'family' => 'Epilogue',
    'stack' => '"Epilogue", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'ericaone' => array(
    'source' => 'google',
    'family' => 'Erica One',
    'stack' => '"Erica One", display',
    'weights' => array(
      '400'
    )
  ),
  'esteban' => array(
    'source' => 'google',
    'family' => 'Esteban',
    'stack' => '"Esteban", serif',
    'weights' => array(
      '400'
    )
  ),
  'estonia' => array(
    'source' => 'google',
    'family' => 'Estonia',
    'stack' => '"Estonia", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'euphoriascript' => array(
    'source' => 'google',
    'family' => 'Euphoria Script',
    'stack' => '"Euphoria Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ewert' => array(
    'source' => 'google',
    'family' => 'Ewert',
    'stack' => '"Ewert", display',
    'weights' => array(
      '400'
    )
  ),
  'exile' => array(
    'source' => 'google',
    'family' => 'Exile',
    'stack' => '"Exile", display',
    'weights' => array(
      '400'
    )
  ),
  'exo' => array(
    'source' => 'google',
    'family' => 'Exo',
    'stack' => '"Exo", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'exo2' => array(
    'source' => 'google',
    'family' => 'Exo 2',
    'stack' => '"Exo 2", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'expletussans' => array(
    'source' => 'google',
    'family' => 'Expletus Sans',
    'stack' => '"Expletus Sans", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'explora' => array(
    'source' => 'google',
    'family' => 'Explora',
    'stack' => '"Explora", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'facultyglyphic' => array(
    'source' => 'google',
    'family' => 'Faculty Glyphic',
    'stack' => '"Faculty Glyphic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'fahkwang' => array(
    'source' => 'google',
    'family' => 'Fahkwang',
    'stack' => '"Fahkwang", sans-serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'familjengrotesk' => array(
    'source' => 'google',
    'family' => 'Familjen Grotesk',
    'stack' => '"Familjen Grotesk", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'fanwoodtext' => array(
    'source' => 'google',
    'family' => 'Fanwood Text',
    'stack' => '"Fanwood Text", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'farro' => array(
    'source' => 'google',
    'family' => 'Farro',
    'stack' => '"Farro", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '700'
    )
  ),
  'farsan' => array(
    'source' => 'google',
    'family' => 'Farsan',
    'stack' => '"Farsan", display',
    'weights' => array(
      '400'
    )
  ),
  'fascinate' => array(
    'source' => 'google',
    'family' => 'Fascinate',
    'stack' => '"Fascinate", display',
    'weights' => array(
      '400'
    )
  ),
  'fascinateinline' => array(
    'source' => 'google',
    'family' => 'Fascinate Inline',
    'stack' => '"Fascinate Inline", display',
    'weights' => array(
      '400'
    )
  ),
  'fasterone' => array(
    'source' => 'google',
    'family' => 'Faster One',
    'stack' => '"Faster One", display',
    'weights' => array(
      '400'
    )
  ),
  'fasthand' => array(
    'source' => 'google',
    'family' => 'Fasthand',
    'stack' => '"Fasthand", display',
    'weights' => array(
      '400'
    )
  ),
  'faunaone' => array(
    'source' => 'google',
    'family' => 'Fauna One',
    'stack' => '"Fauna One", serif',
    'weights' => array(
      '400'
    )
  ),
  'faustina' => array(
    'source' => 'google',
    'family' => 'Faustina',
    'stack' => '"Faustina", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'federant' => array(
    'source' => 'google',
    'family' => 'Federant',
    'stack' => '"Federant", display',
    'weights' => array(
      '400'
    )
  ),
  'federo' => array(
    'source' => 'google',
    'family' => 'Federo',
    'stack' => '"Federo", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'felipa' => array(
    'source' => 'google',
    'family' => 'Felipa',
    'stack' => '"Felipa", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'fenix' => array(
    'source' => 'google',
    'family' => 'Fenix',
    'stack' => '"Fenix", serif',
    'weights' => array(
      '400'
    )
  ),
  'festive' => array(
    'source' => 'google',
    'family' => 'Festive',
    'stack' => '"Festive", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'figtree' => array(
    'source' => 'google',
    'family' => 'Figtree',
    'stack' => '"Figtree", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'fingerpaint' => array(
    'source' => 'google',
    'family' => 'Finger Paint',
    'stack' => '"Finger Paint", display',
    'weights' => array(
      '400'
    )
  ),
  'finlandica' => array(
    'source' => 'google',
    'family' => 'Finlandica',
    'stack' => '"Finlandica", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'firacode' => array(
    'source' => 'google',
    'family' => 'Fira Code',
    'stack' => '"Fira Code", monospace',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'firamono' => array(
    'source' => 'google',
    'family' => 'Fira Mono',
    'stack' => '"Fira Mono", monospace',
    'weights' => array(
      '400',
      '500',
      '700'
    )
  ),
  'firasans' => array(
    'source' => 'google',
    'family' => 'Fira Sans',
    'stack' => '"Fira Sans", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'firasanscondensed' => array(
    'source' => 'google',
    'family' => 'Fira Sans Condensed',
    'stack' => '"Fira Sans Condensed", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'firasansextracondensed' => array(
    'source' => 'google',
    'family' => 'Fira Sans Extra Condensed',
    'stack' => '"Fira Sans Extra Condensed", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'fjallaone' => array(
    'source' => 'google',
    'family' => 'Fjalla One',
    'stack' => '"Fjalla One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'fjordone' => array(
    'source' => 'google',
    'family' => 'Fjord One',
    'stack' => '"Fjord One", serif',
    'weights' => array(
      '400'
    )
  ),
  'flamenco' => array(
    'source' => 'google',
    'family' => 'Flamenco',
    'stack' => '"Flamenco", display',
    'weights' => array(
      '300',
      '400'
    )
  ),
  'flavors' => array(
    'source' => 'google',
    'family' => 'Flavors',
    'stack' => '"Flavors", display',
    'weights' => array(
      '400'
    )
  ),
  'fleurdeleah' => array(
    'source' => 'google',
    'family' => 'Fleur De Leah',
    'stack' => '"Fleur De Leah", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'flowblock' => array(
    'source' => 'google',
    'family' => 'Flow Block',
    'stack' => '"Flow Block", display',
    'weights' => array(
      '400'
    )
  ),
  'flowcircular' => array(
    'source' => 'google',
    'family' => 'Flow Circular',
    'stack' => '"Flow Circular", display',
    'weights' => array(
      '400'
    )
  ),
  'flowrounded' => array(
    'source' => 'google',
    'family' => 'Flow Rounded',
    'stack' => '"Flow Rounded", display',
    'weights' => array(
      '400'
    )
  ),
  'foldit' => array(
    'source' => 'google',
    'family' => 'Foldit',
    'stack' => '"Foldit", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'fondamento' => array(
    'source' => 'google',
    'family' => 'Fondamento',
    'stack' => '"Fondamento", handwriting',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'fontdinerswanky' => array(
    'source' => 'google',
    'family' => 'Fontdiner Swanky',
    'stack' => '"Fontdiner Swanky", display',
    'weights' => array(
      '400'
    )
  ),
  'forum' => array(
    'source' => 'google',
    'family' => 'Forum',
    'stack' => '"Forum", display',
    'weights' => array(
      '400'
    )
  ),
  'fragmentmono' => array(
    'source' => 'google',
    'family' => 'Fragment Mono',
    'stack' => '"Fragment Mono", monospace',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'francoisone' => array(
    'source' => 'google',
    'family' => 'Francois One',
    'stack' => '"Francois One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'frankruhllibre' => array(
    'source' => 'google',
    'family' => 'Frank Ruhl Libre',
    'stack' => '"Frank Ruhl Libre", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'fraunces' => array(
    'source' => 'google',
    'family' => 'Fraunces',
    'stack' => '"Fraunces", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'freckleface' => array(
    'source' => 'google',
    'family' => 'Freckle Face',
    'stack' => '"Freckle Face", display',
    'weights' => array(
      '400'
    )
  ),
  'frederickathegreat' => array(
    'source' => 'google',
    'family' => 'Fredericka the Great',
    'stack' => '"Fredericka the Great", display',
    'weights' => array(
      '400'
    )
  ),
  'fredoka' => array(
    'source' => 'google',
    'family' => 'Fredoka',
    'stack' => '"Fredoka", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'freehand' => array(
    'source' => 'google',
    'family' => 'Freehand',
    'stack' => '"Freehand", display',
    'weights' => array(
      '400'
    )
  ),
  'freeman' => array(
    'source' => 'google',
    'family' => 'Freeman',
    'stack' => '"Freeman", display',
    'weights' => array(
      '400'
    )
  ),
  'fresca' => array(
    'source' => 'google',
    'family' => 'Fresca',
    'stack' => '"Fresca", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'frijole' => array(
    'source' => 'google',
    'family' => 'Frijole',
    'stack' => '"Frijole", display',
    'weights' => array(
      '400'
    )
  ),
  'fruktur' => array(
    'source' => 'google',
    'family' => 'Fruktur',
    'stack' => '"Fruktur", display',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'fugazone' => array(
    'source' => 'google',
    'family' => 'Fugaz One',
    'stack' => '"Fugaz One", display',
    'weights' => array(
      '400'
    )
  ),
  'fuggles' => array(
    'source' => 'google',
    'family' => 'Fuggles',
    'stack' => '"Fuggles", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'funneldisplay' => array(
    'source' => 'google',
    'family' => 'Funnel Display',
    'stack' => '"Funnel Display", display',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'funnelsans' => array(
    'source' => 'google',
    'family' => 'Funnel Sans',
    'stack' => '"Funnel Sans", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'fustat' => array(
    'source' => 'google',
    'family' => 'Fustat',
    'stack' => '"Fustat", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'fuzzybubbles' => array(
    'source' => 'google',
    'family' => 'Fuzzy Bubbles',
    'stack' => '"Fuzzy Bubbles", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'gfsdidot' => array(
    'source' => 'google',
    'family' => 'GFS Didot',
    'stack' => '"GFS Didot", serif',
    'weights' => array(
      '400'
    )
  ),
  'gfsneohellenic' => array(
    'source' => 'google',
    'family' => 'GFS Neohellenic',
    'stack' => '"GFS Neohellenic", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'gamaamli' => array(
    'source' => 'google',
    'family' => 'Ga Maamli',
    'stack' => '"Ga Maamli", display',
    'weights' => array(
      '400'
    )
  ),
  'gabarito' => array(
    'source' => 'google',
    'family' => 'Gabarito',
    'stack' => '"Gabarito", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'gabriela' => array(
    'source' => 'google',
    'family' => 'Gabriela',
    'stack' => '"Gabriela", serif',
    'weights' => array(
      '400'
    )
  ),
  'gaegu' => array(
    'source' => 'google',
    'family' => 'Gaegu',
    'stack' => '"Gaegu", handwriting',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'gafata' => array(
    'source' => 'google',
    'family' => 'Gafata',
    'stack' => '"Gafata", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'gajrajone' => array(
    'source' => 'google',
    'family' => 'Gajraj One',
    'stack' => '"Gajraj One", display',
    'weights' => array(
      '400'
    )
  ),
  'galada' => array(
    'source' => 'google',
    'family' => 'Galada',
    'stack' => '"Galada", display',
    'weights' => array(
      '400'
    )
  ),
  'galdeano' => array(
    'source' => 'google',
    'family' => 'Galdeano',
    'stack' => '"Galdeano", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'galindo' => array(
    'source' => 'google',
    'family' => 'Galindo',
    'stack' => '"Galindo", display',
    'weights' => array(
      '400'
    )
  ),
  'gamjaflower' => array(
    'source' => 'google',
    'family' => 'Gamja Flower',
    'stack' => '"Gamja Flower", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'gantari' => array(
    'source' => 'google',
    'family' => 'Gantari',
    'stack' => '"Gantari", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'gasoekone' => array(
    'source' => 'google',
    'family' => 'Gasoek One',
    'stack' => '"Gasoek One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'gayathri' => array(
    'source' => 'google',
    'family' => 'Gayathri',
    'stack' => '"Gayathri", sans-serif',
    'weights' => array(
      '100',
      '400',
      '700'
    )
  ),
  'geist' => array(
    'source' => 'google',
    'family' => 'Geist',
    'stack' => '"Geist", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'geistmono' => array(
    'source' => 'google',
    'family' => 'Geist Mono',
    'stack' => '"Geist Mono", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'gelasio' => array(
    'source' => 'google',
    'family' => 'Gelasio',
    'stack' => '"Gelasio", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'gemunulibre' => array(
    'source' => 'google',
    'family' => 'Gemunu Libre',
    'stack' => '"Gemunu Libre", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'genos' => array(
    'source' => 'google',
    'family' => 'Genos',
    'stack' => '"Genos", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'gentiumbookplus' => array(
    'source' => 'google',
    'family' => 'Gentium Book Plus',
    'stack' => '"Gentium Book Plus", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'gentiumplus' => array(
    'source' => 'google',
    'family' => 'Gentium Plus',
    'stack' => '"Gentium Plus", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'geo' => array(
    'source' => 'google',
    'family' => 'Geo',
    'stack' => '"Geo", sans-serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'geologica' => array(
    'source' => 'google',
    'family' => 'Geologica',
    'stack' => '"Geologica", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'georama' => array(
    'source' => 'google',
    'family' => 'Georama',
    'stack' => '"Georama", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'geostar' => array(
    'source' => 'google',
    'family' => 'Geostar',
    'stack' => '"Geostar", display',
    'weights' => array(
      '400'
    )
  ),
  'geostarfill' => array(
    'source' => 'google',
    'family' => 'Geostar Fill',
    'stack' => '"Geostar Fill", display',
    'weights' => array(
      '400'
    )
  ),
  'germaniaone' => array(
    'source' => 'google',
    'family' => 'Germania One',
    'stack' => '"Germania One", display',
    'weights' => array(
      '400'
    )
  ),
  'gideonroman' => array(
    'source' => 'google',
    'family' => 'Gideon Roman',
    'stack' => '"Gideon Roman", display',
    'weights' => array(
      '400'
    )
  ),
  'gidole' => array(
    'source' => 'google',
    'family' => 'Gidole',
    'stack' => '"Gidole", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'gidugu' => array(
    'source' => 'google',
    'family' => 'Gidugu',
    'stack' => '"Gidugu", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'gildadisplay' => array(
    'source' => 'google',
    'family' => 'Gilda Display',
    'stack' => '"Gilda Display", serif',
    'weights' => array(
      '400'
    )
  ),
  'girassol' => array(
    'source' => 'google',
    'family' => 'Girassol',
    'stack' => '"Girassol", display',
    'weights' => array(
      '400'
    )
  ),
  'giveyouglory' => array(
    'source' => 'google',
    'family' => 'Give You Glory',
    'stack' => '"Give You Glory", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'glassantiqua' => array(
    'source' => 'google',
    'family' => 'Glass Antiqua',
    'stack' => '"Glass Antiqua", display',
    'weights' => array(
      '400'
    )
  ),
  'glegoo' => array(
    'source' => 'google',
    'family' => 'Glegoo',
    'stack' => '"Glegoo", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'gloock' => array(
    'source' => 'google',
    'family' => 'Gloock',
    'stack' => '"Gloock", serif',
    'weights' => array(
      '400'
    )
  ),
  'gloriahallelujah' => array(
    'source' => 'google',
    'family' => 'Gloria Hallelujah',
    'stack' => '"Gloria Hallelujah", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'glory' => array(
    'source' => 'google',
    'family' => 'Glory',
    'stack' => '"Glory", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'gluten' => array(
    'source' => 'google',
    'family' => 'Gluten',
    'stack' => '"Gluten", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'goblinone' => array(
    'source' => 'google',
    'family' => 'Goblin One',
    'stack' => '"Goblin One", display',
    'weights' => array(
      '400'
    )
  ),
  'gochihand' => array(
    'source' => 'google',
    'family' => 'Gochi Hand',
    'stack' => '"Gochi Hand", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'goldman' => array(
    'source' => 'google',
    'family' => 'Goldman',
    'stack' => '"Goldman", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'golostext' => array(
    'source' => 'google',
    'family' => 'Golos Text',
    'stack' => '"Golos Text", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'gorditas' => array(
    'source' => 'google',
    'family' => 'Gorditas',
    'stack' => '"Gorditas", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'gothica1' => array(
    'source' => 'google',
    'family' => 'Gothic A1',
    'stack' => '"Gothic A1", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'gotu' => array(
    'source' => 'google',
    'family' => 'Gotu',
    'stack' => '"Gotu", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'goudybookletter1911' => array(
    'source' => 'google',
    'family' => 'Goudy Bookletter 1911',
    'stack' => '"Goudy Bookletter 1911", serif',
    'weights' => array(
      '400'
    )
  ),
  'gowunbatang' => array(
    'source' => 'google',
    'family' => 'Gowun Batang',
    'stack' => '"Gowun Batang", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'gowundodum' => array(
    'source' => 'google',
    'family' => 'Gowun Dodum',
    'stack' => '"Gowun Dodum", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'graduate' => array(
    'source' => 'google',
    'family' => 'Graduate',
    'stack' => '"Graduate", serif',
    'weights' => array(
      '400'
    )
  ),
  'grandhotel' => array(
    'source' => 'google',
    'family' => 'Grand Hotel',
    'stack' => '"Grand Hotel", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'grandifloraone' => array(
    'source' => 'google',
    'family' => 'Grandiflora One',
    'stack' => '"Grandiflora One", serif',
    'weights' => array(
      '400'
    )
  ),
  'grandstander' => array(
    'source' => 'google',
    'family' => 'Grandstander',
    'stack' => '"Grandstander", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'grapenuts' => array(
    'source' => 'google',
    'family' => 'Grape Nuts',
    'stack' => '"Grape Nuts", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'gravitasone' => array(
    'source' => 'google',
    'family' => 'Gravitas One',
    'stack' => '"Gravitas One", display',
    'weights' => array(
      '400'
    )
  ),
  'greatvibes' => array(
    'source' => 'google',
    'family' => 'Great Vibes',
    'stack' => '"Great Vibes", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'grechenfuemen' => array(
    'source' => 'google',
    'family' => 'Grechen Fuemen',
    'stack' => '"Grechen Fuemen", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'grenze' => array(
    'source' => 'google',
    'family' => 'Grenze',
    'stack' => '"Grenze", serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'grenzegotisch' => array(
    'source' => 'google',
    'family' => 'Grenze Gotisch',
    'stack' => '"Grenze Gotisch", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'greyqo' => array(
    'source' => 'google',
    'family' => 'Grey Qo',
    'stack' => '"Grey Qo", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'griffy' => array(
    'source' => 'google',
    'family' => 'Griffy',
    'stack' => '"Griffy", display',
    'weights' => array(
      '400'
    )
  ),
  'gruppo' => array(
    'source' => 'google',
    'family' => 'Gruppo',
    'stack' => '"Gruppo", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'gudea' => array(
    'source' => 'google',
    'family' => 'Gudea',
    'stack' => '"Gudea", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'gugi' => array(
    'source' => 'google',
    'family' => 'Gugi',
    'stack' => '"Gugi", display',
    'weights' => array(
      '400'
    )
  ),
  'gulzar' => array(
    'source' => 'google',
    'family' => 'Gulzar',
    'stack' => '"Gulzar", serif',
    'weights' => array(
      '400'
    )
  ),
  'gupter' => array(
    'source' => 'google',
    'family' => 'Gupter',
    'stack' => '"Gupter", serif',
    'weights' => array(
      '400',
      '500',
      '700'
    )
  ),
  'gurajada' => array(
    'source' => 'google',
    'family' => 'Gurajada',
    'stack' => '"Gurajada", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'gwendolyn' => array(
    'source' => 'google',
    'family' => 'Gwendolyn',
    'stack' => '"Gwendolyn", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'habibi' => array(
    'source' => 'google',
    'family' => 'Habibi',
    'stack' => '"Habibi", serif',
    'weights' => array(
      '400'
    )
  ),
  'hachimarupop' => array(
    'source' => 'google',
    'family' => 'Hachi Maru Pop',
    'stack' => '"Hachi Maru Pop", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'hahmlet' => array(
    'source' => 'google',
    'family' => 'Hahmlet',
    'stack' => '"Hahmlet", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'halant' => array(
    'source' => 'google',
    'family' => 'Halant',
    'stack' => '"Halant", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'hammersmithone' => array(
    'source' => 'google',
    'family' => 'Hammersmith One',
    'stack' => '"Hammersmith One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'hanalei' => array(
    'source' => 'google',
    'family' => 'Hanalei',
    'stack' => '"Hanalei", display',
    'weights' => array(
      '400'
    )
  ),
  'hanaleifill' => array(
    'source' => 'google',
    'family' => 'Hanalei Fill',
    'stack' => '"Hanalei Fill", display',
    'weights' => array(
      '400'
    )
  ),
  'handjet' => array(
    'source' => 'google',
    'family' => 'Handjet',
    'stack' => '"Handjet", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'handlee' => array(
    'source' => 'google',
    'family' => 'Handlee',
    'stack' => '"Handlee", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'hankengrotesk' => array(
    'source' => 'google',
    'family' => 'Hanken Grotesk',
    'stack' => '"Hanken Grotesk", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'hanuman' => array(
    'source' => 'google',
    'family' => 'Hanuman',
    'stack' => '"Hanuman", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'happymonkey' => array(
    'source' => 'google',
    'family' => 'Happy Monkey',
    'stack' => '"Happy Monkey", display',
    'weights' => array(
      '400'
    )
  ),
  'harmattan' => array(
    'source' => 'google',
    'family' => 'Harmattan',
    'stack' => '"Harmattan", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'headlandone' => array(
    'source' => 'google',
    'family' => 'Headland One',
    'stack' => '"Headland One", serif',
    'weights' => array(
      '400'
    )
  ),
  'hedvigletterssans' => array(
    'source' => 'google',
    'family' => 'Hedvig Letters Sans',
    'stack' => '"Hedvig Letters Sans", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'hedviglettersserif' => array(
    'source' => 'google',
    'family' => 'Hedvig Letters Serif',
    'stack' => '"Hedvig Letters Serif", serif',
    'weights' => array(
      '400'
    )
  ),
  'heebo' => array(
    'source' => 'google',
    'family' => 'Heebo',
    'stack' => '"Heebo", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'hennypenny' => array(
    'source' => 'google',
    'family' => 'Henny Penny',
    'stack' => '"Henny Penny", display',
    'weights' => array(
      '400'
    )
  ),
  'heptaslab' => array(
    'source' => 'google',
    'family' => 'Hepta Slab',
    'stack' => '"Hepta Slab", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'herrvonmuellerhoff' => array(
    'source' => 'google',
    'family' => 'Herr Von Muellerhoff',
    'stack' => '"Herr Von Muellerhoff", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'himelody' => array(
    'source' => 'google',
    'family' => 'Hi Melody',
    'stack' => '"Hi Melody", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'hinamincho' => array(
    'source' => 'google',
    'family' => 'Hina Mincho',
    'stack' => '"Hina Mincho", serif',
    'weights' => array(
      '400'
    )
  ),
  'hind' => array(
    'source' => 'google',
    'family' => 'Hind',
    'stack' => '"Hind", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'hindguntur' => array(
    'source' => 'google',
    'family' => 'Hind Guntur',
    'stack' => '"Hind Guntur", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'hindmadurai' => array(
    'source' => 'google',
    'family' => 'Hind Madurai',
    'stack' => '"Hind Madurai", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'hindmysuru' => array(
    'source' => 'google',
    'family' => 'Hind Mysuru',
    'stack' => '"Hind Mysuru", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'hindsiliguri' => array(
    'source' => 'google',
    'family' => 'Hind Siliguri',
    'stack' => '"Hind Siliguri", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'hindvadodara' => array(
    'source' => 'google',
    'family' => 'Hind Vadodara',
    'stack' => '"Hind Vadodara", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'holtwoodonesc' => array(
    'source' => 'google',
    'family' => 'Holtwood One SC',
    'stack' => '"Holtwood One SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'homemadeapple' => array(
    'source' => 'google',
    'family' => 'Homemade Apple',
    'stack' => '"Homemade Apple", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'homenaje' => array(
    'source' => 'google',
    'family' => 'Homenaje',
    'stack' => '"Homenaje", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'honk' => array(
    'source' => 'google',
    'family' => 'Honk',
    'stack' => '"Honk", display',
    'weights' => array(
      '400'
    )
  ),
  'hostgrotesk' => array(
    'source' => 'google',
    'family' => 'Host Grotesk',
    'stack' => '"Host Grotesk", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'hubballi' => array(
    'source' => 'google',
    'family' => 'Hubballi',
    'stack' => '"Hubballi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'hubotsans' => array(
    'source' => 'google',
    'family' => 'Hubot Sans',
    'stack' => '"Hubot Sans", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'huninn' => array(
    'source' => 'google',
    'family' => 'Huninn',
    'stack' => '"Huninn", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'hurricane' => array(
    'source' => 'google',
    'family' => 'Hurricane',
    'stack' => '"Hurricane", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ibmplexmono' => array(
    'source' => 'google',
    'family' => 'IBM Plex Mono',
    'stack' => '"IBM Plex Mono", monospace',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'ibmplexsans' => array(
    'source' => 'google',
    'family' => 'IBM Plex Sans',
    'stack' => '"IBM Plex Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'ibmplexsansarabic' => array(
    'source' => 'google',
    'family' => 'IBM Plex Sans Arabic',
    'stack' => '"IBM Plex Sans Arabic", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'ibmplexsanscondensed' => array(
    'source' => 'google',
    'family' => 'IBM Plex Sans Condensed',
    'stack' => '"IBM Plex Sans Condensed", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'ibmplexsansdevanagari' => array(
    'source' => 'google',
    'family' => 'IBM Plex Sans Devanagari',
    'stack' => '"IBM Plex Sans Devanagari", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'ibmplexsanshebrew' => array(
    'source' => 'google',
    'family' => 'IBM Plex Sans Hebrew',
    'stack' => '"IBM Plex Sans Hebrew", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'ibmplexsansjp' => array(
    'source' => 'google',
    'family' => 'IBM Plex Sans JP',
    'stack' => '"IBM Plex Sans JP", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'ibmplexsanskr' => array(
    'source' => 'google',
    'family' => 'IBM Plex Sans KR',
    'stack' => '"IBM Plex Sans KR", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'ibmplexsansthai' => array(
    'source' => 'google',
    'family' => 'IBM Plex Sans Thai',
    'stack' => '"IBM Plex Sans Thai", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'ibmplexsansthailooped' => array(
    'source' => 'google',
    'family' => 'IBM Plex Sans Thai Looped',
    'stack' => '"IBM Plex Sans Thai Looped", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'ibmplexserif' => array(
    'source' => 'google',
    'family' => 'IBM Plex Serif',
    'stack' => '"IBM Plex Serif", serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'imfelldwpica' => array(
    'source' => 'google',
    'family' => 'IM Fell DW Pica',
    'stack' => '"IM Fell DW Pica", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'imfelldwpicasc' => array(
    'source' => 'google',
    'family' => 'IM Fell DW Pica SC',
    'stack' => '"IM Fell DW Pica SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'imfelldoublepica' => array(
    'source' => 'google',
    'family' => 'IM Fell Double Pica',
    'stack' => '"IM Fell Double Pica", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'imfelldoublepicasc' => array(
    'source' => 'google',
    'family' => 'IM Fell Double Pica SC',
    'stack' => '"IM Fell Double Pica SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'imfellenglish' => array(
    'source' => 'google',
    'family' => 'IM Fell English',
    'stack' => '"IM Fell English", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'imfellenglishsc' => array(
    'source' => 'google',
    'family' => 'IM Fell English SC',
    'stack' => '"IM Fell English SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'imfellfrenchcanon' => array(
    'source' => 'google',
    'family' => 'IM Fell French Canon',
    'stack' => '"IM Fell French Canon", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'imfellfrenchcanonsc' => array(
    'source' => 'google',
    'family' => 'IM Fell French Canon SC',
    'stack' => '"IM Fell French Canon SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'imfellgreatprimer' => array(
    'source' => 'google',
    'family' => 'IM Fell Great Primer',
    'stack' => '"IM Fell Great Primer", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'imfellgreatprimersc' => array(
    'source' => 'google',
    'family' => 'IM Fell Great Primer SC',
    'stack' => '"IM Fell Great Primer SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'iansui' => array(
    'source' => 'google',
    'family' => 'Iansui',
    'stack' => '"Iansui", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ibarrarealnova' => array(
    'source' => 'google',
    'family' => 'Ibarra Real Nova',
    'stack' => '"Ibarra Real Nova", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'iceberg' => array(
    'source' => 'google',
    'family' => 'Iceberg',
    'stack' => '"Iceberg", display',
    'weights' => array(
      '400'
    )
  ),
  'iceland' => array(
    'source' => 'google',
    'family' => 'Iceland',
    'stack' => '"Iceland", display',
    'weights' => array(
      '400'
    )
  ),
  'imbue' => array(
    'source' => 'google',
    'family' => 'Imbue',
    'stack' => '"Imbue", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'imperialscript' => array(
    'source' => 'google',
    'family' => 'Imperial Script',
    'stack' => '"Imperial Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'imprima' => array(
    'source' => 'google',
    'family' => 'Imprima',
    'stack' => '"Imprima", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'inclusivesans' => array(
    'source' => 'google',
    'family' => 'Inclusive Sans',
    'stack' => '"Inclusive Sans", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'inconsolata' => array(
    'source' => 'google',
    'family' => 'Inconsolata',
    'stack' => '"Inconsolata", monospace',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'inder' => array(
    'source' => 'google',
    'family' => 'Inder',
    'stack' => '"Inder", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'indieflower' => array(
    'source' => 'google',
    'family' => 'Indie Flower',
    'stack' => '"Indie Flower", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ingriddarling' => array(
    'source' => 'google',
    'family' => 'Ingrid Darling',
    'stack' => '"Ingrid Darling", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'inika' => array(
    'source' => 'google',
    'family' => 'Inika',
    'stack' => '"Inika", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'inknutantiqua' => array(
    'source' => 'google',
    'family' => 'Inknut Antiqua',
    'stack' => '"Inknut Antiqua", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'inriasans' => array(
    'source' => 'google',
    'family' => 'Inria Sans',
    'stack' => '"Inria Sans", sans-serif',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'inriaserif' => array(
    'source' => 'google',
    'family' => 'Inria Serif',
    'stack' => '"Inria Serif", serif',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'inspiration' => array(
    'source' => 'google',
    'family' => 'Inspiration',
    'stack' => '"Inspiration", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'instrumentsans' => array(
    'source' => 'google',
    'family' => 'Instrument Sans',
    'stack' => '"Instrument Sans", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'instrumentserif' => array(
    'source' => 'google',
    'family' => 'Instrument Serif',
    'stack' => '"Instrument Serif", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'inter' => array(
    'source' => 'google',
    'family' => 'Inter',
    'stack' => '"Inter", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'intertight' => array(
    'source' => 'google',
    'family' => 'Inter Tight',
    'stack' => '"Inter Tight", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'irishgrover' => array(
    'source' => 'google',
    'family' => 'Irish Grover',
    'stack' => '"Irish Grover", display',
    'weights' => array(
      '400'
    )
  ),
  'islandmoments' => array(
    'source' => 'google',
    'family' => 'Island Moments',
    'stack' => '"Island Moments", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'istokweb' => array(
    'source' => 'google',
    'family' => 'Istok Web',
    'stack' => '"Istok Web", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'italiana' => array(
    'source' => 'google',
    'family' => 'Italiana',
    'stack' => '"Italiana", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'italianno' => array(
    'source' => 'google',
    'family' => 'Italianno',
    'stack' => '"Italianno", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'itim' => array(
    'source' => 'google',
    'family' => 'Itim',
    'stack' => '"Itim", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'jacquard12' => array(
    'source' => 'google',
    'family' => 'Jacquard 12',
    'stack' => '"Jacquard 12", display',
    'weights' => array(
      '400'
    )
  ),
  'jacquard12charted' => array(
    'source' => 'google',
    'family' => 'Jacquard 12 Charted',
    'stack' => '"Jacquard 12 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'jacquard24' => array(
    'source' => 'google',
    'family' => 'Jacquard 24',
    'stack' => '"Jacquard 24", display',
    'weights' => array(
      '400'
    )
  ),
  'jacquard24charted' => array(
    'source' => 'google',
    'family' => 'Jacquard 24 Charted',
    'stack' => '"Jacquard 24 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'jacquardabastarda9' => array(
    'source' => 'google',
    'family' => 'Jacquarda Bastarda 9',
    'stack' => '"Jacquarda Bastarda 9", display',
    'weights' => array(
      '400'
    )
  ),
  'jacquardabastarda9charted' => array(
    'source' => 'google',
    'family' => 'Jacquarda Bastarda 9 Charted',
    'stack' => '"Jacquarda Bastarda 9 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'jacquesfrancois' => array(
    'source' => 'google',
    'family' => 'Jacques Francois',
    'stack' => '"Jacques Francois", serif',
    'weights' => array(
      '400'
    )
  ),
  'jacquesfrancoisshadow' => array(
    'source' => 'google',
    'family' => 'Jacques Francois Shadow',
    'stack' => '"Jacques Francois Shadow", display',
    'weights' => array(
      '400'
    )
  ),
  'jaini' => array(
    'source' => 'google',
    'family' => 'Jaini',
    'stack' => '"Jaini", display',
    'weights' => array(
      '400'
    )
  ),
  'jainipurva' => array(
    'source' => 'google',
    'family' => 'Jaini Purva',
    'stack' => '"Jaini Purva", display',
    'weights' => array(
      '400'
    )
  ),
  'jaldi' => array(
    'source' => 'google',
    'family' => 'Jaldi',
    'stack' => '"Jaldi", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'jaro' => array(
    'source' => 'google',
    'family' => 'Jaro',
    'stack' => '"Jaro", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'jersey10' => array(
    'source' => 'google',
    'family' => 'Jersey 10',
    'stack' => '"Jersey 10", display',
    'weights' => array(
      '400'
    )
  ),
  'jersey10charted' => array(
    'source' => 'google',
    'family' => 'Jersey 10 Charted',
    'stack' => '"Jersey 10 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'jersey15' => array(
    'source' => 'google',
    'family' => 'Jersey 15',
    'stack' => '"Jersey 15", display',
    'weights' => array(
      '400'
    )
  ),
  'jersey15charted' => array(
    'source' => 'google',
    'family' => 'Jersey 15 Charted',
    'stack' => '"Jersey 15 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'jersey20' => array(
    'source' => 'google',
    'family' => 'Jersey 20',
    'stack' => '"Jersey 20", display',
    'weights' => array(
      '400'
    )
  ),
  'jersey20charted' => array(
    'source' => 'google',
    'family' => 'Jersey 20 Charted',
    'stack' => '"Jersey 20 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'jersey25' => array(
    'source' => 'google',
    'family' => 'Jersey 25',
    'stack' => '"Jersey 25", display',
    'weights' => array(
      '400'
    )
  ),
  'jersey25charted' => array(
    'source' => 'google',
    'family' => 'Jersey 25 Charted',
    'stack' => '"Jersey 25 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'jetbrainsmono' => array(
    'source' => 'google',
    'family' => 'JetBrains Mono',
    'stack' => '"JetBrains Mono", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'jimnightshade' => array(
    'source' => 'google',
    'family' => 'Jim Nightshade',
    'stack' => '"Jim Nightshade", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'joan' => array(
    'source' => 'google',
    'family' => 'Joan',
    'stack' => '"Joan", serif',
    'weights' => array(
      '400'
    )
  ),
  'jockeyone' => array(
    'source' => 'google',
    'family' => 'Jockey One',
    'stack' => '"Jockey One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'jollylodger' => array(
    'source' => 'google',
    'family' => 'Jolly Lodger',
    'stack' => '"Jolly Lodger", display',
    'weights' => array(
      '400'
    )
  ),
  'jomhuria' => array(
    'source' => 'google',
    'family' => 'Jomhuria',
    'stack' => '"Jomhuria", display',
    'weights' => array(
      '400'
    )
  ),
  'jomolhari' => array(
    'source' => 'google',
    'family' => 'Jomolhari',
    'stack' => '"Jomolhari", serif',
    'weights' => array(
      '400'
    )
  ),
  'josefinsans' => array(
    'source' => 'google',
    'family' => 'Josefin Sans',
    'stack' => '"Josefin Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'josefinslab' => array(
    'source' => 'google',
    'family' => 'Josefin Slab',
    'stack' => '"Josefin Slab", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'jost' => array(
    'source' => 'google',
    'family' => 'Jost',
    'stack' => '"Jost", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'jotione' => array(
    'source' => 'google',
    'family' => 'Joti One',
    'stack' => '"Joti One", display',
    'weights' => array(
      '400'
    )
  ),
  'jua' => array(
    'source' => 'google',
    'family' => 'Jua',
    'stack' => '"Jua", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'judson' => array(
    'source' => 'google',
    'family' => 'Judson',
    'stack' => '"Judson", serif',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'julee' => array(
    'source' => 'google',
    'family' => 'Julee',
    'stack' => '"Julee", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'juliussansone' => array(
    'source' => 'google',
    'family' => 'Julius Sans One',
    'stack' => '"Julius Sans One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'junge' => array(
    'source' => 'google',
    'family' => 'Junge',
    'stack' => '"Junge", serif',
    'weights' => array(
      '400'
    )
  ),
  'jura' => array(
    'source' => 'google',
    'family' => 'Jura',
    'stack' => '"Jura", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'justanotherhand' => array(
    'source' => 'google',
    'family' => 'Just Another Hand',
    'stack' => '"Just Another Hand", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'justmeagaindownhere' => array(
    'source' => 'google',
    'family' => 'Just Me Again Down Here',
    'stack' => '"Just Me Again Down Here", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'k2d' => array(
    'source' => 'google',
    'family' => 'K2D',
    'stack' => '"K2D", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i'
    )
  ),
  'kablammo' => array(
    'source' => 'google',
    'family' => 'Kablammo',
    'stack' => '"Kablammo", display',
    'weights' => array(
      '400'
    )
  ),
  'kadwa' => array(
    'source' => 'google',
    'family' => 'Kadwa',
    'stack' => '"Kadwa", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'kaiseidecol' => array(
    'source' => 'google',
    'family' => 'Kaisei Decol',
    'stack' => '"Kaisei Decol", serif',
    'weights' => array(
      '400',
      '500',
      '700'
    )
  ),
  'kaiseiharunoumi' => array(
    'source' => 'google',
    'family' => 'Kaisei HarunoUmi',
    'stack' => '"Kaisei HarunoUmi", serif',
    'weights' => array(
      '400',
      '500',
      '700'
    )
  ),
  'kaiseiopti' => array(
    'source' => 'google',
    'family' => 'Kaisei Opti',
    'stack' => '"Kaisei Opti", serif',
    'weights' => array(
      '400',
      '500',
      '700'
    )
  ),
  'kaiseitokumin' => array(
    'source' => 'google',
    'family' => 'Kaisei Tokumin',
    'stack' => '"Kaisei Tokumin", serif',
    'weights' => array(
      '400',
      '500',
      '700',
      '800'
    )
  ),
  'kalam' => array(
    'source' => 'google',
    'family' => 'Kalam',
    'stack' => '"Kalam", handwriting',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'kalnia' => array(
    'source' => 'google',
    'family' => 'Kalnia',
    'stack' => '"Kalnia", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'kalniaglaze' => array(
    'source' => 'google',
    'family' => 'Kalnia Glaze',
    'stack' => '"Kalnia Glaze", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'kameron' => array(
    'source' => 'google',
    'family' => 'Kameron',
    'stack' => '"Kameron", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'kanchenjunga' => array(
    'source' => 'google',
    'family' => 'Kanchenjunga',
    'stack' => '"Kanchenjunga", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'kanit' => array(
    'source' => 'google',
    'family' => 'Kanit',
    'stack' => '"Kanit", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'kantumruypro' => array(
    'source' => 'google',
    'family' => 'Kantumruy Pro',
    'stack' => '"Kantumruy Pro", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'kapakana' => array(
    'source' => 'google',
    'family' => 'Kapakana',
    'stack' => '"Kapakana", handwriting',
    'weights' => array(
      '300',
      '400'
    )
  ),
  'karantina' => array(
    'source' => 'google',
    'family' => 'Karantina',
    'stack' => '"Karantina", display',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'karla' => array(
    'source' => 'google',
    'family' => 'Karla',
    'stack' => '"Karla", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'karlatamilinclined' => array(
    'source' => 'google',
    'family' => 'Karla Tamil Inclined',
    'stack' => '"Karla Tamil Inclined", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'karlatamilupright' => array(
    'source' => 'google',
    'family' => 'Karla Tamil Upright',
    'stack' => '"Karla Tamil Upright", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'karma' => array(
    'source' => 'google',
    'family' => 'Karma',
    'stack' => '"Karma", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'katibeh' => array(
    'source' => 'google',
    'family' => 'Katibeh',
    'stack' => '"Katibeh", display',
    'weights' => array(
      '400'
    )
  ),
  'kaushanscript' => array(
    'source' => 'google',
    'family' => 'Kaushan Script',
    'stack' => '"Kaushan Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'kavivanar' => array(
    'source' => 'google',
    'family' => 'Kavivanar',
    'stack' => '"Kavivanar", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'kavoon' => array(
    'source' => 'google',
    'family' => 'Kavoon',
    'stack' => '"Kavoon", display',
    'weights' => array(
      '400'
    )
  ),
  'kayphodu' => array(
    'source' => 'google',
    'family' => 'Kay Pho Du',
    'stack' => '"Kay Pho Du", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'kdamthmorpro' => array(
    'source' => 'google',
    'family' => 'Kdam Thmor Pro',
    'stack' => '"Kdam Thmor Pro", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'keaniaone' => array(
    'source' => 'google',
    'family' => 'Keania One',
    'stack' => '"Keania One", display',
    'weights' => array(
      '400'
    )
  ),
  'kellyslab' => array(
    'source' => 'google',
    'family' => 'Kelly Slab',
    'stack' => '"Kelly Slab", display',
    'weights' => array(
      '400'
    )
  ),
  'kenia' => array(
    'source' => 'google',
    'family' => 'Kenia',
    'stack' => '"Kenia", display',
    'weights' => array(
      '400'
    )
  ),
  'khand' => array(
    'source' => 'google',
    'family' => 'Khand',
    'stack' => '"Khand", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'khmer' => array(
    'source' => 'google',
    'family' => 'Khmer',
    'stack' => '"Khmer", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'khula' => array(
    'source' => 'google',
    'family' => 'Khula',
    'stack' => '"Khula", sans-serif',
    'weights' => array(
      '300',
      '400',
      '600',
      '700',
      '800'
    )
  ),
  'kings' => array(
    'source' => 'google',
    'family' => 'Kings',
    'stack' => '"Kings", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'kiranghaerang' => array(
    'source' => 'google',
    'family' => 'Kirang Haerang',
    'stack' => '"Kirang Haerang", display',
    'weights' => array(
      '400'
    )
  ),
  'kiteone' => array(
    'source' => 'google',
    'family' => 'Kite One',
    'stack' => '"Kite One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'kiwimaru' => array(
    'source' => 'google',
    'family' => 'Kiwi Maru',
    'stack' => '"Kiwi Maru", serif',
    'weights' => array(
      '300',
      '400',
      '500'
    )
  ),
  'kleeone' => array(
    'source' => 'google',
    'family' => 'Klee One',
    'stack' => '"Klee One", handwriting',
    'weights' => array(
      '400',
      '600'
    )
  ),
  'knewave' => array(
    'source' => 'google',
    'family' => 'Knewave',
    'stack' => '"Knewave", display',
    'weights' => array(
      '400'
    )
  ),
  'koho' => array(
    'source' => 'google',
    'family' => 'KoHo',
    'stack' => '"KoHo", sans-serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'kodchasan' => array(
    'source' => 'google',
    'family' => 'Kodchasan',
    'stack' => '"Kodchasan", sans-serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'kodemono' => array(
    'source' => 'google',
    'family' => 'Kode Mono',
    'stack' => '"Kode Mono", monospace',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'kohsantepheap' => array(
    'source' => 'google',
    'family' => 'Koh Santepheap',
    'stack' => '"Koh Santepheap", serif',
    'weights' => array(
      '100',
      '300',
      '400',
      '700',
      '900'
    )
  ),
  'kolkerbrush' => array(
    'source' => 'google',
    'family' => 'Kolker Brush',
    'stack' => '"Kolker Brush", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'konkhmersleokchher' => array(
    'source' => 'google',
    'family' => 'Konkhmer Sleokchher',
    'stack' => '"Konkhmer Sleokchher", display',
    'weights' => array(
      '400'
    )
  ),
  'kosugi' => array(
    'source' => 'google',
    'family' => 'Kosugi',
    'stack' => '"Kosugi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'kosugimaru' => array(
    'source' => 'google',
    'family' => 'Kosugi Maru',
    'stack' => '"Kosugi Maru", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'kottaone' => array(
    'source' => 'google',
    'family' => 'Kotta One',
    'stack' => '"Kotta One", serif',
    'weights' => array(
      '400'
    )
  ),
  'koulen' => array(
    'source' => 'google',
    'family' => 'Koulen',
    'stack' => '"Koulen", display',
    'weights' => array(
      '400'
    )
  ),
  'kranky' => array(
    'source' => 'google',
    'family' => 'Kranky',
    'stack' => '"Kranky", display',
    'weights' => array(
      '400'
    )
  ),
  'kreon' => array(
    'source' => 'google',
    'family' => 'Kreon',
    'stack' => '"Kreon", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'kristi' => array(
    'source' => 'google',
    'family' => 'Kristi',
    'stack' => '"Kristi", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'kronaone' => array(
    'source' => 'google',
    'family' => 'Krona One',
    'stack' => '"Krona One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'krub' => array(
    'source' => 'google',
    'family' => 'Krub',
    'stack' => '"Krub", sans-serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'kufam' => array(
    'source' => 'google',
    'family' => 'Kufam',
    'stack' => '"Kufam", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'kulimpark' => array(
    'source' => 'google',
    'family' => 'Kulim Park',
    'stack' => '"Kulim Park", sans-serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'kumarone' => array(
    'source' => 'google',
    'family' => 'Kumar One',
    'stack' => '"Kumar One", display',
    'weights' => array(
      '400'
    )
  ),
  'kumaroneoutline' => array(
    'source' => 'google',
    'family' => 'Kumar One Outline',
    'stack' => '"Kumar One Outline", display',
    'weights' => array(
      '400'
    )
  ),
  'kumbhsans' => array(
    'source' => 'google',
    'family' => 'Kumbh Sans',
    'stack' => '"Kumbh Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'kurale' => array(
    'source' => 'google',
    'family' => 'Kurale',
    'stack' => '"Kurale", serif',
    'weights' => array(
      '400'
    )
  ),
  'lxgwmarkergothic' => array(
    'source' => 'google',
    'family' => 'LXGW Marker Gothic',
    'stack' => '"LXGW Marker Gothic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'lxgwwenkaimonotc' => array(
    'source' => 'google',
    'family' => 'LXGW WenKai Mono TC',
    'stack' => '"LXGW WenKai Mono TC", monospace',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'lxgwwenkaitc' => array(
    'source' => 'google',
    'family' => 'LXGW WenKai TC',
    'stack' => '"LXGW WenKai TC", handwriting',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'labelleaurore' => array(
    'source' => 'google',
    'family' => 'La Belle Aurore',
    'stack' => '"La Belle Aurore", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'labrada' => array(
    'source' => 'google',
    'family' => 'Labrada',
    'stack' => '"Labrada", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'lacquer' => array(
    'source' => 'google',
    'family' => 'Lacquer',
    'stack' => '"Lacquer", display',
    'weights' => array(
      '400'
    )
  ),
  'laila' => array(
    'source' => 'google',
    'family' => 'Laila',
    'stack' => '"Laila", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'lakkireddy' => array(
    'source' => 'google',
    'family' => 'Lakki Reddy',
    'stack' => '"Lakki Reddy", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'lalezar' => array(
    'source' => 'google',
    'family' => 'Lalezar',
    'stack' => '"Lalezar", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'lancelot' => array(
    'source' => 'google',
    'family' => 'Lancelot',
    'stack' => '"Lancelot", display',
    'weights' => array(
      '400'
    )
  ),
  'langar' => array(
    'source' => 'google',
    'family' => 'Langar',
    'stack' => '"Langar", display',
    'weights' => array(
      '400'
    )
  ),
  'lateef' => array(
    'source' => 'google',
    'family' => 'Lateef',
    'stack' => '"Lateef", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'lato' => array(
    'source' => 'google',
    'family' => 'Lato',
    'stack' => '"Lato", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i',
      '900',
      '900i'
    )
  ),
  'lavishlyyours' => array(
    'source' => 'google',
    'family' => 'Lavishly Yours',
    'stack' => '"Lavishly Yours", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'leaguegothic' => array(
    'source' => 'google',
    'family' => 'League Gothic',
    'stack' => '"League Gothic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'leaguescript' => array(
    'source' => 'google',
    'family' => 'League Script',
    'stack' => '"League Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'leaguespartan' => array(
    'source' => 'google',
    'family' => 'League Spartan',
    'stack' => '"League Spartan", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'leckerlione' => array(
    'source' => 'google',
    'family' => 'Leckerli One',
    'stack' => '"Leckerli One", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ledger' => array(
    'source' => 'google',
    'family' => 'Ledger',
    'stack' => '"Ledger", serif',
    'weights' => array(
      '400'
    )
  ),
  'lekton' => array(
    'source' => 'google',
    'family' => 'Lekton',
    'stack' => '"Lekton", monospace',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'lemon' => array(
    'source' => 'google',
    'family' => 'Lemon',
    'stack' => '"Lemon", display',
    'weights' => array(
      '400'
    )
  ),
  'lemonada' => array(
    'source' => 'google',
    'family' => 'Lemonada',
    'stack' => '"Lemonada", display',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'lexend' => array(
    'source' => 'google',
    'family' => 'Lexend',
    'stack' => '"Lexend", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'lexenddeca' => array(
    'source' => 'google',
    'family' => 'Lexend Deca',
    'stack' => '"Lexend Deca", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'lexendexa' => array(
    'source' => 'google',
    'family' => 'Lexend Exa',
    'stack' => '"Lexend Exa", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'lexendgiga' => array(
    'source' => 'google',
    'family' => 'Lexend Giga',
    'stack' => '"Lexend Giga", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'lexendmega' => array(
    'source' => 'google',
    'family' => 'Lexend Mega',
    'stack' => '"Lexend Mega", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'lexendpeta' => array(
    'source' => 'google',
    'family' => 'Lexend Peta',
    'stack' => '"Lexend Peta", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'lexendtera' => array(
    'source' => 'google',
    'family' => 'Lexend Tera',
    'stack' => '"Lexend Tera", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'lexendzetta' => array(
    'source' => 'google',
    'family' => 'Lexend Zetta',
    'stack' => '"Lexend Zetta", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'libertinusmath' => array(
    'source' => 'google',
    'family' => 'Libertinus Math',
    'stack' => '"Libertinus Math", display',
    'weights' => array(
      '400'
    )
  ),
  'libertinusmono' => array(
    'source' => 'google',
    'family' => 'Libertinus Mono',
    'stack' => '"Libertinus Mono", monospace',
    'weights' => array(
      '400'
    )
  ),
  'librebarcode128' => array(
    'source' => 'google',
    'family' => 'Libre Barcode 128',
    'stack' => '"Libre Barcode 128", display',
    'weights' => array(
      '400'
    )
  ),
  'librebarcode128text' => array(
    'source' => 'google',
    'family' => 'Libre Barcode 128 Text',
    'stack' => '"Libre Barcode 128 Text", display',
    'weights' => array(
      '400'
    )
  ),
  'librebarcode39' => array(
    'source' => 'google',
    'family' => 'Libre Barcode 39',
    'stack' => '"Libre Barcode 39", display',
    'weights' => array(
      '400'
    )
  ),
  'librebarcode39extended' => array(
    'source' => 'google',
    'family' => 'Libre Barcode 39 Extended',
    'stack' => '"Libre Barcode 39 Extended", display',
    'weights' => array(
      '400'
    )
  ),
  'librebarcode39extendedtext' => array(
    'source' => 'google',
    'family' => 'Libre Barcode 39 Extended Text',
    'stack' => '"Libre Barcode 39 Extended Text", display',
    'weights' => array(
      '400'
    )
  ),
  'librebarcode39text' => array(
    'source' => 'google',
    'family' => 'Libre Barcode 39 Text',
    'stack' => '"Libre Barcode 39 Text", display',
    'weights' => array(
      '400'
    )
  ),
  'librebarcodeean13text' => array(
    'source' => 'google',
    'family' => 'Libre Barcode EAN13 Text',
    'stack' => '"Libre Barcode EAN13 Text", display',
    'weights' => array(
      '400'
    )
  ),
  'librebaskerville' => array(
    'source' => 'google',
    'family' => 'Libre Baskerville',
    'stack' => '"Libre Baskerville", serif',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'librebodoni' => array(
    'source' => 'google',
    'family' => 'Libre Bodoni',
    'stack' => '"Libre Bodoni", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'librecaslondisplay' => array(
    'source' => 'google',
    'family' => 'Libre Caslon Display',
    'stack' => '"Libre Caslon Display", serif',
    'weights' => array(
      '400'
    )
  ),
  'librecaslontext' => array(
    'source' => 'google',
    'family' => 'Libre Caslon Text',
    'stack' => '"Libre Caslon Text", serif',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'librefranklin' => array(
    'source' => 'google',
    'family' => 'Libre Franklin',
    'stack' => '"Libre Franklin", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'licorice' => array(
    'source' => 'google',
    'family' => 'Licorice',
    'stack' => '"Licorice", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'lifesavers' => array(
    'source' => 'google',
    'family' => 'Life Savers',
    'stack' => '"Life Savers", display',
    'weights' => array(
      '400',
      '700',
      '800'
    )
  ),
  'lilitaone' => array(
    'source' => 'google',
    'family' => 'Lilita One',
    'stack' => '"Lilita One", display',
    'weights' => array(
      '400'
    )
  ),
  'lilyscriptone' => array(
    'source' => 'google',
    'family' => 'Lily Script One',
    'stack' => '"Lily Script One", display',
    'weights' => array(
      '400'
    )
  ),
  'limelight' => array(
    'source' => 'google',
    'family' => 'Limelight',
    'stack' => '"Limelight", display',
    'weights' => array(
      '400'
    )
  ),
  'lindenhill' => array(
    'source' => 'google',
    'family' => 'Linden Hill',
    'stack' => '"Linden Hill", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'linefont' => array(
    'source' => 'google',
    'family' => 'Linefont',
    'stack' => '"Linefont", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'lisubosa' => array(
    'source' => 'google',
    'family' => 'Lisu Bosa',
    'stack' => '"Lisu Bosa", serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'liter' => array(
    'source' => 'google',
    'family' => 'Liter',
    'stack' => '"Liter", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'literata' => array(
    'source' => 'google',
    'family' => 'Literata',
    'stack' => '"Literata", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'liujianmaocao' => array(
    'source' => 'google',
    'family' => 'Liu Jian Mao Cao',
    'stack' => '"Liu Jian Mao Cao", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'livvic' => array(
    'source' => 'google',
    'family' => 'Livvic',
    'stack' => '"Livvic", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '900',
      '900i'
    )
  ),
  'lobster' => array(
    'source' => 'google',
    'family' => 'Lobster',
    'stack' => '"Lobster", display',
    'weights' => array(
      '400'
    )
  ),
  'lobstertwo' => array(
    'source' => 'google',
    'family' => 'Lobster Two',
    'stack' => '"Lobster Two", display',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'londrinaoutline' => array(
    'source' => 'google',
    'family' => 'Londrina Outline',
    'stack' => '"Londrina Outline", display',
    'weights' => array(
      '400'
    )
  ),
  'londrinashadow' => array(
    'source' => 'google',
    'family' => 'Londrina Shadow',
    'stack' => '"Londrina Shadow", display',
    'weights' => array(
      '400'
    )
  ),
  'londrinasketch' => array(
    'source' => 'google',
    'family' => 'Londrina Sketch',
    'stack' => '"Londrina Sketch", display',
    'weights' => array(
      '400'
    )
  ),
  'londrinasolid' => array(
    'source' => 'google',
    'family' => 'Londrina Solid',
    'stack' => '"Londrina Solid", display',
    'weights' => array(
      '100',
      '300',
      '400',
      '900'
    )
  ),
  'longcang' => array(
    'source' => 'google',
    'family' => 'Long Cang',
    'stack' => '"Long Cang", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'lora' => array(
    'source' => 'google',
    'family' => 'Lora',
    'stack' => '"Lora", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'lovelight' => array(
    'source' => 'google',
    'family' => 'Love Light',
    'stack' => '"Love Light", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'loveyalikeasister' => array(
    'source' => 'google',
    'family' => 'Love Ya Like A Sister',
    'stack' => '"Love Ya Like A Sister", display',
    'weights' => array(
      '400'
    )
  ),
  'lovedbytheking' => array(
    'source' => 'google',
    'family' => 'Loved by the King',
    'stack' => '"Loved by the King", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'loversquarrel' => array(
    'source' => 'google',
    'family' => 'Lovers Quarrel',
    'stack' => '"Lovers Quarrel", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'luckiestguy' => array(
    'source' => 'google',
    'family' => 'Luckiest Guy',
    'stack' => '"Luckiest Guy", display',
    'weights' => array(
      '400'
    )
  ),
  'lugrasimo' => array(
    'source' => 'google',
    'family' => 'Lugrasimo',
    'stack' => '"Lugrasimo", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'lumanosimo' => array(
    'source' => 'google',
    'family' => 'Lumanosimo',
    'stack' => '"Lumanosimo", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'lunasima' => array(
    'source' => 'google',
    'family' => 'Lunasima',
    'stack' => '"Lunasima", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'lusitana' => array(
    'source' => 'google',
    'family' => 'Lusitana',
    'stack' => '"Lusitana", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'lustria' => array(
    'source' => 'google',
    'family' => 'Lustria',
    'stack' => '"Lustria", serif',
    'weights' => array(
      '400'
    )
  ),
  'luxuriousroman' => array(
    'source' => 'google',
    'family' => 'Luxurious Roman',
    'stack' => '"Luxurious Roman", display',
    'weights' => array(
      '400'
    )
  ),
  'luxuriousscript' => array(
    'source' => 'google',
    'family' => 'Luxurious Script',
    'stack' => '"Luxurious Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'mplus1' => array(
    'source' => 'google',
    'family' => 'M PLUS 1',
    'stack' => '"M PLUS 1", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'mplus1code' => array(
    'source' => 'google',
    'family' => 'M PLUS 1 Code',
    'stack' => '"M PLUS 1 Code", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'mplus1p' => array(
    'source' => 'google',
    'family' => 'M PLUS 1p',
    'stack' => '"M PLUS 1p", sans-serif',
    'weights' => array(
      '100',
      '300',
      '400',
      '500',
      '700',
      '800',
      '900'
    )
  ),
  'mplus2' => array(
    'source' => 'google',
    'family' => 'M PLUS 2',
    'stack' => '"M PLUS 2", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'mpluscodelatin' => array(
    'source' => 'google',
    'family' => 'M PLUS Code Latin',
    'stack' => '"M PLUS Code Latin", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'mplusrounded1c' => array(
    'source' => 'google',
    'family' => 'M PLUS Rounded 1c',
    'stack' => '"M PLUS Rounded 1c", sans-serif',
    'weights' => array(
      '100',
      '300',
      '400',
      '500',
      '700',
      '800',
      '900'
    )
  ),
  'mashanzheng' => array(
    'source' => 'google',
    'family' => 'Ma Shan Zheng',
    'stack' => '"Ma Shan Zheng", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'macondo' => array(
    'source' => 'google',
    'family' => 'Macondo',
    'stack' => '"Macondo", display',
    'weights' => array(
      '400'
    )
  ),
  'macondoswashcaps' => array(
    'source' => 'google',
    'family' => 'Macondo Swash Caps',
    'stack' => '"Macondo Swash Caps", display',
    'weights' => array(
      '400'
    )
  ),
  'mada' => array(
    'source' => 'google',
    'family' => 'Mada',
    'stack' => '"Mada", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'madimione' => array(
    'source' => 'google',
    'family' => 'Madimi One',
    'stack' => '"Madimi One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'magra' => array(
    'source' => 'google',
    'family' => 'Magra',
    'stack' => '"Magra", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'maidenorange' => array(
    'source' => 'google',
    'family' => 'Maiden Orange',
    'stack' => '"Maiden Orange", serif',
    'weights' => array(
      '400'
    )
  ),
  'maitree' => array(
    'source' => 'google',
    'family' => 'Maitree',
    'stack' => '"Maitree", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'majormonodisplay' => array(
    'source' => 'google',
    'family' => 'Major Mono Display',
    'stack' => '"Major Mono Display", monospace',
    'weights' => array(
      '400'
    )
  ),
  'mako' => array(
    'source' => 'google',
    'family' => 'Mako',
    'stack' => '"Mako", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'mali' => array(
    'source' => 'google',
    'family' => 'Mali',
    'stack' => '"Mali", handwriting',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'mallanna' => array(
    'source' => 'google',
    'family' => 'Mallanna',
    'stack' => '"Mallanna", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'maname' => array(
    'source' => 'google',
    'family' => 'Maname',
    'stack' => '"Maname", serif',
    'weights' => array(
      '400'
    )
  ),
  'mandali' => array(
    'source' => 'google',
    'family' => 'Mandali',
    'stack' => '"Mandali", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'manjari' => array(
    'source' => 'google',
    'family' => 'Manjari',
    'stack' => '"Manjari", sans-serif',
    'weights' => array(
      '100',
      '400',
      '700'
    )
  ),
  'manrope' => array(
    'source' => 'google',
    'family' => 'Manrope',
    'stack' => '"Manrope", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'mansalva' => array(
    'source' => 'google',
    'family' => 'Mansalva',
    'stack' => '"Mansalva", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'manuale' => array(
    'source' => 'google',
    'family' => 'Manuale',
    'stack' => '"Manuale", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'manufacturingconsent' => array(
    'source' => 'google',
    'family' => 'Manufacturing Consent',
    'stack' => '"Manufacturing Consent", display',
    'weights' => array(
      '400'
    )
  ),
  'marcellus' => array(
    'source' => 'google',
    'family' => 'Marcellus',
    'stack' => '"Marcellus", serif',
    'weights' => array(
      '400'
    )
  ),
  'marcellussc' => array(
    'source' => 'google',
    'family' => 'Marcellus SC',
    'stack' => '"Marcellus SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'marckscript' => array(
    'source' => 'google',
    'family' => 'Marck Script',
    'stack' => '"Marck Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'margarine' => array(
    'source' => 'google',
    'family' => 'Margarine',
    'stack' => '"Margarine", display',
    'weights' => array(
      '400'
    )
  ),
  'marhey' => array(
    'source' => 'google',
    'family' => 'Marhey',
    'stack' => '"Marhey", display',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'markazitext' => array(
    'source' => 'google',
    'family' => 'Markazi Text',
    'stack' => '"Markazi Text", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'markoone' => array(
    'source' => 'google',
    'family' => 'Marko One',
    'stack' => '"Marko One", serif',
    'weights' => array(
      '400'
    )
  ),
  'marmelad' => array(
    'source' => 'google',
    'family' => 'Marmelad',
    'stack' => '"Marmelad", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'martel' => array(
    'source' => 'google',
    'family' => 'Martel',
    'stack' => '"Martel", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'martelsans' => array(
    'source' => 'google',
    'family' => 'Martel Sans',
    'stack' => '"Martel Sans", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'martianmono' => array(
    'source' => 'google',
    'family' => 'Martian Mono',
    'stack' => '"Martian Mono", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'marvel' => array(
    'source' => 'google',
    'family' => 'Marvel',
    'stack' => '"Marvel", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'matangi' => array(
    'source' => 'google',
    'family' => 'Matangi',
    'stack' => '"Matangi", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'mate' => array(
    'source' => 'google',
    'family' => 'Mate',
    'stack' => '"Mate", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'matesc' => array(
    'source' => 'google',
    'family' => 'Mate SC',
    'stack' => '"Mate SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'matemasie' => array(
    'source' => 'google',
    'family' => 'Matemasie',
    'stack' => '"Matemasie", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'materialicons' => array(
    'source' => 'google',
    'family' => 'Material Icons',
    'stack' => '"Material Icons", monospace',
    'weights' => array(
      '400'
    )
  ),
  'materialiconsoutlined' => array(
    'source' => 'google',
    'family' => 'Material Icons Outlined',
    'stack' => '"Material Icons Outlined", monospace',
    'weights' => array(
      '400'
    )
  ),
  'materialiconsround' => array(
    'source' => 'google',
    'family' => 'Material Icons Round',
    'stack' => '"Material Icons Round", monospace',
    'weights' => array(
      '400'
    )
  ),
  'materialiconssharp' => array(
    'source' => 'google',
    'family' => 'Material Icons Sharp',
    'stack' => '"Material Icons Sharp", monospace',
    'weights' => array(
      '400'
    )
  ),
  'materialiconstwotone' => array(
    'source' => 'google',
    'family' => 'Material Icons Two Tone',
    'stack' => '"Material Icons Two Tone", monospace',
    'weights' => array(
      '400'
    )
  ),
  'materialsymbols' => array(
    'source' => 'google',
    'family' => 'Material Symbols',
    'stack' => '"Material Symbols", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'materialsymbolsoutlined' => array(
    'source' => 'google',
    'family' => 'Material Symbols Outlined',
    'stack' => '"Material Symbols Outlined", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'materialsymbolsrounded' => array(
    'source' => 'google',
    'family' => 'Material Symbols Rounded',
    'stack' => '"Material Symbols Rounded", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'materialsymbolssharp' => array(
    'source' => 'google',
    'family' => 'Material Symbols Sharp',
    'stack' => '"Material Symbols Sharp", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'mavenpro' => array(
    'source' => 'google',
    'family' => 'Maven Pro',
    'stack' => '"Maven Pro", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'mclaren' => array(
    'source' => 'google',
    'family' => 'McLaren',
    'stack' => '"McLaren", display',
    'weights' => array(
      '400'
    )
  ),
  'meaculpa' => array(
    'source' => 'google',
    'family' => 'Mea Culpa',
    'stack' => '"Mea Culpa", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'meddon' => array(
    'source' => 'google',
    'family' => 'Meddon',
    'stack' => '"Meddon", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'medievalsharp' => array(
    'source' => 'google',
    'family' => 'MedievalSharp',
    'stack' => '"MedievalSharp", display',
    'weights' => array(
      '400'
    )
  ),
  'medulaone' => array(
    'source' => 'google',
    'family' => 'Medula One',
    'stack' => '"Medula One", display',
    'weights' => array(
      '400'
    )
  ),
  'meerainimai' => array(
    'source' => 'google',
    'family' => 'Meera Inimai',
    'stack' => '"Meera Inimai", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'megrim' => array(
    'source' => 'google',
    'family' => 'Megrim',
    'stack' => '"Megrim", display',
    'weights' => array(
      '400'
    )
  ),
  'meiescript' => array(
    'source' => 'google',
    'family' => 'Meie Script',
    'stack' => '"Meie Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'menbere' => array(
    'source' => 'google',
    'family' => 'Menbere',
    'stack' => '"Menbere", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'meowscript' => array(
    'source' => 'google',
    'family' => 'Meow Script',
    'stack' => '"Meow Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'merienda' => array(
    'source' => 'google',
    'family' => 'Merienda',
    'stack' => '"Merienda", handwriting',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'merriweather' => array(
    'source' => 'google',
    'family' => 'Merriweather',
    'stack' => '"Merriweather", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'merriweathersans' => array(
    'source' => 'google',
    'family' => 'Merriweather Sans',
    'stack' => '"Merriweather Sans", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'metal' => array(
    'source' => 'google',
    'family' => 'Metal',
    'stack' => '"Metal", display',
    'weights' => array(
      '400'
    )
  ),
  'metalmania' => array(
    'source' => 'google',
    'family' => 'Metal Mania',
    'stack' => '"Metal Mania", display',
    'weights' => array(
      '400'
    )
  ),
  'metamorphous' => array(
    'source' => 'google',
    'family' => 'Metamorphous',
    'stack' => '"Metamorphous", display',
    'weights' => array(
      '400'
    )
  ),
  'metrophobic' => array(
    'source' => 'google',
    'family' => 'Metrophobic',
    'stack' => '"Metrophobic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'michroma' => array(
    'source' => 'google',
    'family' => 'Michroma',
    'stack' => '"Michroma", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'micro5' => array(
    'source' => 'google',
    'family' => 'Micro 5',
    'stack' => '"Micro 5", display',
    'weights' => array(
      '400'
    )
  ),
  'micro5charted' => array(
    'source' => 'google',
    'family' => 'Micro 5 Charted',
    'stack' => '"Micro 5 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'milonga' => array(
    'source' => 'google',
    'family' => 'Milonga',
    'stack' => '"Milonga", display',
    'weights' => array(
      '400'
    )
  ),
  'miltonian' => array(
    'source' => 'google',
    'family' => 'Miltonian',
    'stack' => '"Miltonian", display',
    'weights' => array(
      '400'
    )
  ),
  'miltoniantattoo' => array(
    'source' => 'google',
    'family' => 'Miltonian Tattoo',
    'stack' => '"Miltonian Tattoo", display',
    'weights' => array(
      '400'
    )
  ),
  'mina' => array(
    'source' => 'google',
    'family' => 'Mina',
    'stack' => '"Mina", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'mingzat' => array(
    'source' => 'google',
    'family' => 'Mingzat',
    'stack' => '"Mingzat", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'miniver' => array(
    'source' => 'google',
    'family' => 'Miniver',
    'stack' => '"Miniver", display',
    'weights' => array(
      '400'
    )
  ),
  'miriamlibre' => array(
    'source' => 'google',
    'family' => 'Miriam Libre',
    'stack' => '"Miriam Libre", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'mirza' => array(
    'source' => 'google',
    'family' => 'Mirza',
    'stack' => '"Mirza", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'missfajardose' => array(
    'source' => 'google',
    'family' => 'Miss Fajardose',
    'stack' => '"Miss Fajardose", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'mitr' => array(
    'source' => 'google',
    'family' => 'Mitr',
    'stack' => '"Mitr", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'mochiypopone' => array(
    'source' => 'google',
    'family' => 'Mochiy Pop One',
    'stack' => '"Mochiy Pop One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'mochiypoppone' => array(
    'source' => 'google',
    'family' => 'Mochiy Pop P One',
    'stack' => '"Mochiy Pop P One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'modak' => array(
    'source' => 'google',
    'family' => 'Modak',
    'stack' => '"Modak", display',
    'weights' => array(
      '400'
    )
  ),
  'modernantiqua' => array(
    'source' => 'google',
    'family' => 'Modern Antiqua',
    'stack' => '"Modern Antiqua", display',
    'weights' => array(
      '400'
    )
  ),
  'moderustic' => array(
    'source' => 'google',
    'family' => 'Moderustic',
    'stack' => '"Moderustic", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'mogra' => array(
    'source' => 'google',
    'family' => 'Mogra',
    'stack' => '"Mogra", display',
    'weights' => array(
      '400'
    )
  ),
  'mohave' => array(
    'source' => 'google',
    'family' => 'Mohave',
    'stack' => '"Mohave", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'moiraione' => array(
    'source' => 'google',
    'family' => 'Moirai One',
    'stack' => '"Moirai One", display',
    'weights' => array(
      '400'
    )
  ),
  'molengo' => array(
    'source' => 'google',
    'family' => 'Molengo',
    'stack' => '"Molengo", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'molle' => array(
    'source' => 'google',
    'family' => 'Molle',
    'stack' => '"Molle", handwriting',
    'weights' => array(
      '400i'
    )
  ),
  'monasans' => array(
    'source' => 'google',
    'family' => 'Mona Sans',
    'stack' => '"Mona Sans", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'monda' => array(
    'source' => 'google',
    'family' => 'Monda',
    'stack' => '"Monda", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'monofett' => array(
    'source' => 'google',
    'family' => 'Monofett',
    'stack' => '"Monofett", monospace',
    'weights' => array(
      '400'
    )
  ),
  'monomakh' => array(
    'source' => 'google',
    'family' => 'Monomakh',
    'stack' => '"Monomakh", display',
    'weights' => array(
      '400'
    )
  ),
  'monomaniacone' => array(
    'source' => 'google',
    'family' => 'Monomaniac One',
    'stack' => '"Monomaniac One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'monoton' => array(
    'source' => 'google',
    'family' => 'Monoton',
    'stack' => '"Monoton", display',
    'weights' => array(
      '400'
    )
  ),
  'monsieurladoulaise' => array(
    'source' => 'google',
    'family' => 'Monsieur La Doulaise',
    'stack' => '"Monsieur La Doulaise", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'montaga' => array(
    'source' => 'google',
    'family' => 'Montaga',
    'stack' => '"Montaga", serif',
    'weights' => array(
      '400'
    )
  ),
  'montaguslab' => array(
    'source' => 'google',
    'family' => 'Montagu Slab',
    'stack' => '"Montagu Slab", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'montecarlo' => array(
    'source' => 'google',
    'family' => 'MonteCarlo',
    'stack' => '"MonteCarlo", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'montez' => array(
    'source' => 'google',
    'family' => 'Montez',
    'stack' => '"Montez", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'montserrat' => array(
    'source' => 'google',
    'family' => 'Montserrat',
    'stack' => '"Montserrat", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'montserratalternates' => array(
    'source' => 'google',
    'family' => 'Montserrat Alternates',
    'stack' => '"Montserrat Alternates", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'montserratunderline' => array(
    'source' => 'google',
    'family' => 'Montserrat Underline',
    'stack' => '"Montserrat Underline", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'moolahlah' => array(
    'source' => 'google',
    'family' => 'Moo Lah Lah',
    'stack' => '"Moo Lah Lah", display',
    'weights' => array(
      '400'
    )
  ),
  'mooli' => array(
    'source' => 'google',
    'family' => 'Mooli',
    'stack' => '"Mooli", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'moondance' => array(
    'source' => 'google',
    'family' => 'Moon Dance',
    'stack' => '"Moon Dance", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'moul' => array(
    'source' => 'google',
    'family' => 'Moul',
    'stack' => '"Moul", display',
    'weights' => array(
      '400'
    )
  ),
  'moulpali' => array(
    'source' => 'google',
    'family' => 'Moulpali',
    'stack' => '"Moulpali", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'mountainsofchristmas' => array(
    'source' => 'google',
    'family' => 'Mountains of Christmas',
    'stack' => '"Mountains of Christmas", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'mousememoirs' => array(
    'source' => 'google',
    'family' => 'Mouse Memoirs',
    'stack' => '"Mouse Memoirs", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'mrbedfort' => array(
    'source' => 'google',
    'family' => 'Mr Bedfort',
    'stack' => '"Mr Bedfort", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'mrdafoe' => array(
    'source' => 'google',
    'family' => 'Mr Dafoe',
    'stack' => '"Mr Dafoe", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'mrdehaviland' => array(
    'source' => 'google',
    'family' => 'Mr De Haviland',
    'stack' => '"Mr De Haviland", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'mrssaintdelafield' => array(
    'source' => 'google',
    'family' => 'Mrs Saint Delafield',
    'stack' => '"Mrs Saint Delafield", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'mrssheppards' => array(
    'source' => 'google',
    'family' => 'Mrs Sheppards',
    'stack' => '"Mrs Sheppards", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'msmadi' => array(
    'source' => 'google',
    'family' => 'Ms Madi',
    'stack' => '"Ms Madi", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'mukta' => array(
    'source' => 'google',
    'family' => 'Mukta',
    'stack' => '"Mukta", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'muktamahee' => array(
    'source' => 'google',
    'family' => 'Mukta Mahee',
    'stack' => '"Mukta Mahee", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'muktamalar' => array(
    'source' => 'google',
    'family' => 'Mukta Malar',
    'stack' => '"Mukta Malar", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'muktavaani' => array(
    'source' => 'google',
    'family' => 'Mukta Vaani',
    'stack' => '"Mukta Vaani", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'mulish' => array(
    'source' => 'google',
    'family' => 'Mulish',
    'stack' => '"Mulish", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'murecho' => array(
    'source' => 'google',
    'family' => 'Murecho',
    'stack' => '"Murecho", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'museomoderno' => array(
    'source' => 'google',
    'family' => 'MuseoModerno',
    'stack' => '"MuseoModerno", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'mysoul' => array(
    'source' => 'google',
    'family' => 'My Soul',
    'stack' => '"My Soul", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'mynerve' => array(
    'source' => 'google',
    'family' => 'Mynerve',
    'stack' => '"Mynerve", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'mysteryquest' => array(
    'source' => 'google',
    'family' => 'Mystery Quest',
    'stack' => '"Mystery Quest", display',
    'weights' => array(
      '400'
    )
  ),
  'ntr' => array(
    'source' => 'google',
    'family' => 'NTR',
    'stack' => '"NTR", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'nabla' => array(
    'source' => 'google',
    'family' => 'Nabla',
    'stack' => '"Nabla", display',
    'weights' => array(
      '400'
    )
  ),
  'namdhinggo' => array(
    'source' => 'google',
    'family' => 'Namdhinggo',
    'stack' => '"Namdhinggo", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'nanumbrushscript' => array(
    'source' => 'google',
    'family' => 'Nanum Brush Script',
    'stack' => '"Nanum Brush Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'nanumgothic' => array(
    'source' => 'google',
    'family' => 'Nanum Gothic',
    'stack' => '"Nanum Gothic", sans-serif',
    'weights' => array(
      '400',
      '700',
      '800'
    )
  ),
  'nanumgothiccoding' => array(
    'source' => 'google',
    'family' => 'Nanum Gothic Coding',
    'stack' => '"Nanum Gothic Coding", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'nanummyeongjo' => array(
    'source' => 'google',
    'family' => 'Nanum Myeongjo',
    'stack' => '"Nanum Myeongjo", serif',
    'weights' => array(
      '400',
      '700',
      '800'
    )
  ),
  'nanumpenscript' => array(
    'source' => 'google',
    'family' => 'Nanum Pen Script',
    'stack' => '"Nanum Pen Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'narnoor' => array(
    'source' => 'google',
    'family' => 'Narnoor',
    'stack' => '"Narnoor", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'nationalpark' => array(
    'source' => 'google',
    'family' => 'National Park',
    'stack' => '"National Park", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'neonderthaw' => array(
    'source' => 'google',
    'family' => 'Neonderthaw',
    'stack' => '"Neonderthaw", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'nerkoone' => array(
    'source' => 'google',
    'family' => 'Nerko One',
    'stack' => '"Nerko One", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'neucha' => array(
    'source' => 'google',
    'family' => 'Neucha',
    'stack' => '"Neucha", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'neuton' => array(
    'source' => 'google',
    'family' => 'Neuton',
    'stack' => '"Neuton", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '400i',
      '700',
      '800'
    )
  ),
  'newamsterdam' => array(
    'source' => 'google',
    'family' => 'New Amsterdam',
    'stack' => '"New Amsterdam", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'newrocker' => array(
    'source' => 'google',
    'family' => 'New Rocker',
    'stack' => '"New Rocker", display',
    'weights' => array(
      '400'
    )
  ),
  'newtegomin' => array(
    'source' => 'google',
    'family' => 'New Tegomin',
    'stack' => '"New Tegomin", serif',
    'weights' => array(
      '400'
    )
  ),
  'newscycle' => array(
    'source' => 'google',
    'family' => 'News Cycle',
    'stack' => '"News Cycle", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'newsreader' => array(
    'source' => 'google',
    'family' => 'Newsreader',
    'stack' => '"Newsreader", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'niconne' => array(
    'source' => 'google',
    'family' => 'Niconne',
    'stack' => '"Niconne", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'niramit' => array(
    'source' => 'google',
    'family' => 'Niramit',
    'stack' => '"Niramit", sans-serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'nixieone' => array(
    'source' => 'google',
    'family' => 'Nixie One',
    'stack' => '"Nixie One", display',
    'weights' => array(
      '400'
    )
  ),
  'nobile' => array(
    'source' => 'google',
    'family' => 'Nobile',
    'stack' => '"Nobile", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '500',
      '500i',
      '700',
      '700i'
    )
  ),
  'nokora' => array(
    'source' => 'google',
    'family' => 'Nokora',
    'stack' => '"Nokora", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'norican' => array(
    'source' => 'google',
    'family' => 'Norican',
    'stack' => '"Norican", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'nosifer' => array(
    'source' => 'google',
    'family' => 'Nosifer',
    'stack' => '"Nosifer", display',
    'weights' => array(
      '400'
    )
  ),
  'notable' => array(
    'source' => 'google',
    'family' => 'Notable',
    'stack' => '"Notable", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'nothingyoucoulddo' => array(
    'source' => 'google',
    'family' => 'Nothing You Could Do',
    'stack' => '"Nothing You Could Do", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'noticiatext' => array(
    'source' => 'google',
    'family' => 'Noticia Text',
    'stack' => '"Noticia Text", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'notocoloremoji' => array(
    'source' => 'google',
    'family' => 'Noto Color Emoji',
    'stack' => '"Noto Color Emoji", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notoemoji' => array(
    'source' => 'google',
    'family' => 'Noto Emoji',
    'stack' => '"Noto Emoji", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notokufiarabic' => array(
    'source' => 'google',
    'family' => 'Noto Kufi Arabic',
    'stack' => '"Noto Kufi Arabic", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notomusic' => array(
    'source' => 'google',
    'family' => 'Noto Music',
    'stack' => '"Noto Music", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notonaskharabic' => array(
    'source' => 'google',
    'family' => 'Noto Naskh Arabic',
    'stack' => '"Noto Naskh Arabic", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notonastaliqurdu' => array(
    'source' => 'google',
    'family' => 'Noto Nastaliq Urdu',
    'stack' => '"Noto Nastaliq Urdu", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notorashihebrew' => array(
    'source' => 'google',
    'family' => 'Noto Rashi Hebrew',
    'stack' => '"Noto Rashi Hebrew", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosans' => array(
    'source' => 'google',
    'family' => 'Noto Sans',
    'stack' => '"Noto Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'notosansadlam' => array(
    'source' => 'google',
    'family' => 'Noto Sans Adlam',
    'stack' => '"Noto Sans Adlam", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansadlamunjoined' => array(
    'source' => 'google',
    'family' => 'Noto Sans Adlam Unjoined',
    'stack' => '"Noto Sans Adlam Unjoined", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansanatolianhieroglyphs' => array(
    'source' => 'google',
    'family' => 'Noto Sans Anatolian Hieroglyphs',
    'stack' => '"Noto Sans Anatolian Hieroglyphs", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansarabic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Arabic',
    'stack' => '"Noto Sans Arabic", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansarmenian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Armenian',
    'stack' => '"Noto Sans Armenian", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansavestan' => array(
    'source' => 'google',
    'family' => 'Noto Sans Avestan',
    'stack' => '"Noto Sans Avestan", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansbalinese' => array(
    'source' => 'google',
    'family' => 'Noto Sans Balinese',
    'stack' => '"Noto Sans Balinese", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansbamum' => array(
    'source' => 'google',
    'family' => 'Noto Sans Bamum',
    'stack' => '"Noto Sans Bamum", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansbassavah' => array(
    'source' => 'google',
    'family' => 'Noto Sans Bassa Vah',
    'stack' => '"Noto Sans Bassa Vah", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansbatak' => array(
    'source' => 'google',
    'family' => 'Noto Sans Batak',
    'stack' => '"Noto Sans Batak", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansbengali' => array(
    'source' => 'google',
    'family' => 'Noto Sans Bengali',
    'stack' => '"Noto Sans Bengali", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansbhaiksuki' => array(
    'source' => 'google',
    'family' => 'Noto Sans Bhaiksuki',
    'stack' => '"Noto Sans Bhaiksuki", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansbrahmi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Brahmi',
    'stack' => '"Noto Sans Brahmi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansbuginese' => array(
    'source' => 'google',
    'family' => 'Noto Sans Buginese',
    'stack' => '"Noto Sans Buginese", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansbuhid' => array(
    'source' => 'google',
    'family' => 'Noto Sans Buhid',
    'stack' => '"Noto Sans Buhid", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanscanadianaboriginal' => array(
    'source' => 'google',
    'family' => 'Noto Sans Canadian Aboriginal',
    'stack' => '"Noto Sans Canadian Aboriginal", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanscarian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Carian',
    'stack' => '"Noto Sans Carian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanscaucasianalbanian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Caucasian Albanian',
    'stack' => '"Noto Sans Caucasian Albanian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanschakma' => array(
    'source' => 'google',
    'family' => 'Noto Sans Chakma',
    'stack' => '"Noto Sans Chakma", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanscham' => array(
    'source' => 'google',
    'family' => 'Noto Sans Cham',
    'stack' => '"Noto Sans Cham", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanscherokee' => array(
    'source' => 'google',
    'family' => 'Noto Sans Cherokee',
    'stack' => '"Noto Sans Cherokee", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanschorasmian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Chorasmian',
    'stack' => '"Noto Sans Chorasmian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanscoptic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Coptic',
    'stack' => '"Noto Sans Coptic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanscuneiform' => array(
    'source' => 'google',
    'family' => 'Noto Sans Cuneiform',
    'stack' => '"Noto Sans Cuneiform", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanscypriot' => array(
    'source' => 'google',
    'family' => 'Noto Sans Cypriot',
    'stack' => '"Noto Sans Cypriot", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanscyprominoan' => array(
    'source' => 'google',
    'family' => 'Noto Sans Cypro Minoan',
    'stack' => '"Noto Sans Cypro Minoan", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansdeseret' => array(
    'source' => 'google',
    'family' => 'Noto Sans Deseret',
    'stack' => '"Noto Sans Deseret", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansdevanagari' => array(
    'source' => 'google',
    'family' => 'Noto Sans Devanagari',
    'stack' => '"Noto Sans Devanagari", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansdisplay' => array(
    'source' => 'google',
    'family' => 'Noto Sans Display',
    'stack' => '"Noto Sans Display", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'notosansduployan' => array(
    'source' => 'google',
    'family' => 'Noto Sans Duployan',
    'stack' => '"Noto Sans Duployan", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'notosansegyptianhieroglyphs' => array(
    'source' => 'google',
    'family' => 'Noto Sans Egyptian Hieroglyphs',
    'stack' => '"Noto Sans Egyptian Hieroglyphs", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanselbasan' => array(
    'source' => 'google',
    'family' => 'Noto Sans Elbasan',
    'stack' => '"Noto Sans Elbasan", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanselymaic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Elymaic',
    'stack' => '"Noto Sans Elymaic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansethiopic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Ethiopic',
    'stack' => '"Noto Sans Ethiopic", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansgeorgian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Georgian',
    'stack' => '"Noto Sans Georgian", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansglagolitic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Glagolitic',
    'stack' => '"Noto Sans Glagolitic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansgothic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Gothic',
    'stack' => '"Noto Sans Gothic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansgrantha' => array(
    'source' => 'google',
    'family' => 'Noto Sans Grantha',
    'stack' => '"Noto Sans Grantha", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansgujarati' => array(
    'source' => 'google',
    'family' => 'Noto Sans Gujarati',
    'stack' => '"Noto Sans Gujarati", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansgunjalagondi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Gunjala Gondi',
    'stack' => '"Noto Sans Gunjala Gondi", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansgurmukhi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Gurmukhi',
    'stack' => '"Noto Sans Gurmukhi", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanshk' => array(
    'source' => 'google',
    'family' => 'Noto Sans HK',
    'stack' => '"Noto Sans HK", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanshanifirohingya' => array(
    'source' => 'google',
    'family' => 'Noto Sans Hanifi Rohingya',
    'stack' => '"Noto Sans Hanifi Rohingya", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanshanunoo' => array(
    'source' => 'google',
    'family' => 'Noto Sans Hanunoo',
    'stack' => '"Noto Sans Hanunoo", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanshatran' => array(
    'source' => 'google',
    'family' => 'Noto Sans Hatran',
    'stack' => '"Noto Sans Hatran", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanshebrew' => array(
    'source' => 'google',
    'family' => 'Noto Sans Hebrew',
    'stack' => '"Noto Sans Hebrew", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansimperialaramaic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Imperial Aramaic',
    'stack' => '"Noto Sans Imperial Aramaic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansindicsiyaqnumbers' => array(
    'source' => 'google',
    'family' => 'Noto Sans Indic Siyaq Numbers',
    'stack' => '"Noto Sans Indic Siyaq Numbers", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansinscriptionalpahlavi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Inscriptional Pahlavi',
    'stack' => '"Noto Sans Inscriptional Pahlavi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansinscriptionalparthian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Inscriptional Parthian',
    'stack' => '"Noto Sans Inscriptional Parthian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansjp' => array(
    'source' => 'google',
    'family' => 'Noto Sans JP',
    'stack' => '"Noto Sans JP", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansjavanese' => array(
    'source' => 'google',
    'family' => 'Noto Sans Javanese',
    'stack' => '"Noto Sans Javanese", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanskr' => array(
    'source' => 'google',
    'family' => 'Noto Sans KR',
    'stack' => '"Noto Sans KR", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanskaithi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Kaithi',
    'stack' => '"Noto Sans Kaithi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanskannada' => array(
    'source' => 'google',
    'family' => 'Noto Sans Kannada',
    'stack' => '"Noto Sans Kannada", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanskawi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Kawi',
    'stack' => '"Noto Sans Kawi", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanskayahli' => array(
    'source' => 'google',
    'family' => 'Noto Sans Kayah Li',
    'stack' => '"Noto Sans Kayah Li", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanskharoshthi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Kharoshthi',
    'stack' => '"Noto Sans Kharoshthi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanskhmer' => array(
    'source' => 'google',
    'family' => 'Noto Sans Khmer',
    'stack' => '"Noto Sans Khmer", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanskhojki' => array(
    'source' => 'google',
    'family' => 'Noto Sans Khojki',
    'stack' => '"Noto Sans Khojki", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanskhudawadi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Khudawadi',
    'stack' => '"Noto Sans Khudawadi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanslao' => array(
    'source' => 'google',
    'family' => 'Noto Sans Lao',
    'stack' => '"Noto Sans Lao", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanslaolooped' => array(
    'source' => 'google',
    'family' => 'Noto Sans Lao Looped',
    'stack' => '"Noto Sans Lao Looped", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanslepcha' => array(
    'source' => 'google',
    'family' => 'Noto Sans Lepcha',
    'stack' => '"Noto Sans Lepcha", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanslimbu' => array(
    'source' => 'google',
    'family' => 'Noto Sans Limbu',
    'stack' => '"Noto Sans Limbu", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanslineara' => array(
    'source' => 'google',
    'family' => 'Noto Sans Linear A',
    'stack' => '"Noto Sans Linear A", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanslinearb' => array(
    'source' => 'google',
    'family' => 'Noto Sans Linear B',
    'stack' => '"Noto Sans Linear B", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanslisu' => array(
    'source' => 'google',
    'family' => 'Noto Sans Lisu',
    'stack' => '"Noto Sans Lisu", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanslycian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Lycian',
    'stack' => '"Noto Sans Lycian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanslydian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Lydian',
    'stack' => '"Noto Sans Lydian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmahajani' => array(
    'source' => 'google',
    'family' => 'Noto Sans Mahajani',
    'stack' => '"Noto Sans Mahajani", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmalayalam' => array(
    'source' => 'google',
    'family' => 'Noto Sans Malayalam',
    'stack' => '"Noto Sans Malayalam", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansmandaic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Mandaic',
    'stack' => '"Noto Sans Mandaic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmanichaean' => array(
    'source' => 'google',
    'family' => 'Noto Sans Manichaean',
    'stack' => '"Noto Sans Manichaean", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmarchen' => array(
    'source' => 'google',
    'family' => 'Noto Sans Marchen',
    'stack' => '"Noto Sans Marchen", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmasaramgondi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Masaram Gondi',
    'stack' => '"Noto Sans Masaram Gondi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmath' => array(
    'source' => 'google',
    'family' => 'Noto Sans Math',
    'stack' => '"Noto Sans Math", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmayannumerals' => array(
    'source' => 'google',
    'family' => 'Noto Sans Mayan Numerals',
    'stack' => '"Noto Sans Mayan Numerals", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmedefaidrin' => array(
    'source' => 'google',
    'family' => 'Noto Sans Medefaidrin',
    'stack' => '"Noto Sans Medefaidrin", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansmeeteimayek' => array(
    'source' => 'google',
    'family' => 'Noto Sans Meetei Mayek',
    'stack' => '"Noto Sans Meetei Mayek", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansmendekikakui' => array(
    'source' => 'google',
    'family' => 'Noto Sans Mende Kikakui',
    'stack' => '"Noto Sans Mende Kikakui", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmeroitic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Meroitic',
    'stack' => '"Noto Sans Meroitic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmiao' => array(
    'source' => 'google',
    'family' => 'Noto Sans Miao',
    'stack' => '"Noto Sans Miao", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmodi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Modi',
    'stack' => '"Noto Sans Modi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmongolian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Mongolian',
    'stack' => '"Noto Sans Mongolian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmono' => array(
    'source' => 'google',
    'family' => 'Noto Sans Mono',
    'stack' => '"Noto Sans Mono", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansmro' => array(
    'source' => 'google',
    'family' => 'Noto Sans Mro',
    'stack' => '"Noto Sans Mro", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmultani' => array(
    'source' => 'google',
    'family' => 'Noto Sans Multani',
    'stack' => '"Noto Sans Multani", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansmyanmar' => array(
    'source' => 'google',
    'family' => 'Noto Sans Myanmar',
    'stack' => '"Noto Sans Myanmar", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansnko' => array(
    'source' => 'google',
    'family' => 'Noto Sans NKo',
    'stack' => '"Noto Sans NKo", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansnkounjoined' => array(
    'source' => 'google',
    'family' => 'Noto Sans NKo Unjoined',
    'stack' => '"Noto Sans NKo Unjoined", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansnabataean' => array(
    'source' => 'google',
    'family' => 'Noto Sans Nabataean',
    'stack' => '"Noto Sans Nabataean", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansnagmundari' => array(
    'source' => 'google',
    'family' => 'Noto Sans Nag Mundari',
    'stack' => '"Noto Sans Nag Mundari", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansnandinagari' => array(
    'source' => 'google',
    'family' => 'Noto Sans Nandinagari',
    'stack' => '"Noto Sans Nandinagari", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansnewtailue' => array(
    'source' => 'google',
    'family' => 'Noto Sans New Tai Lue',
    'stack' => '"Noto Sans New Tai Lue", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansnewa' => array(
    'source' => 'google',
    'family' => 'Noto Sans Newa',
    'stack' => '"Noto Sans Newa", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansnushu' => array(
    'source' => 'google',
    'family' => 'Noto Sans Nushu',
    'stack' => '"Noto Sans Nushu", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansogham' => array(
    'source' => 'google',
    'family' => 'Noto Sans Ogham',
    'stack' => '"Noto Sans Ogham", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansolchiki' => array(
    'source' => 'google',
    'family' => 'Noto Sans Ol Chiki',
    'stack' => '"Noto Sans Ol Chiki", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosansoldhungarian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Old Hungarian',
    'stack' => '"Noto Sans Old Hungarian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansolditalic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Old Italic',
    'stack' => '"Noto Sans Old Italic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansoldnortharabian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Old North Arabian',
    'stack' => '"Noto Sans Old North Arabian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansoldpermic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Old Permic',
    'stack' => '"Noto Sans Old Permic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansoldpersian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Old Persian',
    'stack' => '"Noto Sans Old Persian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansoldsogdian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Old Sogdian',
    'stack' => '"Noto Sans Old Sogdian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansoldsoutharabian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Old South Arabian',
    'stack' => '"Noto Sans Old South Arabian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansoldturkic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Old Turkic',
    'stack' => '"Noto Sans Old Turkic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansoriya' => array(
    'source' => 'google',
    'family' => 'Noto Sans Oriya',
    'stack' => '"Noto Sans Oriya", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansosage' => array(
    'source' => 'google',
    'family' => 'Noto Sans Osage',
    'stack' => '"Noto Sans Osage", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansosmanya' => array(
    'source' => 'google',
    'family' => 'Noto Sans Osmanya',
    'stack' => '"Noto Sans Osmanya", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanspahawhhmong' => array(
    'source' => 'google',
    'family' => 'Noto Sans Pahawh Hmong',
    'stack' => '"Noto Sans Pahawh Hmong", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanspalmyrene' => array(
    'source' => 'google',
    'family' => 'Noto Sans Palmyrene',
    'stack' => '"Noto Sans Palmyrene", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanspaucinhau' => array(
    'source' => 'google',
    'family' => 'Noto Sans Pau Cin Hau',
    'stack' => '"Noto Sans Pau Cin Hau", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansphagspa' => array(
    'source' => 'google',
    'family' => 'Noto Sans PhagsPa',
    'stack' => '"Noto Sans PhagsPa", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansphoenician' => array(
    'source' => 'google',
    'family' => 'Noto Sans Phoenician',
    'stack' => '"Noto Sans Phoenician", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanspsalterpahlavi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Psalter Pahlavi',
    'stack' => '"Noto Sans Psalter Pahlavi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansrejang' => array(
    'source' => 'google',
    'family' => 'Noto Sans Rejang',
    'stack' => '"Noto Sans Rejang", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansrunic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Runic',
    'stack' => '"Noto Sans Runic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssc' => array(
    'source' => 'google',
    'family' => 'Noto Sans SC',
    'stack' => '"Noto Sans SC", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanssamaritan' => array(
    'source' => 'google',
    'family' => 'Noto Sans Samaritan',
    'stack' => '"Noto Sans Samaritan", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssaurashtra' => array(
    'source' => 'google',
    'family' => 'Noto Sans Saurashtra',
    'stack' => '"Noto Sans Saurashtra", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssharada' => array(
    'source' => 'google',
    'family' => 'Noto Sans Sharada',
    'stack' => '"Noto Sans Sharada", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansshavian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Shavian',
    'stack' => '"Noto Sans Shavian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssiddham' => array(
    'source' => 'google',
    'family' => 'Noto Sans Siddham',
    'stack' => '"Noto Sans Siddham", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssignwriting' => array(
    'source' => 'google',
    'family' => 'Noto Sans SignWriting',
    'stack' => '"Noto Sans SignWriting", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssinhala' => array(
    'source' => 'google',
    'family' => 'Noto Sans Sinhala',
    'stack' => '"Noto Sans Sinhala", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanssogdian' => array(
    'source' => 'google',
    'family' => 'Noto Sans Sogdian',
    'stack' => '"Noto Sans Sogdian", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssorasompeng' => array(
    'source' => 'google',
    'family' => 'Noto Sans Sora Sompeng',
    'stack' => '"Noto Sans Sora Sompeng", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanssoyombo' => array(
    'source' => 'google',
    'family' => 'Noto Sans Soyombo',
    'stack' => '"Noto Sans Soyombo", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssundanese' => array(
    'source' => 'google',
    'family' => 'Noto Sans Sundanese',
    'stack' => '"Noto Sans Sundanese", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanssunuwar' => array(
    'source' => 'google',
    'family' => 'Noto Sans Sunuwar',
    'stack' => '"Noto Sans Sunuwar", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssylotinagri' => array(
    'source' => 'google',
    'family' => 'Noto Sans Syloti Nagri',
    'stack' => '"Noto Sans Syloti Nagri", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssymbols' => array(
    'source' => 'google',
    'family' => 'Noto Sans Symbols',
    'stack' => '"Noto Sans Symbols", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanssymbols2' => array(
    'source' => 'google',
    'family' => 'Noto Sans Symbols 2',
    'stack' => '"Noto Sans Symbols 2", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanssyriac' => array(
    'source' => 'google',
    'family' => 'Noto Sans Syriac',
    'stack' => '"Noto Sans Syriac", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanssyriaceastern' => array(
    'source' => 'google',
    'family' => 'Noto Sans Syriac Eastern',
    'stack' => '"Noto Sans Syriac Eastern", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanstc' => array(
    'source' => 'google',
    'family' => 'Noto Sans TC',
    'stack' => '"Noto Sans TC", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanstagalog' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tagalog',
    'stack' => '"Noto Sans Tagalog", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanstagbanwa' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tagbanwa',
    'stack' => '"Noto Sans Tagbanwa", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanstaile' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tai Le',
    'stack' => '"Noto Sans Tai Le", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanstaitham' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tai Tham',
    'stack' => '"Noto Sans Tai Tham", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanstaiviet' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tai Viet',
    'stack' => '"Noto Sans Tai Viet", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanstakri' => array(
    'source' => 'google',
    'family' => 'Noto Sans Takri',
    'stack' => '"Noto Sans Takri", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanstamil' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tamil',
    'stack' => '"Noto Sans Tamil", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanstamilsupplement' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tamil Supplement',
    'stack' => '"Noto Sans Tamil Supplement", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanstangsa' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tangsa',
    'stack' => '"Noto Sans Tangsa", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanstelugu' => array(
    'source' => 'google',
    'family' => 'Noto Sans Telugu',
    'stack' => '"Noto Sans Telugu", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansthaana' => array(
    'source' => 'google',
    'family' => 'Noto Sans Thaana',
    'stack' => '"Noto Sans Thaana", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansthai' => array(
    'source' => 'google',
    'family' => 'Noto Sans Thai',
    'stack' => '"Noto Sans Thai", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosansthailooped' => array(
    'source' => 'google',
    'family' => 'Noto Sans Thai Looped',
    'stack' => '"Noto Sans Thai Looped", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notosanstifinagh' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tifinagh',
    'stack' => '"Noto Sans Tifinagh", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanstirhuta' => array(
    'source' => 'google',
    'family' => 'Noto Sans Tirhuta',
    'stack' => '"Noto Sans Tirhuta", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansugaritic' => array(
    'source' => 'google',
    'family' => 'Noto Sans Ugaritic',
    'stack' => '"Noto Sans Ugaritic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansvai' => array(
    'source' => 'google',
    'family' => 'Noto Sans Vai',
    'stack' => '"Noto Sans Vai", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansvithkuqi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Vithkuqi',
    'stack' => '"Noto Sans Vithkuqi", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notosanswancho' => array(
    'source' => 'google',
    'family' => 'Noto Sans Wancho',
    'stack' => '"Noto Sans Wancho", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanswarangciti' => array(
    'source' => 'google',
    'family' => 'Noto Sans Warang Citi',
    'stack' => '"Noto Sans Warang Citi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosansyi' => array(
    'source' => 'google',
    'family' => 'Noto Sans Yi',
    'stack' => '"Noto Sans Yi", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notosanszanabazarsquare' => array(
    'source' => 'google',
    'family' => 'Noto Sans Zanabazar Square',
    'stack' => '"Noto Sans Zanabazar Square", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'notoserif' => array(
    'source' => 'google',
    'family' => 'Noto Serif',
    'stack' => '"Noto Serif", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'notoserifahom' => array(
    'source' => 'google',
    'family' => 'Noto Serif Ahom',
    'stack' => '"Noto Serif Ahom", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoserifarmenian' => array(
    'source' => 'google',
    'family' => 'Noto Serif Armenian',
    'stack' => '"Noto Serif Armenian", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifbalinese' => array(
    'source' => 'google',
    'family' => 'Noto Serif Balinese',
    'stack' => '"Noto Serif Balinese", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoserifbengali' => array(
    'source' => 'google',
    'family' => 'Noto Serif Bengali',
    'stack' => '"Noto Serif Bengali", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifdevanagari' => array(
    'source' => 'google',
    'family' => 'Noto Serif Devanagari',
    'stack' => '"Noto Serif Devanagari", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifdisplay' => array(
    'source' => 'google',
    'family' => 'Noto Serif Display',
    'stack' => '"Noto Serif Display", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'notoserifdivesakuru' => array(
    'source' => 'google',
    'family' => 'Noto Serif Dives Akuru',
    'stack' => '"Noto Serif Dives Akuru", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoserifdogra' => array(
    'source' => 'google',
    'family' => 'Noto Serif Dogra',
    'stack' => '"Noto Serif Dogra", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoserifethiopic' => array(
    'source' => 'google',
    'family' => 'Noto Serif Ethiopic',
    'stack' => '"Noto Serif Ethiopic", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifgeorgian' => array(
    'source' => 'google',
    'family' => 'Noto Serif Georgian',
    'stack' => '"Noto Serif Georgian", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifgrantha' => array(
    'source' => 'google',
    'family' => 'Noto Serif Grantha',
    'stack' => '"Noto Serif Grantha", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoserifgujarati' => array(
    'source' => 'google',
    'family' => 'Noto Serif Gujarati',
    'stack' => '"Noto Serif Gujarati", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifgurmukhi' => array(
    'source' => 'google',
    'family' => 'Noto Serif Gurmukhi',
    'stack' => '"Noto Serif Gurmukhi", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifhk' => array(
    'source' => 'google',
    'family' => 'Noto Serif HK',
    'stack' => '"Noto Serif HK", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifhebrew' => array(
    'source' => 'google',
    'family' => 'Noto Serif Hebrew',
    'stack' => '"Noto Serif Hebrew", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifhentaigana' => array(
    'source' => 'google',
    'family' => 'Noto Serif Hentaigana',
    'stack' => '"Noto Serif Hentaigana", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifjp' => array(
    'source' => 'google',
    'family' => 'Noto Serif JP',
    'stack' => '"Noto Serif JP", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifkr' => array(
    'source' => 'google',
    'family' => 'Noto Serif KR',
    'stack' => '"Noto Serif KR", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifkannada' => array(
    'source' => 'google',
    'family' => 'Noto Serif Kannada',
    'stack' => '"Noto Serif Kannada", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifkhitansmallscript' => array(
    'source' => 'google',
    'family' => 'Noto Serif Khitan Small Script',
    'stack' => '"Noto Serif Khitan Small Script", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoserifkhmer' => array(
    'source' => 'google',
    'family' => 'Noto Serif Khmer',
    'stack' => '"Noto Serif Khmer", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifkhojki' => array(
    'source' => 'google',
    'family' => 'Noto Serif Khojki',
    'stack' => '"Noto Serif Khojki", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notoseriflao' => array(
    'source' => 'google',
    'family' => 'Noto Serif Lao',
    'stack' => '"Noto Serif Lao", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifmakasar' => array(
    'source' => 'google',
    'family' => 'Noto Serif Makasar',
    'stack' => '"Noto Serif Makasar", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoserifmalayalam' => array(
    'source' => 'google',
    'family' => 'Noto Serif Malayalam',
    'stack' => '"Noto Serif Malayalam", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifmyanmar' => array(
    'source' => 'google',
    'family' => 'Noto Serif Myanmar',
    'stack' => '"Noto Serif Myanmar", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifnphmong' => array(
    'source' => 'google',
    'family' => 'Noto Serif NP Hmong',
    'stack' => '"Noto Serif NP Hmong", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notoserifolduyghur' => array(
    'source' => 'google',
    'family' => 'Noto Serif Old Uyghur',
    'stack' => '"Noto Serif Old Uyghur", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoseriforiya' => array(
    'source' => 'google',
    'family' => 'Noto Serif Oriya',
    'stack' => '"Noto Serif Oriya", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notoserifottomansiyaq' => array(
    'source' => 'google',
    'family' => 'Noto Serif Ottoman Siyaq',
    'stack' => '"Noto Serif Ottoman Siyaq", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoserifsc' => array(
    'source' => 'google',
    'family' => 'Noto Serif SC',
    'stack' => '"Noto Serif SC", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifsinhala' => array(
    'source' => 'google',
    'family' => 'Noto Serif Sinhala',
    'stack' => '"Noto Serif Sinhala", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoseriftc' => array(
    'source' => 'google',
    'family' => 'Noto Serif TC',
    'stack' => '"Noto Serif TC", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoseriftamil' => array(
    'source' => 'google',
    'family' => 'Noto Serif Tamil',
    'stack' => '"Noto Serif Tamil", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'notoseriftangut' => array(
    'source' => 'google',
    'family' => 'Noto Serif Tangut',
    'stack' => '"Noto Serif Tangut", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoseriftelugu' => array(
    'source' => 'google',
    'family' => 'Noto Serif Telugu',
    'stack' => '"Noto Serif Telugu", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoserifthai' => array(
    'source' => 'google',
    'family' => 'Noto Serif Thai',
    'stack' => '"Noto Serif Thai", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoseriftibetan' => array(
    'source' => 'google',
    'family' => 'Noto Serif Tibetan',
    'stack' => '"Noto Serif Tibetan", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'notoseriftodhri' => array(
    'source' => 'google',
    'family' => 'Noto Serif Todhri',
    'stack' => '"Noto Serif Todhri", serif',
    'weights' => array(
      '400'
    )
  ),
  'notoseriftoto' => array(
    'source' => 'google',
    'family' => 'Noto Serif Toto',
    'stack' => '"Noto Serif Toto", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notoserifvithkuqi' => array(
    'source' => 'google',
    'family' => 'Noto Serif Vithkuqi',
    'stack' => '"Noto Serif Vithkuqi", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notoserifyezidi' => array(
    'source' => 'google',
    'family' => 'Noto Serif Yezidi',
    'stack' => '"Noto Serif Yezidi", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'nototraditionalnushu' => array(
    'source' => 'google',
    'family' => 'Noto Traditional Nushu',
    'stack' => '"Noto Traditional Nushu", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'notoznamennymusicalnotation' => array(
    'source' => 'google',
    'family' => 'Noto Znamenny Musical Notation',
    'stack' => '"Noto Znamenny Musical Notation", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'novacut' => array(
    'source' => 'google',
    'family' => 'Nova Cut',
    'stack' => '"Nova Cut", display',
    'weights' => array(
      '400'
    )
  ),
  'novaflat' => array(
    'source' => 'google',
    'family' => 'Nova Flat',
    'stack' => '"Nova Flat", display',
    'weights' => array(
      '400'
    )
  ),
  'novamono' => array(
    'source' => 'google',
    'family' => 'Nova Mono',
    'stack' => '"Nova Mono", monospace',
    'weights' => array(
      '400'
    )
  ),
  'novaoval' => array(
    'source' => 'google',
    'family' => 'Nova Oval',
    'stack' => '"Nova Oval", display',
    'weights' => array(
      '400'
    )
  ),
  'novaround' => array(
    'source' => 'google',
    'family' => 'Nova Round',
    'stack' => '"Nova Round", display',
    'weights' => array(
      '400'
    )
  ),
  'novascript' => array(
    'source' => 'google',
    'family' => 'Nova Script',
    'stack' => '"Nova Script", display',
    'weights' => array(
      '400'
    )
  ),
  'novaslim' => array(
    'source' => 'google',
    'family' => 'Nova Slim',
    'stack' => '"Nova Slim", display',
    'weights' => array(
      '400'
    )
  ),
  'novasquare' => array(
    'source' => 'google',
    'family' => 'Nova Square',
    'stack' => '"Nova Square", display',
    'weights' => array(
      '400'
    )
  ),
  'numans' => array(
    'source' => 'google',
    'family' => 'Numans',
    'stack' => '"Numans", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'nunito' => array(
    'source' => 'google',
    'family' => 'Nunito',
    'stack' => '"Nunito", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'nunitosans' => array(
    'source' => 'google',
    'family' => 'Nunito Sans',
    'stack' => '"Nunito Sans", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'nuosusil' => array(
    'source' => 'google',
    'family' => 'Nuosu SIL',
    'stack' => '"Nuosu SIL", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'odibeesans' => array(
    'source' => 'google',
    'family' => 'Odibee Sans',
    'stack' => '"Odibee Sans", display',
    'weights' => array(
      '400'
    )
  ),
  'odormeanchey' => array(
    'source' => 'google',
    'family' => 'Odor Mean Chey',
    'stack' => '"Odor Mean Chey", serif',
    'weights' => array(
      '400'
    )
  ),
  'offside' => array(
    'source' => 'google',
    'family' => 'Offside',
    'stack' => '"Offside", display',
    'weights' => array(
      '400'
    )
  ),
  'oi' => array(
    'source' => 'google',
    'family' => 'Oi',
    'stack' => '"Oi", display',
    'weights' => array(
      '400'
    )
  ),
  'ojuju' => array(
    'source' => 'google',
    'family' => 'Ojuju',
    'stack' => '"Ojuju", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'oldstandardtt' => array(
    'source' => 'google',
    'family' => 'Old Standard TT',
    'stack' => '"Old Standard TT", serif',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'oldenburg' => array(
    'source' => 'google',
    'family' => 'Oldenburg',
    'stack' => '"Oldenburg", display',
    'weights' => array(
      '400'
    )
  ),
  'ole' => array(
    'source' => 'google',
    'family' => 'Ole',
    'stack' => '"Ole", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'oleoscript' => array(
    'source' => 'google',
    'family' => 'Oleo Script',
    'stack' => '"Oleo Script", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'oleoscriptswashcaps' => array(
    'source' => 'google',
    'family' => 'Oleo Script Swash Caps',
    'stack' => '"Oleo Script Swash Caps", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'onest' => array(
    'source' => 'google',
    'family' => 'Onest',
    'stack' => '"Onest", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'ooohbaby' => array(
    'source' => 'google',
    'family' => 'Oooh Baby',
    'stack' => '"Oooh Baby", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'opensans' => array(
    'source' => 'google',
    'family' => 'Open Sans',
    'stack' => '"Open Sans", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'oranienbaum' => array(
    'source' => 'google',
    'family' => 'Oranienbaum',
    'stack' => '"Oranienbaum", serif',
    'weights' => array(
      '400'
    )
  ),
  'orbit' => array(
    'source' => 'google',
    'family' => 'Orbit',
    'stack' => '"Orbit", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'orbitron' => array(
    'source' => 'google',
    'family' => 'Orbitron',
    'stack' => '"Orbitron", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'oregano' => array(
    'source' => 'google',
    'family' => 'Oregano',
    'stack' => '"Oregano", display',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'orelegaone' => array(
    'source' => 'google',
    'family' => 'Orelega One',
    'stack' => '"Orelega One", display',
    'weights' => array(
      '400'
    )
  ),
  'orienta' => array(
    'source' => 'google',
    'family' => 'Orienta',
    'stack' => '"Orienta", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'originalsurfer' => array(
    'source' => 'google',
    'family' => 'Original Surfer',
    'stack' => '"Original Surfer", display',
    'weights' => array(
      '400'
    )
  ),
  'oswald' => array(
    'source' => 'google',
    'family' => 'Oswald',
    'stack' => '"Oswald", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'outfit' => array(
    'source' => 'google',
    'family' => 'Outfit',
    'stack' => '"Outfit", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'overtherainbow' => array(
    'source' => 'google',
    'family' => 'Over the Rainbow',
    'stack' => '"Over the Rainbow", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'overlock' => array(
    'source' => 'google',
    'family' => 'Overlock',
    'stack' => '"Overlock", display',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i',
      '900',
      '900i'
    )
  ),
  'overlocksc' => array(
    'source' => 'google',
    'family' => 'Overlock SC',
    'stack' => '"Overlock SC", display',
    'weights' => array(
      '400'
    )
  ),
  'overpass' => array(
    'source' => 'google',
    'family' => 'Overpass',
    'stack' => '"Overpass", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'overpassmono' => array(
    'source' => 'google',
    'family' => 'Overpass Mono',
    'stack' => '"Overpass Mono", monospace',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'ovo' => array(
    'source' => 'google',
    'family' => 'Ovo',
    'stack' => '"Ovo", serif',
    'weights' => array(
      '400'
    )
  ),
  'oxanium' => array(
    'source' => 'google',
    'family' => 'Oxanium',
    'stack' => '"Oxanium", display',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'oxygen' => array(
    'source' => 'google',
    'family' => 'Oxygen',
    'stack' => '"Oxygen", sans-serif',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'oxygenmono' => array(
    'source' => 'google',
    'family' => 'Oxygen Mono',
    'stack' => '"Oxygen Mono", monospace',
    'weights' => array(
      '400'
    )
  ),
  'ptmono' => array(
    'source' => 'google',
    'family' => 'PT Mono',
    'stack' => '"PT Mono", monospace',
    'weights' => array(
      '400'
    )
  ),
  'ptsans' => array(
    'source' => 'google',
    'family' => 'PT Sans',
    'stack' => '"PT Sans", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'ptsanscaption' => array(
    'source' => 'google',
    'family' => 'PT Sans Caption',
    'stack' => '"PT Sans Caption", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'ptsansnarrow' => array(
    'source' => 'google',
    'family' => 'PT Sans Narrow',
    'stack' => '"PT Sans Narrow", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'ptserif' => array(
    'source' => 'google',
    'family' => 'PT Serif',
    'stack' => '"PT Serif", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'ptserifcaption' => array(
    'source' => 'google',
    'family' => 'PT Serif Caption',
    'stack' => '"PT Serif Caption", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'pacifico' => array(
    'source' => 'google',
    'family' => 'Pacifico',
    'stack' => '"Pacifico", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'padauk' => array(
    'source' => 'google',
    'family' => 'Padauk',
    'stack' => '"Padauk", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'padyakkeexpandedone' => array(
    'source' => 'google',
    'family' => 'Padyakke Expanded One',
    'stack' => '"Padyakke Expanded One", serif',
    'weights' => array(
      '400'
    )
  ),
  'palanquin' => array(
    'source' => 'google',
    'family' => 'Palanquin',
    'stack' => '"Palanquin", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'palanquindark' => array(
    'source' => 'google',
    'family' => 'Palanquin Dark',
    'stack' => '"Palanquin Dark", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'palettemosaic' => array(
    'source' => 'google',
    'family' => 'Palette Mosaic',
    'stack' => '"Palette Mosaic", display',
    'weights' => array(
      '400'
    )
  ),
  'pangolin' => array(
    'source' => 'google',
    'family' => 'Pangolin',
    'stack' => '"Pangolin", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'paprika' => array(
    'source' => 'google',
    'family' => 'Paprika',
    'stack' => '"Paprika", display',
    'weights' => array(
      '400'
    )
  ),
  'parastoo' => array(
    'source' => 'google',
    'family' => 'Parastoo',
    'stack' => '"Parastoo", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'parisienne' => array(
    'source' => 'google',
    'family' => 'Parisienne',
    'stack' => '"Parisienne", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'parkinsans' => array(
    'source' => 'google',
    'family' => 'Parkinsans',
    'stack' => '"Parkinsans", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'passeroone' => array(
    'source' => 'google',
    'family' => 'Passero One',
    'stack' => '"Passero One", display',
    'weights' => array(
      '400'
    )
  ),
  'passionone' => array(
    'source' => 'google',
    'family' => 'Passion One',
    'stack' => '"Passion One", display',
    'weights' => array(
      '400',
      '700',
      '900'
    )
  ),
  'passionsconflict' => array(
    'source' => 'google',
    'family' => 'Passions Conflict',
    'stack' => '"Passions Conflict", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'pathwayextreme' => array(
    'source' => 'google',
    'family' => 'Pathway Extreme',
    'stack' => '"Pathway Extreme", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'pathwaygothicone' => array(
    'source' => 'google',
    'family' => 'Pathway Gothic One',
    'stack' => '"Pathway Gothic One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'patrickhand' => array(
    'source' => 'google',
    'family' => 'Patrick Hand',
    'stack' => '"Patrick Hand", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'patrickhandsc' => array(
    'source' => 'google',
    'family' => 'Patrick Hand SC',
    'stack' => '"Patrick Hand SC", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'pattaya' => array(
    'source' => 'google',
    'family' => 'Pattaya',
    'stack' => '"Pattaya", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'patuaone' => array(
    'source' => 'google',
    'family' => 'Patua One',
    'stack' => '"Patua One", display',
    'weights' => array(
      '400'
    )
  ),
  'pavanam' => array(
    'source' => 'google',
    'family' => 'Pavanam',
    'stack' => '"Pavanam", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'paytoneone' => array(
    'source' => 'google',
    'family' => 'Paytone One',
    'stack' => '"Paytone One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'peddana' => array(
    'source' => 'google',
    'family' => 'Peddana',
    'stack' => '"Peddana", serif',
    'weights' => array(
      '400'
    )
  ),
  'peralta' => array(
    'source' => 'google',
    'family' => 'Peralta',
    'stack' => '"Peralta", serif',
    'weights' => array(
      '400'
    )
  ),
  'permanentmarker' => array(
    'source' => 'google',
    'family' => 'Permanent Marker',
    'stack' => '"Permanent Marker", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'petemoss' => array(
    'source' => 'google',
    'family' => 'Petemoss',
    'stack' => '"Petemoss", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'petitformalscript' => array(
    'source' => 'google',
    'family' => 'Petit Formal Script',
    'stack' => '"Petit Formal Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'petrona' => array(
    'source' => 'google',
    'family' => 'Petrona',
    'stack' => '"Petrona", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'phetsarath' => array(
    'source' => 'google',
    'family' => 'Phetsarath',
    'stack' => '"Phetsarath", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'philosopher' => array(
    'source' => 'google',
    'family' => 'Philosopher',
    'stack' => '"Philosopher", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'phudu' => array(
    'source' => 'google',
    'family' => 'Phudu',
    'stack' => '"Phudu", display',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'piazzolla' => array(
    'source' => 'google',
    'family' => 'Piazzolla',
    'stack' => '"Piazzolla", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'piedra' => array(
    'source' => 'google',
    'family' => 'Piedra',
    'stack' => '"Piedra", display',
    'weights' => array(
      '400'
    )
  ),
  'pinyonscript' => array(
    'source' => 'google',
    'family' => 'Pinyon Script',
    'stack' => '"Pinyon Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'pirataone' => array(
    'source' => 'google',
    'family' => 'Pirata One',
    'stack' => '"Pirata One", display',
    'weights' => array(
      '400'
    )
  ),
  'pixelifysans' => array(
    'source' => 'google',
    'family' => 'Pixelify Sans',
    'stack' => '"Pixelify Sans", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'plaster' => array(
    'source' => 'google',
    'family' => 'Plaster',
    'stack' => '"Plaster", display',
    'weights' => array(
      '400'
    )
  ),
  'platypi' => array(
    'source' => 'google',
    'family' => 'Platypi',
    'stack' => '"Platypi", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'play' => array(
    'source' => 'google',
    'family' => 'Play',
    'stack' => '"Play", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'playball' => array(
    'source' => 'google',
    'family' => 'Playball',
    'stack' => '"Playball", display',
    'weights' => array(
      '400'
    )
  ),
  'playfair' => array(
    'source' => 'google',
    'family' => 'Playfair',
    'stack' => '"Playfair", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'playfairdisplay' => array(
    'source' => 'google',
    'family' => 'Playfair Display',
    'stack' => '"Playfair Display", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'playfairdisplaysc' => array(
    'source' => 'google',
    'family' => 'Playfair Display SC',
    'stack' => '"Playfair Display SC", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i',
      '900',
      '900i'
    )
  ),
  'playpensans' => array(
    'source' => 'google',
    'family' => 'Playpen Sans',
    'stack' => '"Playpen Sans", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'playpensansarabic' => array(
    'source' => 'google',
    'family' => 'Playpen Sans Arabic',
    'stack' => '"Playpen Sans Arabic", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'playpensansdeva' => array(
    'source' => 'google',
    'family' => 'Playpen Sans Deva',
    'stack' => '"Playpen Sans Deva", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'playpensanshebrew' => array(
    'source' => 'google',
    'family' => 'Playpen Sans Hebrew',
    'stack' => '"Playpen Sans Hebrew", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'playpensansthai' => array(
    'source' => 'google',
    'family' => 'Playpen Sans Thai',
    'stack' => '"Playpen Sans Thai", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'playwritear' => array(
    'source' => 'google',
    'family' => 'Playwrite AR',
    'stack' => '"Playwrite AR", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritearguides' => array(
    'source' => 'google',
    'family' => 'Playwrite AR Guides',
    'stack' => '"Playwrite AR Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteat' => array(
    'source' => 'google',
    'family' => 'Playwrite AT',
    'stack' => '"Playwrite AT", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '100i',
      '200i',
      '300i',
      '400i'
    )
  ),
  'playwriteatguides' => array(
    'source' => 'google',
    'family' => 'Playwrite AT Guides',
    'stack' => '"Playwrite AT Guides", handwriting',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'playwriteaunsw' => array(
    'source' => 'google',
    'family' => 'Playwrite AU NSW',
    'stack' => '"Playwrite AU NSW", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteaunswguides' => array(
    'source' => 'google',
    'family' => 'Playwrite AU NSW Guides',
    'stack' => '"Playwrite AU NSW Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteauqld' => array(
    'source' => 'google',
    'family' => 'Playwrite AU QLD',
    'stack' => '"Playwrite AU QLD", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteauqldguides' => array(
    'source' => 'google',
    'family' => 'Playwrite AU QLD Guides',
    'stack' => '"Playwrite AU QLD Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteausa' => array(
    'source' => 'google',
    'family' => 'Playwrite AU SA',
    'stack' => '"Playwrite AU SA", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteausaguides' => array(
    'source' => 'google',
    'family' => 'Playwrite AU SA Guides',
    'stack' => '"Playwrite AU SA Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteautas' => array(
    'source' => 'google',
    'family' => 'Playwrite AU TAS',
    'stack' => '"Playwrite AU TAS", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteautasguides' => array(
    'source' => 'google',
    'family' => 'Playwrite AU TAS Guides',
    'stack' => '"Playwrite AU TAS Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteauvic' => array(
    'source' => 'google',
    'family' => 'Playwrite AU VIC',
    'stack' => '"Playwrite AU VIC", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteauvicguides' => array(
    'source' => 'google',
    'family' => 'Playwrite AU VIC Guides',
    'stack' => '"Playwrite AU VIC Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritebevlg' => array(
    'source' => 'google',
    'family' => 'Playwrite BE VLG',
    'stack' => '"Playwrite BE VLG", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritebevlgguides' => array(
    'source' => 'google',
    'family' => 'Playwrite BE VLG Guides',
    'stack' => '"Playwrite BE VLG Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritebewal' => array(
    'source' => 'google',
    'family' => 'Playwrite BE WAL',
    'stack' => '"Playwrite BE WAL", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritebewalguides' => array(
    'source' => 'google',
    'family' => 'Playwrite BE WAL Guides',
    'stack' => '"Playwrite BE WAL Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritebr' => array(
    'source' => 'google',
    'family' => 'Playwrite BR',
    'stack' => '"Playwrite BR", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritebrguides' => array(
    'source' => 'google',
    'family' => 'Playwrite BR Guides',
    'stack' => '"Playwrite BR Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteca' => array(
    'source' => 'google',
    'family' => 'Playwrite CA',
    'stack' => '"Playwrite CA", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritecaguides' => array(
    'source' => 'google',
    'family' => 'Playwrite CA Guides',
    'stack' => '"Playwrite CA Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritecl' => array(
    'source' => 'google',
    'family' => 'Playwrite CL',
    'stack' => '"Playwrite CL", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteclguides' => array(
    'source' => 'google',
    'family' => 'Playwrite CL Guides',
    'stack' => '"Playwrite CL Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteco' => array(
    'source' => 'google',
    'family' => 'Playwrite CO',
    'stack' => '"Playwrite CO", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritecoguides' => array(
    'source' => 'google',
    'family' => 'Playwrite CO Guides',
    'stack' => '"Playwrite CO Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritecu' => array(
    'source' => 'google',
    'family' => 'Playwrite CU',
    'stack' => '"Playwrite CU", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritecuguides' => array(
    'source' => 'google',
    'family' => 'Playwrite CU Guides',
    'stack' => '"Playwrite CU Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritecz' => array(
    'source' => 'google',
    'family' => 'Playwrite CZ',
    'stack' => '"Playwrite CZ", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteczguides' => array(
    'source' => 'google',
    'family' => 'Playwrite CZ Guides',
    'stack' => '"Playwrite CZ Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritedegrund' => array(
    'source' => 'google',
    'family' => 'Playwrite DE Grund',
    'stack' => '"Playwrite DE Grund", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritedegrundguides' => array(
    'source' => 'google',
    'family' => 'Playwrite DE Grund Guides',
    'stack' => '"Playwrite DE Grund Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritedela' => array(
    'source' => 'google',
    'family' => 'Playwrite DE LA',
    'stack' => '"Playwrite DE LA", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritedelaguides' => array(
    'source' => 'google',
    'family' => 'Playwrite DE LA Guides',
    'stack' => '"Playwrite DE LA Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritedesas' => array(
    'source' => 'google',
    'family' => 'Playwrite DE SAS',
    'stack' => '"Playwrite DE SAS", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritedesasguides' => array(
    'source' => 'google',
    'family' => 'Playwrite DE SAS Guides',
    'stack' => '"Playwrite DE SAS Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritedeva' => array(
    'source' => 'google',
    'family' => 'Playwrite DE VA',
    'stack' => '"Playwrite DE VA", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritedevaguides' => array(
    'source' => 'google',
    'family' => 'Playwrite DE VA Guides',
    'stack' => '"Playwrite DE VA Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritedkloopet' => array(
    'source' => 'google',
    'family' => 'Playwrite DK Loopet',
    'stack' => '"Playwrite DK Loopet", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritedkloopetguides' => array(
    'source' => 'google',
    'family' => 'Playwrite DK Loopet Guides',
    'stack' => '"Playwrite DK Loopet Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritedkuloopet' => array(
    'source' => 'google',
    'family' => 'Playwrite DK Uloopet',
    'stack' => '"Playwrite DK Uloopet", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritedkuloopetguides' => array(
    'source' => 'google',
    'family' => 'Playwrite DK Uloopet Guides',
    'stack' => '"Playwrite DK Uloopet Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritees' => array(
    'source' => 'google',
    'family' => 'Playwrite ES',
    'stack' => '"Playwrite ES", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteesdeco' => array(
    'source' => 'google',
    'family' => 'Playwrite ES Deco',
    'stack' => '"Playwrite ES Deco", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteesdecoguides' => array(
    'source' => 'google',
    'family' => 'Playwrite ES Deco Guides',
    'stack' => '"Playwrite ES Deco Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteesguides' => array(
    'source' => 'google',
    'family' => 'Playwrite ES Guides',
    'stack' => '"Playwrite ES Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritefrmoderne' => array(
    'source' => 'google',
    'family' => 'Playwrite FR Moderne',
    'stack' => '"Playwrite FR Moderne", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritefrmoderneguides' => array(
    'source' => 'google',
    'family' => 'Playwrite FR Moderne Guides',
    'stack' => '"Playwrite FR Moderne Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritefrtrad' => array(
    'source' => 'google',
    'family' => 'Playwrite FR Trad',
    'stack' => '"Playwrite FR Trad", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritefrtradguides' => array(
    'source' => 'google',
    'family' => 'Playwrite FR Trad Guides',
    'stack' => '"Playwrite FR Trad Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritegbj' => array(
    'source' => 'google',
    'family' => 'Playwrite GB J',
    'stack' => '"Playwrite GB J", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '100i',
      '200i',
      '300i',
      '400i'
    )
  ),
  'playwritegbjguides' => array(
    'source' => 'google',
    'family' => 'Playwrite GB J Guides',
    'stack' => '"Playwrite GB J Guides", handwriting',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'playwritegbs' => array(
    'source' => 'google',
    'family' => 'Playwrite GB S',
    'stack' => '"Playwrite GB S", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '100i',
      '200i',
      '300i',
      '400i'
    )
  ),
  'playwritegbsguides' => array(
    'source' => 'google',
    'family' => 'Playwrite GB S Guides',
    'stack' => '"Playwrite GB S Guides", handwriting',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'playwritehr' => array(
    'source' => 'google',
    'family' => 'Playwrite HR',
    'stack' => '"Playwrite HR", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritehrguides' => array(
    'source' => 'google',
    'family' => 'Playwrite HR Guides',
    'stack' => '"Playwrite HR Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritehrlijeva' => array(
    'source' => 'google',
    'family' => 'Playwrite HR Lijeva',
    'stack' => '"Playwrite HR Lijeva", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritehrlijevaguides' => array(
    'source' => 'google',
    'family' => 'Playwrite HR Lijeva Guides',
    'stack' => '"Playwrite HR Lijeva Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritehu' => array(
    'source' => 'google',
    'family' => 'Playwrite HU',
    'stack' => '"Playwrite HU", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritehuguides' => array(
    'source' => 'google',
    'family' => 'Playwrite HU Guides',
    'stack' => '"Playwrite HU Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteid' => array(
    'source' => 'google',
    'family' => 'Playwrite ID',
    'stack' => '"Playwrite ID", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteidguides' => array(
    'source' => 'google',
    'family' => 'Playwrite ID Guides',
    'stack' => '"Playwrite ID Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteie' => array(
    'source' => 'google',
    'family' => 'Playwrite IE',
    'stack' => '"Playwrite IE", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteieguides' => array(
    'source' => 'google',
    'family' => 'Playwrite IE Guides',
    'stack' => '"Playwrite IE Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritein' => array(
    'source' => 'google',
    'family' => 'Playwrite IN',
    'stack' => '"Playwrite IN", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteinguides' => array(
    'source' => 'google',
    'family' => 'Playwrite IN Guides',
    'stack' => '"Playwrite IN Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteis' => array(
    'source' => 'google',
    'family' => 'Playwrite IS',
    'stack' => '"Playwrite IS", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteisguides' => array(
    'source' => 'google',
    'family' => 'Playwrite IS Guides',
    'stack' => '"Playwrite IS Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteitmoderna' => array(
    'source' => 'google',
    'family' => 'Playwrite IT Moderna',
    'stack' => '"Playwrite IT Moderna", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteitmodernaguides' => array(
    'source' => 'google',
    'family' => 'Playwrite IT Moderna Guides',
    'stack' => '"Playwrite IT Moderna Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteittrad' => array(
    'source' => 'google',
    'family' => 'Playwrite IT Trad',
    'stack' => '"Playwrite IT Trad", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteittradguides' => array(
    'source' => 'google',
    'family' => 'Playwrite IT Trad Guides',
    'stack' => '"Playwrite IT Trad Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritemx' => array(
    'source' => 'google',
    'family' => 'Playwrite MX',
    'stack' => '"Playwrite MX", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritemxguides' => array(
    'source' => 'google',
    'family' => 'Playwrite MX Guides',
    'stack' => '"Playwrite MX Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritengmodern' => array(
    'source' => 'google',
    'family' => 'Playwrite NG Modern',
    'stack' => '"Playwrite NG Modern", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritengmodernguides' => array(
    'source' => 'google',
    'family' => 'Playwrite NG Modern Guides',
    'stack' => '"Playwrite NG Modern Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritenl' => array(
    'source' => 'google',
    'family' => 'Playwrite NL',
    'stack' => '"Playwrite NL", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritenlguides' => array(
    'source' => 'google',
    'family' => 'Playwrite NL Guides',
    'stack' => '"Playwrite NL Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteno' => array(
    'source' => 'google',
    'family' => 'Playwrite NO',
    'stack' => '"Playwrite NO", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritenoguides' => array(
    'source' => 'google',
    'family' => 'Playwrite NO Guides',
    'stack' => '"Playwrite NO Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritenz' => array(
    'source' => 'google',
    'family' => 'Playwrite NZ',
    'stack' => '"Playwrite NZ", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritenzguides' => array(
    'source' => 'google',
    'family' => 'Playwrite NZ Guides',
    'stack' => '"Playwrite NZ Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritepe' => array(
    'source' => 'google',
    'family' => 'Playwrite PE',
    'stack' => '"Playwrite PE", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritepeguides' => array(
    'source' => 'google',
    'family' => 'Playwrite PE Guides',
    'stack' => '"Playwrite PE Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritepl' => array(
    'source' => 'google',
    'family' => 'Playwrite PL',
    'stack' => '"Playwrite PL", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteplguides' => array(
    'source' => 'google',
    'family' => 'Playwrite PL Guides',
    'stack' => '"Playwrite PL Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritept' => array(
    'source' => 'google',
    'family' => 'Playwrite PT',
    'stack' => '"Playwrite PT", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteptguides' => array(
    'source' => 'google',
    'family' => 'Playwrite PT Guides',
    'stack' => '"Playwrite PT Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritero' => array(
    'source' => 'google',
    'family' => 'Playwrite RO',
    'stack' => '"Playwrite RO", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteroguides' => array(
    'source' => 'google',
    'family' => 'Playwrite RO Guides',
    'stack' => '"Playwrite RO Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritesk' => array(
    'source' => 'google',
    'family' => 'Playwrite SK',
    'stack' => '"Playwrite SK", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteskguides' => array(
    'source' => 'google',
    'family' => 'Playwrite SK Guides',
    'stack' => '"Playwrite SK Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritetz' => array(
    'source' => 'google',
    'family' => 'Playwrite TZ',
    'stack' => '"Playwrite TZ", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritetzguides' => array(
    'source' => 'google',
    'family' => 'Playwrite TZ Guides',
    'stack' => '"Playwrite TZ Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteusmodern' => array(
    'source' => 'google',
    'family' => 'Playwrite US Modern',
    'stack' => '"Playwrite US Modern", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteusmodernguides' => array(
    'source' => 'google',
    'family' => 'Playwrite US Modern Guides',
    'stack' => '"Playwrite US Modern Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteustrad' => array(
    'source' => 'google',
    'family' => 'Playwrite US Trad',
    'stack' => '"Playwrite US Trad", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwriteustradguides' => array(
    'source' => 'google',
    'family' => 'Playwrite US Trad Guides',
    'stack' => '"Playwrite US Trad Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwritevn' => array(
    'source' => 'google',
    'family' => 'Playwrite VN',
    'stack' => '"Playwrite VN", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritevnguides' => array(
    'source' => 'google',
    'family' => 'Playwrite VN Guides',
    'stack' => '"Playwrite VN Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'playwriteza' => array(
    'source' => 'google',
    'family' => 'Playwrite ZA',
    'stack' => '"Playwrite ZA", handwriting',
    'weights' => array(
      '100',
      '200',
      '300',
      '400'
    )
  ),
  'playwritezaguides' => array(
    'source' => 'google',
    'family' => 'Playwrite ZA Guides',
    'stack' => '"Playwrite ZA Guides", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'plusjakartasans' => array(
    'source' => 'google',
    'family' => 'Plus Jakarta Sans',
    'stack' => '"Plus Jakarta Sans", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'pochaevsk' => array(
    'source' => 'google',
    'family' => 'Pochaevsk',
    'stack' => '"Pochaevsk", display',
    'weights' => array(
      '400'
    )
  ),
  'podkova' => array(
    'source' => 'google',
    'family' => 'Podkova',
    'stack' => '"Podkova", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'poetsenone' => array(
    'source' => 'google',
    'family' => 'Poetsen One',
    'stack' => '"Poetsen One", display',
    'weights' => array(
      '400'
    )
  ),
  'poiretone' => array(
    'source' => 'google',
    'family' => 'Poiret One',
    'stack' => '"Poiret One", display',
    'weights' => array(
      '400'
    )
  ),
  'pollerone' => array(
    'source' => 'google',
    'family' => 'Poller One',
    'stack' => '"Poller One", display',
    'weights' => array(
      '400'
    )
  ),
  'poltawskinowy' => array(
    'source' => 'google',
    'family' => 'Poltawski Nowy',
    'stack' => '"Poltawski Nowy", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'poly' => array(
    'source' => 'google',
    'family' => 'Poly',
    'stack' => '"Poly", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'pompiere' => array(
    'source' => 'google',
    'family' => 'Pompiere',
    'stack' => '"Pompiere", display',
    'weights' => array(
      '400'
    )
  ),
  'ponnala' => array(
    'source' => 'google',
    'family' => 'Ponnala',
    'stack' => '"Ponnala", display',
    'weights' => array(
      '400'
    )
  ),
  'ponomar' => array(
    'source' => 'google',
    'family' => 'Ponomar',
    'stack' => '"Ponomar", display',
    'weights' => array(
      '400'
    )
  ),
  'pontanosans' => array(
    'source' => 'google',
    'family' => 'Pontano Sans',
    'stack' => '"Pontano Sans", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'poorstory' => array(
    'source' => 'google',
    'family' => 'Poor Story',
    'stack' => '"Poor Story", display',
    'weights' => array(
      '400'
    )
  ),
  'poppins' => array(
    'source' => 'google',
    'family' => 'Poppins',
    'stack' => '"Poppins", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'portlligatsans' => array(
    'source' => 'google',
    'family' => 'Port Lligat Sans',
    'stack' => '"Port Lligat Sans", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'portlligatslab' => array(
    'source' => 'google',
    'family' => 'Port Lligat Slab',
    'stack' => '"Port Lligat Slab", serif',
    'weights' => array(
      '400'
    )
  ),
  'pottaone' => array(
    'source' => 'google',
    'family' => 'Potta One',
    'stack' => '"Potta One", display',
    'weights' => array(
      '400'
    )
  ),
  'pragatinarrow' => array(
    'source' => 'google',
    'family' => 'Pragati Narrow',
    'stack' => '"Pragati Narrow", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'praise' => array(
    'source' => 'google',
    'family' => 'Praise',
    'stack' => '"Praise", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'prata' => array(
    'source' => 'google',
    'family' => 'Prata',
    'stack' => '"Prata", serif',
    'weights' => array(
      '400'
    )
  ),
  'preahvihear' => array(
    'source' => 'google',
    'family' => 'Preahvihear',
    'stack' => '"Preahvihear", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'pressstart2p' => array(
    'source' => 'google',
    'family' => 'Press Start 2P',
    'stack' => '"Press Start 2P", display',
    'weights' => array(
      '400'
    )
  ),
  'pridi' => array(
    'source' => 'google',
    'family' => 'Pridi',
    'stack' => '"Pridi", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'princesssofia' => array(
    'source' => 'google',
    'family' => 'Princess Sofia',
    'stack' => '"Princess Sofia", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'prociono' => array(
    'source' => 'google',
    'family' => 'Prociono',
    'stack' => '"Prociono", serif',
    'weights' => array(
      '400'
    )
  ),
  'prompt' => array(
    'source' => 'google',
    'family' => 'Prompt',
    'stack' => '"Prompt", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'prostoone' => array(
    'source' => 'google',
    'family' => 'Prosto One',
    'stack' => '"Prosto One", display',
    'weights' => array(
      '400'
    )
  ),
  'protestguerrilla' => array(
    'source' => 'google',
    'family' => 'Protest Guerrilla',
    'stack' => '"Protest Guerrilla", display',
    'weights' => array(
      '400'
    )
  ),
  'protestrevolution' => array(
    'source' => 'google',
    'family' => 'Protest Revolution',
    'stack' => '"Protest Revolution", display',
    'weights' => array(
      '400'
    )
  ),
  'protestriot' => array(
    'source' => 'google',
    'family' => 'Protest Riot',
    'stack' => '"Protest Riot", display',
    'weights' => array(
      '400'
    )
  ),
  'proteststrike' => array(
    'source' => 'google',
    'family' => 'Protest Strike',
    'stack' => '"Protest Strike", display',
    'weights' => array(
      '400'
    )
  ),
  'prozalibre' => array(
    'source' => 'google',
    'family' => 'Proza Libre',
    'stack' => '"Proza Libre", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i'
    )
  ),
  'publicsans' => array(
    'source' => 'google',
    'family' => 'Public Sans',
    'stack' => '"Public Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'puppiesplay' => array(
    'source' => 'google',
    'family' => 'Puppies Play',
    'stack' => '"Puppies Play", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'puritan' => array(
    'source' => 'google',
    'family' => 'Puritan',
    'stack' => '"Puritan", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'purplepurse' => array(
    'source' => 'google',
    'family' => 'Purple Purse',
    'stack' => '"Purple Purse", display',
    'weights' => array(
      '400'
    )
  ),
  'qahiri' => array(
    'source' => 'google',
    'family' => 'Qahiri',
    'stack' => '"Qahiri", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'quando' => array(
    'source' => 'google',
    'family' => 'Quando',
    'stack' => '"Quando", serif',
    'weights' => array(
      '400'
    )
  ),
  'quantico' => array(
    'source' => 'google',
    'family' => 'Quantico',
    'stack' => '"Quantico", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'quattrocento' => array(
    'source' => 'google',
    'family' => 'Quattrocento',
    'stack' => '"Quattrocento", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'quattrocentosans' => array(
    'source' => 'google',
    'family' => 'Quattrocento Sans',
    'stack' => '"Quattrocento Sans", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'questrial' => array(
    'source' => 'google',
    'family' => 'Questrial',
    'stack' => '"Questrial", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'quicksand' => array(
    'source' => 'google',
    'family' => 'Quicksand',
    'stack' => '"Quicksand", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'quintessential' => array(
    'source' => 'google',
    'family' => 'Quintessential',
    'stack' => '"Quintessential", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'qwigley' => array(
    'source' => 'google',
    'family' => 'Qwigley',
    'stack' => '"Qwigley", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'qwitchergrypen' => array(
    'source' => 'google',
    'family' => 'Qwitcher Grypen',
    'stack' => '"Qwitcher Grypen", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'rem' => array(
    'source' => 'google',
    'family' => 'REM',
    'stack' => '"REM", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'racingsansone' => array(
    'source' => 'google',
    'family' => 'Racing Sans One',
    'stack' => '"Racing Sans One", display',
    'weights' => array(
      '400'
    )
  ),
  'radiocanada' => array(
    'source' => 'google',
    'family' => 'Radio Canada',
    'stack' => '"Radio Canada", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'radiocanadabig' => array(
    'source' => 'google',
    'family' => 'Radio Canada Big',
    'stack' => '"Radio Canada Big", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'radley' => array(
    'source' => 'google',
    'family' => 'Radley',
    'stack' => '"Radley", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'rajdhani' => array(
    'source' => 'google',
    'family' => 'Rajdhani',
    'stack' => '"Rajdhani", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'rakkas' => array(
    'source' => 'google',
    'family' => 'Rakkas',
    'stack' => '"Rakkas", display',
    'weights' => array(
      '400'
    )
  ),
  'raleway' => array(
    'source' => 'google',
    'family' => 'Raleway',
    'stack' => '"Raleway", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'ralewaydots' => array(
    'source' => 'google',
    'family' => 'Raleway Dots',
    'stack' => '"Raleway Dots", display',
    'weights' => array(
      '400'
    )
  ),
  'ramabhadra' => array(
    'source' => 'google',
    'family' => 'Ramabhadra',
    'stack' => '"Ramabhadra", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'ramaraja' => array(
    'source' => 'google',
    'family' => 'Ramaraja',
    'stack' => '"Ramaraja", serif',
    'weights' => array(
      '400'
    )
  ),
  'rambla' => array(
    'source' => 'google',
    'family' => 'Rambla',
    'stack' => '"Rambla", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'rammettoone' => array(
    'source' => 'google',
    'family' => 'Rammetto One',
    'stack' => '"Rammetto One", display',
    'weights' => array(
      '400'
    )
  ),
  'rampartone' => array(
    'source' => 'google',
    'family' => 'Rampart One',
    'stack' => '"Rampart One", display',
    'weights' => array(
      '400'
    )
  ),
  'ranchers' => array(
    'source' => 'google',
    'family' => 'Ranchers',
    'stack' => '"Ranchers", display',
    'weights' => array(
      '400'
    )
  ),
  'rancho' => array(
    'source' => 'google',
    'family' => 'Rancho',
    'stack' => '"Rancho", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ranga' => array(
    'source' => 'google',
    'family' => 'Ranga',
    'stack' => '"Ranga", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'rasa' => array(
    'source' => 'google',
    'family' => 'Rasa',
    'stack' => '"Rasa", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'rationale' => array(
    'source' => 'google',
    'family' => 'Rationale',
    'stack' => '"Rationale", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'raviprakash' => array(
    'source' => 'google',
    'family' => 'Ravi Prakash',
    'stack' => '"Ravi Prakash", display',
    'weights' => array(
      '400'
    )
  ),
  'readexpro' => array(
    'source' => 'google',
    'family' => 'Readex Pro',
    'stack' => '"Readex Pro", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'recursive' => array(
    'source' => 'google',
    'family' => 'Recursive',
    'stack' => '"Recursive", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'redhatdisplay' => array(
    'source' => 'google',
    'family' => 'Red Hat Display',
    'stack' => '"Red Hat Display", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'redhatmono' => array(
    'source' => 'google',
    'family' => 'Red Hat Mono',
    'stack' => '"Red Hat Mono", monospace',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'redhattext' => array(
    'source' => 'google',
    'family' => 'Red Hat Text',
    'stack' => '"Red Hat Text", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'redrose' => array(
    'source' => 'google',
    'family' => 'Red Rose',
    'stack' => '"Red Rose", display',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'redacted' => array(
    'source' => 'google',
    'family' => 'Redacted',
    'stack' => '"Redacted", display',
    'weights' => array(
      '400'
    )
  ),
  'redactedscript' => array(
    'source' => 'google',
    'family' => 'Redacted Script',
    'stack' => '"Redacted Script", display',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'redditmono' => array(
    'source' => 'google',
    'family' => 'Reddit Mono',
    'stack' => '"Reddit Mono", monospace',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'redditsans' => array(
    'source' => 'google',
    'family' => 'Reddit Sans',
    'stack' => '"Reddit Sans", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'redditsanscondensed' => array(
    'source' => 'google',
    'family' => 'Reddit Sans Condensed',
    'stack' => '"Reddit Sans Condensed", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'redressed' => array(
    'source' => 'google',
    'family' => 'Redressed',
    'stack' => '"Redressed", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'reemkufi' => array(
    'source' => 'google',
    'family' => 'Reem Kufi',
    'stack' => '"Reem Kufi", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'reemkufifun' => array(
    'source' => 'google',
    'family' => 'Reem Kufi Fun',
    'stack' => '"Reem Kufi Fun", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'reemkufiink' => array(
    'source' => 'google',
    'family' => 'Reem Kufi Ink',
    'stack' => '"Reem Kufi Ink", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'reeniebeanie' => array(
    'source' => 'google',
    'family' => 'Reenie Beanie',
    'stack' => '"Reenie Beanie", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'reggaeone' => array(
    'source' => 'google',
    'family' => 'Reggae One',
    'stack' => '"Reggae One", display',
    'weights' => array(
      '400'
    )
  ),
  'rethinksans' => array(
    'source' => 'google',
    'family' => 'Rethink Sans',
    'stack' => '"Rethink Sans", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'revalia' => array(
    'source' => 'google',
    'family' => 'Revalia',
    'stack' => '"Revalia", display',
    'weights' => array(
      '400'
    )
  ),
  'rhodiumlibre' => array(
    'source' => 'google',
    'family' => 'Rhodium Libre',
    'stack' => '"Rhodium Libre", serif',
    'weights' => array(
      '400'
    )
  ),
  'ribeye' => array(
    'source' => 'google',
    'family' => 'Ribeye',
    'stack' => '"Ribeye", display',
    'weights' => array(
      '400'
    )
  ),
  'ribeyemarrow' => array(
    'source' => 'google',
    'family' => 'Ribeye Marrow',
    'stack' => '"Ribeye Marrow", display',
    'weights' => array(
      '400'
    )
  ),
  'righteous' => array(
    'source' => 'google',
    'family' => 'Righteous',
    'stack' => '"Righteous", display',
    'weights' => array(
      '400'
    )
  ),
  'risque' => array(
    'source' => 'google',
    'family' => 'Risque',
    'stack' => '"Risque", display',
    'weights' => array(
      '400'
    )
  ),
  'roadrage' => array(
    'source' => 'google',
    'family' => 'Road Rage',
    'stack' => '"Road Rage", display',
    'weights' => array(
      '400'
    )
  ),
  'roboto' => array(
    'source' => 'google',
    'family' => 'Roboto',
    'stack' => '"Roboto", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'robotocondensed' => array(
    'source' => 'google',
    'family' => 'Roboto Condensed',
    'stack' => '"Roboto Condensed", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'robotoflex' => array(
    'source' => 'google',
    'family' => 'Roboto Flex',
    'stack' => '"Roboto Flex", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'robotomono' => array(
    'source' => 'google',
    'family' => 'Roboto Mono',
    'stack' => '"Roboto Mono", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'robotoserif' => array(
    'source' => 'google',
    'family' => 'Roboto Serif',
    'stack' => '"Roboto Serif", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'robotoslab' => array(
    'source' => 'google',
    'family' => 'Roboto Slab',
    'stack' => '"Roboto Slab", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'rochester' => array(
    'source' => 'google',
    'family' => 'Rochester',
    'stack' => '"Rochester", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'rock3d' => array(
    'source' => 'google',
    'family' => 'Rock 3D',
    'stack' => '"Rock 3D", display',
    'weights' => array(
      '400'
    )
  ),
  'rocksalt' => array(
    'source' => 'google',
    'family' => 'Rock Salt',
    'stack' => '"Rock Salt", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'rocknrollone' => array(
    'source' => 'google',
    'family' => 'RocknRoll One',
    'stack' => '"RocknRoll One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'rokkitt' => array(
    'source' => 'google',
    'family' => 'Rokkitt',
    'stack' => '"Rokkitt", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'romanesco' => array(
    'source' => 'google',
    'family' => 'Romanesco',
    'stack' => '"Romanesco", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ropasans' => array(
    'source' => 'google',
    'family' => 'Ropa Sans',
    'stack' => '"Ropa Sans", sans-serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'rosario' => array(
    'source' => 'google',
    'family' => 'Rosario',
    'stack' => '"Rosario", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'rosarivo' => array(
    'source' => 'google',
    'family' => 'Rosarivo',
    'stack' => '"Rosarivo", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'rougescript' => array(
    'source' => 'google',
    'family' => 'Rouge Script',
    'stack' => '"Rouge Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'rowdies' => array(
    'source' => 'google',
    'family' => 'Rowdies',
    'stack' => '"Rowdies", display',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'rozhaone' => array(
    'source' => 'google',
    'family' => 'Rozha One',
    'stack' => '"Rozha One", serif',
    'weights' => array(
      '400'
    )
  ),
  'rubik' => array(
    'source' => 'google',
    'family' => 'Rubik',
    'stack' => '"Rubik", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'rubik80sfade' => array(
    'source' => 'google',
    'family' => 'Rubik 80s Fade',
    'stack' => '"Rubik 80s Fade", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikbeastly' => array(
    'source' => 'google',
    'family' => 'Rubik Beastly',
    'stack' => '"Rubik Beastly", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikbrokenfax' => array(
    'source' => 'google',
    'family' => 'Rubik Broken Fax',
    'stack' => '"Rubik Broken Fax", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikbubbles' => array(
    'source' => 'google',
    'family' => 'Rubik Bubbles',
    'stack' => '"Rubik Bubbles", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikburned' => array(
    'source' => 'google',
    'family' => 'Rubik Burned',
    'stack' => '"Rubik Burned", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikdirt' => array(
    'source' => 'google',
    'family' => 'Rubik Dirt',
    'stack' => '"Rubik Dirt", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikdistressed' => array(
    'source' => 'google',
    'family' => 'Rubik Distressed',
    'stack' => '"Rubik Distressed", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikdoodleshadow' => array(
    'source' => 'google',
    'family' => 'Rubik Doodle Shadow',
    'stack' => '"Rubik Doodle Shadow", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikdoodletriangles' => array(
    'source' => 'google',
    'family' => 'Rubik Doodle Triangles',
    'stack' => '"Rubik Doodle Triangles", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikgemstones' => array(
    'source' => 'google',
    'family' => 'Rubik Gemstones',
    'stack' => '"Rubik Gemstones", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikglitch' => array(
    'source' => 'google',
    'family' => 'Rubik Glitch',
    'stack' => '"Rubik Glitch", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikglitchpop' => array(
    'source' => 'google',
    'family' => 'Rubik Glitch Pop',
    'stack' => '"Rubik Glitch Pop", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikiso' => array(
    'source' => 'google',
    'family' => 'Rubik Iso',
    'stack' => '"Rubik Iso", display',
    'weights' => array(
      '400'
    )
  ),
  'rubiklines' => array(
    'source' => 'google',
    'family' => 'Rubik Lines',
    'stack' => '"Rubik Lines", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikmaps' => array(
    'source' => 'google',
    'family' => 'Rubik Maps',
    'stack' => '"Rubik Maps", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikmarkerhatch' => array(
    'source' => 'google',
    'family' => 'Rubik Marker Hatch',
    'stack' => '"Rubik Marker Hatch", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikmaze' => array(
    'source' => 'google',
    'family' => 'Rubik Maze',
    'stack' => '"Rubik Maze", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikmicrobe' => array(
    'source' => 'google',
    'family' => 'Rubik Microbe',
    'stack' => '"Rubik Microbe", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikmonoone' => array(
    'source' => 'google',
    'family' => 'Rubik Mono One',
    'stack' => '"Rubik Mono One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'rubikmoonrocks' => array(
    'source' => 'google',
    'family' => 'Rubik Moonrocks',
    'stack' => '"Rubik Moonrocks", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikpixels' => array(
    'source' => 'google',
    'family' => 'Rubik Pixels',
    'stack' => '"Rubik Pixels", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikpuddles' => array(
    'source' => 'google',
    'family' => 'Rubik Puddles',
    'stack' => '"Rubik Puddles", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikscribble' => array(
    'source' => 'google',
    'family' => 'Rubik Scribble',
    'stack' => '"Rubik Scribble", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikspraypaint' => array(
    'source' => 'google',
    'family' => 'Rubik Spray Paint',
    'stack' => '"Rubik Spray Paint", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikstorm' => array(
    'source' => 'google',
    'family' => 'Rubik Storm',
    'stack' => '"Rubik Storm", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikvinyl' => array(
    'source' => 'google',
    'family' => 'Rubik Vinyl',
    'stack' => '"Rubik Vinyl", display',
    'weights' => array(
      '400'
    )
  ),
  'rubikwetpaint' => array(
    'source' => 'google',
    'family' => 'Rubik Wet Paint',
    'stack' => '"Rubik Wet Paint", display',
    'weights' => array(
      '400'
    )
  ),
  'ruda' => array(
    'source' => 'google',
    'family' => 'Ruda',
    'stack' => '"Ruda", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'rufina' => array(
    'source' => 'google',
    'family' => 'Rufina',
    'stack' => '"Rufina", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'rugeboogie' => array(
    'source' => 'google',
    'family' => 'Ruge Boogie',
    'stack' => '"Ruge Boogie", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ruluko' => array(
    'source' => 'google',
    'family' => 'Ruluko',
    'stack' => '"Ruluko", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'rumraisin' => array(
    'source' => 'google',
    'family' => 'Rum Raisin',
    'stack' => '"Rum Raisin", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'ruslandisplay' => array(
    'source' => 'google',
    'family' => 'Ruslan Display',
    'stack' => '"Ruslan Display", display',
    'weights' => array(
      '400'
    )
  ),
  'russoone' => array(
    'source' => 'google',
    'family' => 'Russo One',
    'stack' => '"Russo One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'ruthie' => array(
    'source' => 'google',
    'family' => 'Ruthie',
    'stack' => '"Ruthie", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ruwudu' => array(
    'source' => 'google',
    'family' => 'Ruwudu',
    'stack' => '"Ruwudu", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'rye' => array(
    'source' => 'google',
    'family' => 'Rye',
    'stack' => '"Rye", display',
    'weights' => array(
      '400'
    )
  ),
  'stixtwotext' => array(
    'source' => 'google',
    'family' => 'STIX Two Text',
    'stack' => '"STIX Two Text", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'suse' => array(
    'source' => 'google',
    'family' => 'SUSE',
    'stack' => '"SUSE", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'sacramento' => array(
    'source' => 'google',
    'family' => 'Sacramento',
    'stack' => '"Sacramento", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'sahitya' => array(
    'source' => 'google',
    'family' => 'Sahitya',
    'stack' => '"Sahitya", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'sail' => array(
    'source' => 'google',
    'family' => 'Sail',
    'stack' => '"Sail", display',
    'weights' => array(
      '400'
    )
  ),
  'saira' => array(
    'source' => 'google',
    'family' => 'Saira',
    'stack' => '"Saira", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'sairacondensed' => array(
    'source' => 'google',
    'family' => 'Saira Condensed',
    'stack' => '"Saira Condensed", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'sairaextracondensed' => array(
    'source' => 'google',
    'family' => 'Saira Extra Condensed',
    'stack' => '"Saira Extra Condensed", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'sairasemicondensed' => array(
    'source' => 'google',
    'family' => 'Saira Semi Condensed',
    'stack' => '"Saira Semi Condensed", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'sairastencilone' => array(
    'source' => 'google',
    'family' => 'Saira Stencil One',
    'stack' => '"Saira Stencil One", display',
    'weights' => array(
      '400'
    )
  ),
  'salsa' => array(
    'source' => 'google',
    'family' => 'Salsa',
    'stack' => '"Salsa", display',
    'weights' => array(
      '400'
    )
  ),
  'sanchez' => array(
    'source' => 'google',
    'family' => 'Sanchez',
    'stack' => '"Sanchez", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'sancreek' => array(
    'source' => 'google',
    'family' => 'Sancreek',
    'stack' => '"Sancreek", display',
    'weights' => array(
      '400'
    )
  ),
  'sankofadisplay' => array(
    'source' => 'google',
    'family' => 'Sankofa Display',
    'stack' => '"Sankofa Display", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'sansation' => array(
    'source' => 'google',
    'family' => 'Sansation',
    'stack' => '"Sansation", sans-serif',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'sansita' => array(
    'source' => 'google',
    'family' => 'Sansita',
    'stack' => '"Sansita", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'sansitaswashed' => array(
    'source' => 'google',
    'family' => 'Sansita Swashed',
    'stack' => '"Sansita Swashed", display',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'sarabun' => array(
    'source' => 'google',
    'family' => 'Sarabun',
    'stack' => '"Sarabun", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i'
    )
  ),
  'sarala' => array(
    'source' => 'google',
    'family' => 'Sarala',
    'stack' => '"Sarala", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'sarina' => array(
    'source' => 'google',
    'family' => 'Sarina',
    'stack' => '"Sarina", display',
    'weights' => array(
      '400'
    )
  ),
  'sarpanch' => array(
    'source' => 'google',
    'family' => 'Sarpanch',
    'stack' => '"Sarpanch", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'sassyfrass' => array(
    'source' => 'google',
    'family' => 'Sassy Frass',
    'stack' => '"Sassy Frass", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'satisfy' => array(
    'source' => 'google',
    'family' => 'Satisfy',
    'stack' => '"Satisfy", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'savate' => array(
    'source' => 'google',
    'family' => 'Savate',
    'stack' => '"Savate", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'sawarabigothic' => array(
    'source' => 'google',
    'family' => 'Sawarabi Gothic',
    'stack' => '"Sawarabi Gothic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'sawarabimincho' => array(
    'source' => 'google',
    'family' => 'Sawarabi Mincho',
    'stack' => '"Sawarabi Mincho", serif',
    'weights' => array(
      '400'
    )
  ),
  'scada' => array(
    'source' => 'google',
    'family' => 'Scada',
    'stack' => '"Scada", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'scheherazadenew' => array(
    'source' => 'google',
    'family' => 'Scheherazade New',
    'stack' => '"Scheherazade New", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'schibstedgrotesk' => array(
    'source' => 'google',
    'family' => 'Schibsted Grotesk',
    'stack' => '"Schibsted Grotesk", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'schoolbell' => array(
    'source' => 'google',
    'family' => 'Schoolbell',
    'stack' => '"Schoolbell", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'scopeone' => array(
    'source' => 'google',
    'family' => 'Scope One',
    'stack' => '"Scope One", serif',
    'weights' => array(
      '400'
    )
  ),
  'seaweedscript' => array(
    'source' => 'google',
    'family' => 'Seaweed Script',
    'stack' => '"Seaweed Script", display',
    'weights' => array(
      '400'
    )
  ),
  'secularone' => array(
    'source' => 'google',
    'family' => 'Secular One',
    'stack' => '"Secular One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'sedan' => array(
    'source' => 'google',
    'family' => 'Sedan',
    'stack' => '"Sedan", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'sedansc' => array(
    'source' => 'google',
    'family' => 'Sedan SC',
    'stack' => '"Sedan SC", serif',
    'weights' => array(
      '400'
    )
  ),
  'sedgwickave' => array(
    'source' => 'google',
    'family' => 'Sedgwick Ave',
    'stack' => '"Sedgwick Ave", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'sedgwickavedisplay' => array(
    'source' => 'google',
    'family' => 'Sedgwick Ave Display',
    'stack' => '"Sedgwick Ave Display", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'sen' => array(
    'source' => 'google',
    'family' => 'Sen',
    'stack' => '"Sen", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'sendflowers' => array(
    'source' => 'google',
    'family' => 'Send Flowers',
    'stack' => '"Send Flowers", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'sevillana' => array(
    'source' => 'google',
    'family' => 'Sevillana',
    'stack' => '"Sevillana", display',
    'weights' => array(
      '400'
    )
  ),
  'seymourone' => array(
    'source' => 'google',
    'family' => 'Seymour One',
    'stack' => '"Seymour One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'shadowsintolight' => array(
    'source' => 'google',
    'family' => 'Shadows Into Light',
    'stack' => '"Shadows Into Light", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'shadowsintolighttwo' => array(
    'source' => 'google',
    'family' => 'Shadows Into Light Two',
    'stack' => '"Shadows Into Light Two", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'shafarik' => array(
    'source' => 'google',
    'family' => 'Shafarik',
    'stack' => '"Shafarik", display',
    'weights' => array(
      '400'
    )
  ),
  'shalimar' => array(
    'source' => 'google',
    'family' => 'Shalimar',
    'stack' => '"Shalimar", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'shantellsans' => array(
    'source' => 'google',
    'family' => 'Shantell Sans',
    'stack' => '"Shantell Sans", display',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'shanti' => array(
    'source' => 'google',
    'family' => 'Shanti',
    'stack' => '"Shanti", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'share' => array(
    'source' => 'google',
    'family' => 'Share',
    'stack' => '"Share", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'sharetech' => array(
    'source' => 'google',
    'family' => 'Share Tech',
    'stack' => '"Share Tech", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'sharetechmono' => array(
    'source' => 'google',
    'family' => 'Share Tech Mono',
    'stack' => '"Share Tech Mono", monospace',
    'weights' => array(
      '400'
    )
  ),
  'shipporiantique' => array(
    'source' => 'google',
    'family' => 'Shippori Antique',
    'stack' => '"Shippori Antique", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'shipporiantiqueb1' => array(
    'source' => 'google',
    'family' => 'Shippori Antique B1',
    'stack' => '"Shippori Antique B1", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'shipporimincho' => array(
    'source' => 'google',
    'family' => 'Shippori Mincho',
    'stack' => '"Shippori Mincho", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'shipporiminchob1' => array(
    'source' => 'google',
    'family' => 'Shippori Mincho B1',
    'stack' => '"Shippori Mincho B1", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'shizuru' => array(
    'source' => 'google',
    'family' => 'Shizuru',
    'stack' => '"Shizuru", display',
    'weights' => array(
      '400'
    )
  ),
  'shojumaru' => array(
    'source' => 'google',
    'family' => 'Shojumaru',
    'stack' => '"Shojumaru", display',
    'weights' => array(
      '400'
    )
  ),
  'shortstack' => array(
    'source' => 'google',
    'family' => 'Short Stack',
    'stack' => '"Short Stack", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'shrikhand' => array(
    'source' => 'google',
    'family' => 'Shrikhand',
    'stack' => '"Shrikhand", display',
    'weights' => array(
      '400'
    )
  ),
  'siemreap' => array(
    'source' => 'google',
    'family' => 'Siemreap',
    'stack' => '"Siemreap", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'sigmar' => array(
    'source' => 'google',
    'family' => 'Sigmar',
    'stack' => '"Sigmar", display',
    'weights' => array(
      '400'
    )
  ),
  'sigmarone' => array(
    'source' => 'google',
    'family' => 'Sigmar One',
    'stack' => '"Sigmar One", display',
    'weights' => array(
      '400'
    )
  ),
  'signika' => array(
    'source' => 'google',
    'family' => 'Signika',
    'stack' => '"Signika", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'signikanegative' => array(
    'source' => 'google',
    'family' => 'Signika Negative',
    'stack' => '"Signika Negative", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'silkscreen' => array(
    'source' => 'google',
    'family' => 'Silkscreen',
    'stack' => '"Silkscreen", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'simonetta' => array(
    'source' => 'google',
    'family' => 'Simonetta',
    'stack' => '"Simonetta", display',
    'weights' => array(
      '400',
      '400i',
      '900',
      '900i'
    )
  ),
  'singleday' => array(
    'source' => 'google',
    'family' => 'Single Day',
    'stack' => '"Single Day", display',
    'weights' => array(
      '400'
    )
  ),
  'sintony' => array(
    'source' => 'google',
    'family' => 'Sintony',
    'stack' => '"Sintony", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'sirinstencil' => array(
    'source' => 'google',
    'family' => 'Sirin Stencil',
    'stack' => '"Sirin Stencil", display',
    'weights' => array(
      '400'
    )
  ),
  'sixcaps' => array(
    'source' => 'google',
    'family' => 'Six Caps',
    'stack' => '"Six Caps", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'sixtyfour' => array(
    'source' => 'google',
    'family' => 'Sixtyfour',
    'stack' => '"Sixtyfour", monospace',
    'weights' => array(
      '400'
    )
  ),
  'sixtyfourconvergence' => array(
    'source' => 'google',
    'family' => 'Sixtyfour Convergence',
    'stack' => '"Sixtyfour Convergence", monospace',
    'weights' => array(
      '400'
    )
  ),
  'skranji' => array(
    'source' => 'google',
    'family' => 'Skranji',
    'stack' => '"Skranji", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'slabo13px' => array(
    'source' => 'google',
    'family' => 'Slabo 13px',
    'stack' => '"Slabo 13px", serif',
    'weights' => array(
      '400'
    )
  ),
  'slabo27px' => array(
    'source' => 'google',
    'family' => 'Slabo 27px',
    'stack' => '"Slabo 27px", serif',
    'weights' => array(
      '400'
    )
  ),
  'slackey' => array(
    'source' => 'google',
    'family' => 'Slackey',
    'stack' => '"Slackey", display',
    'weights' => array(
      '400'
    )
  ),
  'slacksideone' => array(
    'source' => 'google',
    'family' => 'Slackside One',
    'stack' => '"Slackside One", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'smokum' => array(
    'source' => 'google',
    'family' => 'Smokum',
    'stack' => '"Smokum", display',
    'weights' => array(
      '400'
    )
  ),
  'smooch' => array(
    'source' => 'google',
    'family' => 'Smooch',
    'stack' => '"Smooch", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'smoochsans' => array(
    'source' => 'google',
    'family' => 'Smooch Sans',
    'stack' => '"Smooch Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'smythe' => array(
    'source' => 'google',
    'family' => 'Smythe',
    'stack' => '"Smythe", display',
    'weights' => array(
      '400'
    )
  ),
  'sniglet' => array(
    'source' => 'google',
    'family' => 'Sniglet',
    'stack' => '"Sniglet", display',
    'weights' => array(
      '400',
      '800'
    )
  ),
  'snippet' => array(
    'source' => 'google',
    'family' => 'Snippet',
    'stack' => '"Snippet", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'snowburstone' => array(
    'source' => 'google',
    'family' => 'Snowburst One',
    'stack' => '"Snowburst One", display',
    'weights' => array(
      '400'
    )
  ),
  'sofadione' => array(
    'source' => 'google',
    'family' => 'Sofadi One',
    'stack' => '"Sofadi One", display',
    'weights' => array(
      '400'
    )
  ),
  'sofia' => array(
    'source' => 'google',
    'family' => 'Sofia',
    'stack' => '"Sofia", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'sofiasans' => array(
    'source' => 'google',
    'family' => 'Sofia Sans',
    'stack' => '"Sofia Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'sofiasanscondensed' => array(
    'source' => 'google',
    'family' => 'Sofia Sans Condensed',
    'stack' => '"Sofia Sans Condensed", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'sofiasansextracondensed' => array(
    'source' => 'google',
    'family' => 'Sofia Sans Extra Condensed',
    'stack' => '"Sofia Sans Extra Condensed", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'sofiasanssemicondensed' => array(
    'source' => 'google',
    'family' => 'Sofia Sans Semi Condensed',
    'stack' => '"Sofia Sans Semi Condensed", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'solitreo' => array(
    'source' => 'google',
    'family' => 'Solitreo',
    'stack' => '"Solitreo", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'solway' => array(
    'source' => 'google',
    'family' => 'Solway',
    'stack' => '"Solway", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '700',
      '800'
    )
  ),
  'sometypemono' => array(
    'source' => 'google',
    'family' => 'Sometype Mono',
    'stack' => '"Sometype Mono", monospace',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'songmyung' => array(
    'source' => 'google',
    'family' => 'Song Myung',
    'stack' => '"Song Myung", serif',
    'weights' => array(
      '400'
    )
  ),
  'sono' => array(
    'source' => 'google',
    'family' => 'Sono',
    'stack' => '"Sono", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'sonsieone' => array(
    'source' => 'google',
    'family' => 'Sonsie One',
    'stack' => '"Sonsie One", display',
    'weights' => array(
      '400'
    )
  ),
  'sora' => array(
    'source' => 'google',
    'family' => 'Sora',
    'stack' => '"Sora", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'sortsmillgoudy' => array(
    'source' => 'google',
    'family' => 'Sorts Mill Goudy',
    'stack' => '"Sorts Mill Goudy", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'sourgummy' => array(
    'source' => 'google',
    'family' => 'Sour Gummy',
    'stack' => '"Sour Gummy", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'sourcecodepro' => array(
    'source' => 'google',
    'family' => 'Source Code Pro',
    'stack' => '"Source Code Pro", monospace',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'sourcesans3' => array(
    'source' => 'google',
    'family' => 'Source Sans 3',
    'stack' => '"Source Sans 3", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'sourceserif4' => array(
    'source' => 'google',
    'family' => 'Source Serif 4',
    'stack' => '"Source Serif 4", serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'spacegrotesk' => array(
    'source' => 'google',
    'family' => 'Space Grotesk',
    'stack' => '"Space Grotesk", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'spacemono' => array(
    'source' => 'google',
    'family' => 'Space Mono',
    'stack' => '"Space Mono", monospace',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'specialelite' => array(
    'source' => 'google',
    'family' => 'Special Elite',
    'stack' => '"Special Elite", display',
    'weights' => array(
      '400'
    )
  ),
  'specialgothic' => array(
    'source' => 'google',
    'family' => 'Special Gothic',
    'stack' => '"Special Gothic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'specialgothiccondensedone' => array(
    'source' => 'google',
    'family' => 'Special Gothic Condensed One',
    'stack' => '"Special Gothic Condensed One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'specialgothicexpandedone' => array(
    'source' => 'google',
    'family' => 'Special Gothic Expanded One',
    'stack' => '"Special Gothic Expanded One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'spectral' => array(
    'source' => 'google',
    'family' => 'Spectral',
    'stack' => '"Spectral", serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i'
    )
  ),
  'spectralsc' => array(
    'source' => 'google',
    'family' => 'Spectral SC',
    'stack' => '"Spectral SC", serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i'
    )
  ),
  'spicyrice' => array(
    'source' => 'google',
    'family' => 'Spicy Rice',
    'stack' => '"Spicy Rice", display',
    'weights' => array(
      '400'
    )
  ),
  'spinnaker' => array(
    'source' => 'google',
    'family' => 'Spinnaker',
    'stack' => '"Spinnaker", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'spirax' => array(
    'source' => 'google',
    'family' => 'Spirax',
    'stack' => '"Spirax", display',
    'weights' => array(
      '400'
    )
  ),
  'splash' => array(
    'source' => 'google',
    'family' => 'Splash',
    'stack' => '"Splash", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'splinesans' => array(
    'source' => 'google',
    'family' => 'Spline Sans',
    'stack' => '"Spline Sans", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'splinesansmono' => array(
    'source' => 'google',
    'family' => 'Spline Sans Mono',
    'stack' => '"Spline Sans Mono", monospace',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'squadaone' => array(
    'source' => 'google',
    'family' => 'Squada One',
    'stack' => '"Squada One", display',
    'weights' => array(
      '400'
    )
  ),
  'squarepeg' => array(
    'source' => 'google',
    'family' => 'Square Peg',
    'stack' => '"Square Peg", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'sreekrushnadevaraya' => array(
    'source' => 'google',
    'family' => 'Sree Krushnadevaraya',
    'stack' => '"Sree Krushnadevaraya", serif',
    'weights' => array(
      '400'
    )
  ),
  'sriracha' => array(
    'source' => 'google',
    'family' => 'Sriracha',
    'stack' => '"Sriracha", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'srisakdi' => array(
    'source' => 'google',
    'family' => 'Srisakdi',
    'stack' => '"Srisakdi", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'staatliches' => array(
    'source' => 'google',
    'family' => 'Staatliches',
    'stack' => '"Staatliches", display',
    'weights' => array(
      '400'
    )
  ),
  'stalemate' => array(
    'source' => 'google',
    'family' => 'Stalemate',
    'stack' => '"Stalemate", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'stalinistone' => array(
    'source' => 'google',
    'family' => 'Stalinist One',
    'stack' => '"Stalinist One", display',
    'weights' => array(
      '400'
    )
  ),
  'stardosstencil' => array(
    'source' => 'google',
    'family' => 'Stardos Stencil',
    'stack' => '"Stardos Stencil", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'stick' => array(
    'source' => 'google',
    'family' => 'Stick',
    'stack' => '"Stick", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'sticknobills' => array(
    'source' => 'google',
    'family' => 'Stick No Bills',
    'stack' => '"Stick No Bills", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'stintultracondensed' => array(
    'source' => 'google',
    'family' => 'Stint Ultra Condensed',
    'stack' => '"Stint Ultra Condensed", serif',
    'weights' => array(
      '400'
    )
  ),
  'stintultraexpanded' => array(
    'source' => 'google',
    'family' => 'Stint Ultra Expanded',
    'stack' => '"Stint Ultra Expanded", serif',
    'weights' => array(
      '400'
    )
  ),
  'stoke' => array(
    'source' => 'google',
    'family' => 'Stoke',
    'stack' => '"Stoke", serif',
    'weights' => array(
      '300',
      '400'
    )
  ),
  'strait' => array(
    'source' => 'google',
    'family' => 'Strait',
    'stack' => '"Strait", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'stylescript' => array(
    'source' => 'google',
    'family' => 'Style Script',
    'stack' => '"Style Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'stylish' => array(
    'source' => 'google',
    'family' => 'Stylish',
    'stack' => '"Stylish", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'sueellenfrancisco' => array(
    'source' => 'google',
    'family' => 'Sue Ellen Francisco',
    'stack' => '"Sue Ellen Francisco", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'suezone' => array(
    'source' => 'google',
    'family' => 'Suez One',
    'stack' => '"Suez One", serif',
    'weights' => array(
      '400'
    )
  ),
  'sulphurpoint' => array(
    'source' => 'google',
    'family' => 'Sulphur Point',
    'stack' => '"Sulphur Point", sans-serif',
    'weights' => array(
      '300',
      '400',
      '700'
    )
  ),
  'sumana' => array(
    'source' => 'google',
    'family' => 'Sumana',
    'stack' => '"Sumana", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'sunflower' => array(
    'source' => 'google',
    'family' => 'Sunflower',
    'stack' => '"Sunflower", sans-serif',
    'weights' => array(
      '300',
      '500',
      '700'
    )
  ),
  'sunshiney' => array(
    'source' => 'google',
    'family' => 'Sunshiney',
    'stack' => '"Sunshiney", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'supermercadoone' => array(
    'source' => 'google',
    'family' => 'Supermercado One',
    'stack' => '"Supermercado One", display',
    'weights' => array(
      '400'
    )
  ),
  'sura' => array(
    'source' => 'google',
    'family' => 'Sura',
    'stack' => '"Sura", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'suranna' => array(
    'source' => 'google',
    'family' => 'Suranna',
    'stack' => '"Suranna", serif',
    'weights' => array(
      '400'
    )
  ),
  'suravaram' => array(
    'source' => 'google',
    'family' => 'Suravaram',
    'stack' => '"Suravaram", serif',
    'weights' => array(
      '400'
    )
  ),
  'suwannaphum' => array(
    'source' => 'google',
    'family' => 'Suwannaphum',
    'stack' => '"Suwannaphum", serif',
    'weights' => array(
      '100',
      '300',
      '400',
      '700',
      '900'
    )
  ),
  'swankyandmoomoo' => array(
    'source' => 'google',
    'family' => 'Swanky and Moo Moo',
    'stack' => '"Swanky and Moo Moo", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'syncopate' => array(
    'source' => 'google',
    'family' => 'Syncopate',
    'stack' => '"Syncopate", sans-serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'syne' => array(
    'source' => 'google',
    'family' => 'Syne',
    'stack' => '"Syne", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'synemono' => array(
    'source' => 'google',
    'family' => 'Syne Mono',
    'stack' => '"Syne Mono", monospace',
    'weights' => array(
      '400'
    )
  ),
  'synetactile' => array(
    'source' => 'google',
    'family' => 'Syne Tactile',
    'stack' => '"Syne Tactile", display',
    'weights' => array(
      '400'
    )
  ),
  'tacone' => array(
    'source' => 'google',
    'family' => 'Tac One',
    'stack' => '"Tac One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'tagesschrift' => array(
    'source' => 'google',
    'family' => 'Tagesschrift',
    'stack' => '"Tagesschrift", display',
    'weights' => array(
      '400'
    )
  ),
  'taiheritagepro' => array(
    'source' => 'google',
    'family' => 'Tai Heritage Pro',
    'stack' => '"Tai Heritage Pro", serif',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'tajawal' => array(
    'source' => 'google',
    'family' => 'Tajawal',
    'stack' => '"Tajawal", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '700',
      '800',
      '900'
    )
  ),
  'tangerine' => array(
    'source' => 'google',
    'family' => 'Tangerine',
    'stack' => '"Tangerine", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'tapestry' => array(
    'source' => 'google',
    'family' => 'Tapestry',
    'stack' => '"Tapestry", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'taprom' => array(
    'source' => 'google',
    'family' => 'Taprom',
    'stack' => '"Taprom", display',
    'weights' => array(
      '400'
    )
  ),
  'tauri' => array(
    'source' => 'google',
    'family' => 'Tauri',
    'stack' => '"Tauri", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'taviraj' => array(
    'source' => 'google',
    'family' => 'Taviraj',
    'stack' => '"Taviraj", serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'teachers' => array(
    'source' => 'google',
    'family' => 'Teachers',
    'stack' => '"Teachers", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'teko' => array(
    'source' => 'google',
    'family' => 'Teko',
    'stack' => '"Teko", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'tektur' => array(
    'source' => 'google',
    'family' => 'Tektur',
    'stack' => '"Tektur", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'telex' => array(
    'source' => 'google',
    'family' => 'Telex',
    'stack' => '"Telex", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'tenaliramakrishna' => array(
    'source' => 'google',
    'family' => 'Tenali Ramakrishna',
    'stack' => '"Tenali Ramakrishna", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'tenorsans' => array(
    'source' => 'google',
    'family' => 'Tenor Sans',
    'stack' => '"Tenor Sans", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'textmeone' => array(
    'source' => 'google',
    'family' => 'Text Me One',
    'stack' => '"Text Me One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'texturina' => array(
    'source' => 'google',
    'family' => 'Texturina',
    'stack' => '"Texturina", serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'thasadith' => array(
    'source' => 'google',
    'family' => 'Thasadith',
    'stack' => '"Thasadith", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'thegirlnextdoor' => array(
    'source' => 'google',
    'family' => 'The Girl Next Door',
    'stack' => '"The Girl Next Door", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'thenautigal' => array(
    'source' => 'google',
    'family' => 'The Nautigal',
    'stack' => '"The Nautigal", handwriting',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'tienne' => array(
    'source' => 'google',
    'family' => 'Tienne',
    'stack' => '"Tienne", serif',
    'weights' => array(
      '400',
      '700',
      '900'
    )
  ),
  'tillana' => array(
    'source' => 'google',
    'family' => 'Tillana',
    'stack' => '"Tillana", display',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'tiltneon' => array(
    'source' => 'google',
    'family' => 'Tilt Neon',
    'stack' => '"Tilt Neon", display',
    'weights' => array(
      '400'
    )
  ),
  'tiltprism' => array(
    'source' => 'google',
    'family' => 'Tilt Prism',
    'stack' => '"Tilt Prism", display',
    'weights' => array(
      '400'
    )
  ),
  'tiltwarp' => array(
    'source' => 'google',
    'family' => 'Tilt Warp',
    'stack' => '"Tilt Warp", display',
    'weights' => array(
      '400'
    )
  ),
  'timmana' => array(
    'source' => 'google',
    'family' => 'Timmana',
    'stack' => '"Timmana", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'tinos' => array(
    'source' => 'google',
    'family' => 'Tinos',
    'stack' => '"Tinos", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'tiny5' => array(
    'source' => 'google',
    'family' => 'Tiny5',
    'stack' => '"Tiny5", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'tirobangla' => array(
    'source' => 'google',
    'family' => 'Tiro Bangla',
    'stack' => '"Tiro Bangla", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'tirodevanagarihindi' => array(
    'source' => 'google',
    'family' => 'Tiro Devanagari Hindi',
    'stack' => '"Tiro Devanagari Hindi", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'tirodevanagarimarathi' => array(
    'source' => 'google',
    'family' => 'Tiro Devanagari Marathi',
    'stack' => '"Tiro Devanagari Marathi", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'tirodevanagarisanskrit' => array(
    'source' => 'google',
    'family' => 'Tiro Devanagari Sanskrit',
    'stack' => '"Tiro Devanagari Sanskrit", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'tirogurmukhi' => array(
    'source' => 'google',
    'family' => 'Tiro Gurmukhi',
    'stack' => '"Tiro Gurmukhi", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'tirokannada' => array(
    'source' => 'google',
    'family' => 'Tiro Kannada',
    'stack' => '"Tiro Kannada", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'tirotamil' => array(
    'source' => 'google',
    'family' => 'Tiro Tamil',
    'stack' => '"Tiro Tamil", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'tirotelugu' => array(
    'source' => 'google',
    'family' => 'Tiro Telugu',
    'stack' => '"Tiro Telugu", serif',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'titanone' => array(
    'source' => 'google',
    'family' => 'Titan One',
    'stack' => '"Titan One", display',
    'weights' => array(
      '400'
    )
  ),
  'titilliumweb' => array(
    'source' => 'google',
    'family' => 'Titillium Web',
    'stack' => '"Titillium Web", sans-serif',
    'weights' => array(
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '600',
      '600i',
      '700',
      '700i',
      '900'
    )
  ),
  'tomorrow' => array(
    'source' => 'google',
    'family' => 'Tomorrow',
    'stack' => '"Tomorrow", sans-serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'tourney' => array(
    'source' => 'google',
    'family' => 'Tourney',
    'stack' => '"Tourney", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'tradewinds' => array(
    'source' => 'google',
    'family' => 'Trade Winds',
    'stack' => '"Trade Winds", display',
    'weights' => array(
      '400'
    )
  ),
  'trainone' => array(
    'source' => 'google',
    'family' => 'Train One',
    'stack' => '"Train One", display',
    'weights' => array(
      '400'
    )
  ),
  'triodion' => array(
    'source' => 'google',
    'family' => 'Triodion',
    'stack' => '"Triodion", display',
    'weights' => array(
      '400'
    )
  ),
  'trirong' => array(
    'source' => 'google',
    'family' => 'Trirong',
    'stack' => '"Trirong", serif',
    'weights' => array(
      '100',
      '100i',
      '200',
      '200i',
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i',
      '900',
      '900i'
    )
  ),
  'trispace' => array(
    'source' => 'google',
    'family' => 'Trispace',
    'stack' => '"Trispace", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'trocchi' => array(
    'source' => 'google',
    'family' => 'Trocchi',
    'stack' => '"Trocchi", serif',
    'weights' => array(
      '400'
    )
  ),
  'trochut' => array(
    'source' => 'google',
    'family' => 'Trochut',
    'stack' => '"Trochut", display',
    'weights' => array(
      '400',
      '400i',
      '700'
    )
  ),
  'truculenta' => array(
    'source' => 'google',
    'family' => 'Truculenta',
    'stack' => '"Truculenta", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'trykker' => array(
    'source' => 'google',
    'family' => 'Trykker',
    'stack' => '"Trykker", serif',
    'weights' => array(
      '400'
    )
  ),
  'tsukimirounded' => array(
    'source' => 'google',
    'family' => 'Tsukimi Rounded',
    'stack' => '"Tsukimi Rounded", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'tuffy' => array(
    'source' => 'google',
    'family' => 'Tuffy',
    'stack' => '"Tuffy", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'tulpenone' => array(
    'source' => 'google',
    'family' => 'Tulpen One',
    'stack' => '"Tulpen One", display',
    'weights' => array(
      '400'
    )
  ),
  'turretroad' => array(
    'source' => 'google',
    'family' => 'Turret Road',
    'stack' => '"Turret Road", display',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '700',
      '800'
    )
  ),
  'twinklestar' => array(
    'source' => 'google',
    'family' => 'Twinkle Star',
    'stack' => '"Twinkle Star", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'ubuntu' => array(
    'source' => 'google',
    'family' => 'Ubuntu',
    'stack' => '"Ubuntu", sans-serif',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '700',
      '700i'
    )
  ),
  'ubuntucondensed' => array(
    'source' => 'google',
    'family' => 'Ubuntu Condensed',
    'stack' => '"Ubuntu Condensed", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'ubuntumono' => array(
    'source' => 'google',
    'family' => 'Ubuntu Mono',
    'stack' => '"Ubuntu Mono", monospace',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'ubuntusans' => array(
    'source' => 'google',
    'family' => 'Ubuntu Sans',
    'stack' => '"Ubuntu Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i'
    )
  ),
  'ubuntusansmono' => array(
    'source' => 'google',
    'family' => 'Ubuntu Sans Mono',
    'stack' => '"Ubuntu Sans Mono", monospace',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'uchen' => array(
    'source' => 'google',
    'family' => 'Uchen',
    'stack' => '"Uchen", serif',
    'weights' => array(
      '400'
    )
  ),
  'ultra' => array(
    'source' => 'google',
    'family' => 'Ultra',
    'stack' => '"Ultra", serif',
    'weights' => array(
      '400'
    )
  ),
  'unbounded' => array(
    'source' => 'google',
    'family' => 'Unbounded',
    'stack' => '"Unbounded", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'uncialantiqua' => array(
    'source' => 'google',
    'family' => 'Uncial Antiqua',
    'stack' => '"Uncial Antiqua", display',
    'weights' => array(
      '400'
    )
  ),
  'underdog' => array(
    'source' => 'google',
    'family' => 'Underdog',
    'stack' => '"Underdog", display',
    'weights' => array(
      '400'
    )
  ),
  'unicaone' => array(
    'source' => 'google',
    'family' => 'Unica One',
    'stack' => '"Unica One", display',
    'weights' => array(
      '400'
    )
  ),
  'unifrakturcook' => array(
    'source' => 'google',
    'family' => 'UnifrakturCook',
    'stack' => '"UnifrakturCook", display',
    'weights' => array(
      '700'
    )
  ),
  'unifrakturmaguntia' => array(
    'source' => 'google',
    'family' => 'UnifrakturMaguntia',
    'stack' => '"UnifrakturMaguntia", display',
    'weights' => array(
      '400'
    )
  ),
  'unkempt' => array(
    'source' => 'google',
    'family' => 'Unkempt',
    'stack' => '"Unkempt", display',
    'weights' => array(
      '400',
      '700'
    )
  ),
  'unlock' => array(
    'source' => 'google',
    'family' => 'Unlock',
    'stack' => '"Unlock", display',
    'weights' => array(
      '400'
    )
  ),
  'unna' => array(
    'source' => 'google',
    'family' => 'Unna',
    'stack' => '"Unna", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'uoqmunthenkhung' => array(
    'source' => 'google',
    'family' => 'UoqMunThenKhung',
    'stack' => '"UoqMunThenKhung", serif',
    'weights' => array(
      '400'
    )
  ),
  'updock' => array(
    'source' => 'google',
    'family' => 'Updock',
    'stack' => '"Updock", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'urbanist' => array(
    'source' => 'google',
    'family' => 'Urbanist',
    'stack' => '"Urbanist", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'vt323' => array(
    'source' => 'google',
    'family' => 'VT323',
    'stack' => '"VT323", monospace',
    'weights' => array(
      '400'
    )
  ),
  'vampiroone' => array(
    'source' => 'google',
    'family' => 'Vampiro One',
    'stack' => '"Vampiro One", display',
    'weights' => array(
      '400'
    )
  ),
  'varela' => array(
    'source' => 'google',
    'family' => 'Varela',
    'stack' => '"Varela", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'varelaround' => array(
    'source' => 'google',
    'family' => 'Varela Round',
    'stack' => '"Varela Round", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'varta' => array(
    'source' => 'google',
    'family' => 'Varta',
    'stack' => '"Varta", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'vastshadow' => array(
    'source' => 'google',
    'family' => 'Vast Shadow',
    'stack' => '"Vast Shadow", serif',
    'weights' => array(
      '400'
    )
  ),
  'vazirmatn' => array(
    'source' => 'google',
    'family' => 'Vazirmatn',
    'stack' => '"Vazirmatn", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'vesperlibre' => array(
    'source' => 'google',
    'family' => 'Vesper Libre',
    'stack' => '"Vesper Libre", serif',
    'weights' => array(
      '400',
      '500',
      '700',
      '900'
    )
  ),
  'viaodalibre' => array(
    'source' => 'google',
    'family' => 'Viaoda Libre',
    'stack' => '"Viaoda Libre", display',
    'weights' => array(
      '400'
    )
  ),
  'vibes' => array(
    'source' => 'google',
    'family' => 'Vibes',
    'stack' => '"Vibes", display',
    'weights' => array(
      '400'
    )
  ),
  'vibur' => array(
    'source' => 'google',
    'family' => 'Vibur',
    'stack' => '"Vibur", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'victormono' => array(
    'source' => 'google',
    'family' => 'Victor Mono',
    'stack' => '"Victor Mono", monospace',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'vidaloka' => array(
    'source' => 'google',
    'family' => 'Vidaloka',
    'stack' => '"Vidaloka", serif',
    'weights' => array(
      '400'
    )
  ),
  'viga' => array(
    'source' => 'google',
    'family' => 'Viga',
    'stack' => '"Viga", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'vinasans' => array(
    'source' => 'google',
    'family' => 'Vina Sans',
    'stack' => '"Vina Sans", display',
    'weights' => array(
      '400'
    )
  ),
  'voces' => array(
    'source' => 'google',
    'family' => 'Voces',
    'stack' => '"Voces", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'volkhov' => array(
    'source' => 'google',
    'family' => 'Volkhov',
    'stack' => '"Volkhov", serif',
    'weights' => array(
      '400',
      '400i',
      '700',
      '700i'
    )
  ),
  'vollkorn' => array(
    'source' => 'google',
    'family' => 'Vollkorn',
    'stack' => '"Vollkorn", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'vollkornsc' => array(
    'source' => 'google',
    'family' => 'Vollkorn SC',
    'stack' => '"Vollkorn SC", serif',
    'weights' => array(
      '400',
      '600',
      '700',
      '900'
    )
  ),
  'voltaire' => array(
    'source' => 'google',
    'family' => 'Voltaire',
    'stack' => '"Voltaire", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'vujahdayscript' => array(
    'source' => 'google',
    'family' => 'Vujahday Script',
    'stack' => '"Vujahday Script", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'wdxllubrifontjpn' => array(
    'source' => 'google',
    'family' => 'WDXL Lubrifont JP N',
    'stack' => '"WDXL Lubrifont JP N", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'wdxllubrifontsc' => array(
    'source' => 'google',
    'family' => 'WDXL Lubrifont SC',
    'stack' => '"WDXL Lubrifont SC", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'wdxllubrifonttc' => array(
    'source' => 'google',
    'family' => 'WDXL Lubrifont TC',
    'stack' => '"WDXL Lubrifont TC", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'waitingforthesunrise' => array(
    'source' => 'google',
    'family' => 'Waiting for the Sunrise',
    'stack' => '"Waiting for the Sunrise", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'wallpoet' => array(
    'source' => 'google',
    'family' => 'Wallpoet',
    'stack' => '"Wallpoet", display',
    'weights' => array(
      '400'
    )
  ),
  'walterturncoat' => array(
    'source' => 'google',
    'family' => 'Walter Turncoat',
    'stack' => '"Walter Turncoat", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'warnes' => array(
    'source' => 'google',
    'family' => 'Warnes',
    'stack' => '"Warnes", display',
    'weights' => array(
      '400'
    )
  ),
  'waterbrush' => array(
    'source' => 'google',
    'family' => 'Water Brush',
    'stack' => '"Water Brush", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'waterfall' => array(
    'source' => 'google',
    'family' => 'Waterfall',
    'stack' => '"Waterfall", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'wavefont' => array(
    'source' => 'google',
    'family' => 'Wavefont',
    'stack' => '"Wavefont", display',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'wellfleet' => array(
    'source' => 'google',
    'family' => 'Wellfleet',
    'stack' => '"Wellfleet", serif',
    'weights' => array(
      '400'
    )
  ),
  'wendyone' => array(
    'source' => 'google',
    'family' => 'Wendy One',
    'stack' => '"Wendy One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'whisper' => array(
    'source' => 'google',
    'family' => 'Whisper',
    'stack' => '"Whisper", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'windsong' => array(
    'source' => 'google',
    'family' => 'WindSong',
    'stack' => '"WindSong", handwriting',
    'weights' => array(
      '400',
      '500'
    )
  ),
  'winkyrough' => array(
    'source' => 'google',
    'family' => 'Winky Rough',
    'stack' => '"Winky Rough", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'winkysans' => array(
    'source' => 'google',
    'family' => 'Winky Sans',
    'stack' => '"Winky Sans", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'wireone' => array(
    'source' => 'google',
    'family' => 'Wire One',
    'stack' => '"Wire One", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'wittgenstein' => array(
    'source' => 'google',
    'family' => 'Wittgenstein',
    'stack' => '"Wittgenstein", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'wixmadefordisplay' => array(
    'source' => 'google',
    'family' => 'Wix Madefor Display',
    'stack' => '"Wix Madefor Display", sans-serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '800'
    )
  ),
  'wixmadefortext' => array(
    'source' => 'google',
    'family' => 'Wix Madefor Text',
    'stack' => '"Wix Madefor Text", sans-serif',
    'weights' => array(
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i',
      '800',
      '800i'
    )
  ),
  'worksans' => array(
    'source' => 'google',
    'family' => 'Work Sans',
    'stack' => '"Work Sans", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'workbench' => array(
    'source' => 'google',
    'family' => 'Workbench',
    'stack' => '"Workbench", monospace',
    'weights' => array(
      '400'
    )
  ),
  'xanhmono' => array(
    'source' => 'google',
    'family' => 'Xanh Mono',
    'stack' => '"Xanh Mono", monospace',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'yaldevi' => array(
    'source' => 'google',
    'family' => 'Yaldevi',
    'stack' => '"Yaldevi", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'yanonekaffeesatz' => array(
    'source' => 'google',
    'family' => 'Yanone Kaffeesatz',
    'stack' => '"Yanone Kaffeesatz", sans-serif',
    'weights' => array(
      '200',
      '300',
      '400',
      '500',
      '600',
      '700'
    )
  ),
  'yantramanav' => array(
    'source' => 'google',
    'family' => 'Yantramanav',
    'stack' => '"Yantramanav", sans-serif',
    'weights' => array(
      '100',
      '300',
      '400',
      '500',
      '700',
      '900'
    )
  ),
  'yarndings12' => array(
    'source' => 'google',
    'family' => 'Yarndings 12',
    'stack' => '"Yarndings 12", display',
    'weights' => array(
      '400'
    )
  ),
  'yarndings12charted' => array(
    'source' => 'google',
    'family' => 'Yarndings 12 Charted',
    'stack' => '"Yarndings 12 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'yarndings20' => array(
    'source' => 'google',
    'family' => 'Yarndings 20',
    'stack' => '"Yarndings 20", display',
    'weights' => array(
      '400'
    )
  ),
  'yarndings20charted' => array(
    'source' => 'google',
    'family' => 'Yarndings 20 Charted',
    'stack' => '"Yarndings 20 Charted", display',
    'weights' => array(
      '400'
    )
  ),
  'yatraone' => array(
    'source' => 'google',
    'family' => 'Yatra One',
    'stack' => '"Yatra One", display',
    'weights' => array(
      '400'
    )
  ),
  'yellowtail' => array(
    'source' => 'google',
    'family' => 'Yellowtail',
    'stack' => '"Yellowtail", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'yeonsung' => array(
    'source' => 'google',
    'family' => 'Yeon Sung',
    'stack' => '"Yeon Sung", display',
    'weights' => array(
      '400'
    )
  ),
  'yesevaone' => array(
    'source' => 'google',
    'family' => 'Yeseva One',
    'stack' => '"Yeseva One", display',
    'weights' => array(
      '400'
    )
  ),
  'yesteryear' => array(
    'source' => 'google',
    'family' => 'Yesteryear',
    'stack' => '"Yesteryear", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'yomogi' => array(
    'source' => 'google',
    'family' => 'Yomogi',
    'stack' => '"Yomogi", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'youngserif' => array(
    'source' => 'google',
    'family' => 'Young Serif',
    'stack' => '"Young Serif", serif',
    'weights' => array(
      '400'
    )
  ),
  'yrsa' => array(
    'source' => 'google',
    'family' => 'Yrsa',
    'stack' => '"Yrsa", serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '600',
      '700',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i'
    )
  ),
  'ysabeau' => array(
    'source' => 'google',
    'family' => 'Ysabeau',
    'stack' => '"Ysabeau", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'ysabeauinfant' => array(
    'source' => 'google',
    'family' => 'Ysabeau Infant',
    'stack' => '"Ysabeau Infant", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'ysabeauoffice' => array(
    'source' => 'google',
    'family' => 'Ysabeau Office',
    'stack' => '"Ysabeau Office", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900',
      '100i',
      '200i',
      '300i',
      '400i',
      '500i',
      '600i',
      '700i',
      '800i',
      '900i'
    )
  ),
  'ysabeausc' => array(
    'source' => 'google',
    'family' => 'Ysabeau SC',
    'stack' => '"Ysabeau SC", sans-serif',
    'weights' => array(
      '100',
      '200',
      '300',
      '400',
      '500',
      '600',
      '700',
      '800',
      '900'
    )
  ),
  'yujiboku' => array(
    'source' => 'google',
    'family' => 'Yuji Boku',
    'stack' => '"Yuji Boku", serif',
    'weights' => array(
      '400'
    )
  ),
  'yujihentaiganaakari' => array(
    'source' => 'google',
    'family' => 'Yuji Hentaigana Akari',
    'stack' => '"Yuji Hentaigana Akari", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'yujihentaiganaakebono' => array(
    'source' => 'google',
    'family' => 'Yuji Hentaigana Akebono',
    'stack' => '"Yuji Hentaigana Akebono", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'yujimai' => array(
    'source' => 'google',
    'family' => 'Yuji Mai',
    'stack' => '"Yuji Mai", serif',
    'weights' => array(
      '400'
    )
  ),
  'yujisyuku' => array(
    'source' => 'google',
    'family' => 'Yuji Syuku',
    'stack' => '"Yuji Syuku", serif',
    'weights' => array(
      '400'
    )
  ),
  'yuseimagic' => array(
    'source' => 'google',
    'family' => 'Yusei Magic',
    'stack' => '"Yusei Magic", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'zcoolkuaile' => array(
    'source' => 'google',
    'family' => 'ZCOOL KuaiLe',
    'stack' => '"ZCOOL KuaiLe", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'zcoolqingkehuangyou' => array(
    'source' => 'google',
    'family' => 'ZCOOL QingKe HuangYou',
    'stack' => '"ZCOOL QingKe HuangYou", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'zcoolxiaowei' => array(
    'source' => 'google',
    'family' => 'ZCOOL XiaoWei',
    'stack' => '"ZCOOL XiaoWei", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'zain' => array(
    'source' => 'google',
    'family' => 'Zain',
    'stack' => '"Zain", sans-serif',
    'weights' => array(
      '200',
      '300',
      '300i',
      '400',
      '400i',
      '700',
      '800',
      '900'
    )
  ),
  'zenantique' => array(
    'source' => 'google',
    'family' => 'Zen Antique',
    'stack' => '"Zen Antique", serif',
    'weights' => array(
      '400'
    )
  ),
  'zenantiquesoft' => array(
    'source' => 'google',
    'family' => 'Zen Antique Soft',
    'stack' => '"Zen Antique Soft", serif',
    'weights' => array(
      '400'
    )
  ),
  'zendots' => array(
    'source' => 'google',
    'family' => 'Zen Dots',
    'stack' => '"Zen Dots", display',
    'weights' => array(
      '400'
    )
  ),
  'zenkakugothicantique' => array(
    'source' => 'google',
    'family' => 'Zen Kaku Gothic Antique',
    'stack' => '"Zen Kaku Gothic Antique", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '700',
      '900'
    )
  ),
  'zenkakugothicnew' => array(
    'source' => 'google',
    'family' => 'Zen Kaku Gothic New',
    'stack' => '"Zen Kaku Gothic New", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '700',
      '900'
    )
  ),
  'zenkurenaido' => array(
    'source' => 'google',
    'family' => 'Zen Kurenaido',
    'stack' => '"Zen Kurenaido", sans-serif',
    'weights' => array(
      '400'
    )
  ),
  'zenloop' => array(
    'source' => 'google',
    'family' => 'Zen Loop',
    'stack' => '"Zen Loop", display',
    'weights' => array(
      '400',
      '400i'
    )
  ),
  'zenmarugothic' => array(
    'source' => 'google',
    'family' => 'Zen Maru Gothic',
    'stack' => '"Zen Maru Gothic", sans-serif',
    'weights' => array(
      '300',
      '400',
      '500',
      '700',
      '900'
    )
  ),
  'zenoldmincho' => array(
    'source' => 'google',
    'family' => 'Zen Old Mincho',
    'stack' => '"Zen Old Mincho", serif',
    'weights' => array(
      '400',
      '500',
      '600',
      '700',
      '900'
    )
  ),
  'zentokyozoo' => array(
    'source' => 'google',
    'family' => 'Zen Tokyo Zoo',
    'stack' => '"Zen Tokyo Zoo", display',
    'weights' => array(
      '400'
    )
  ),
  'zeyada' => array(
    'source' => 'google',
    'family' => 'Zeyada',
    'stack' => '"Zeyada", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'zhimangxing' => array(
    'source' => 'google',
    'family' => 'Zhi Mang Xing',
    'stack' => '"Zhi Mang Xing", handwriting',
    'weights' => array(
      '400'
    )
  ),
  'zillaslab' => array(
    'source' => 'google',
    'family' => 'Zilla Slab',
    'stack' => '"Zilla Slab", serif',
    'weights' => array(
      '300',
      '300i',
      '400',
      '400i',
      '500',
      '500i',
      '600',
      '600i',
      '700',
      '700i'
    )
  ),
  'zillaslabhighlight' => array(
    'source' => 'google',
    'family' => 'Zilla Slab Highlight',
    'stack' => '"Zilla Slab Highlight", serif',
    'weights' => array(
      '400',
      '700'
    )
  )
);
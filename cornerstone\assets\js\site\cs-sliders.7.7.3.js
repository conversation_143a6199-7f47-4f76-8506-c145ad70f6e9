(()=>{var{store:et,util:tt}=window.csGlobal.rivet,{subtractVectors:W,debounce:nt,teardown:st,listener:z,listenerPassive:De,divideVectors:Te,clampVector:rt,vectorsEq:it,addVectors:ge,absVector:fn,roundVector:Fe,multiplyVectors:ve}=tt,se=.065,ot=75e-6,Ie=3,Me=7,at=3,re=e=>!1,ct=ot*-1;function lt(e,{canBeginMove:n=()=>!0,onBeginMove:i=()=>{},onEndMove:c=()=>{},onMove:a=()=>{},onClick:f=()=>{},className:t="is-drag",drag:l,click:m,scroll:r,checkY:d=!1}){let o=!0,u,g=null,p=!1,v=!1,A=!1,S,x=0,h=[],T=!0,D=(s,y)=>{S||(S=s.timeStamp);let b=S-s.timeStamp;S=s.timeStamp;let E=W(u,y);u=y;let k=rt(Te(E,[b,b]),Me*-1,Me);return h.push(k),h.length>at&&h.shift(),x=Te(h.reduce((F,V)=>ge(F,V)),[h.length,h.length]),[y,x]},I=({clientX:s,clientY:y})=>[s,y],M=s=>{var y;return s.type.includes("touch")?I((y=s.touches[0])!=null?y:s.changedTouches[0]):I(s)},L=([s,y])=>Math.abs(s)>Ie||d&&Math.abs(y)>Ie,P=s=>{g=M(s),u=g,h=[]},q=s=>W(g,M(s)),N=s=>{re("POINTER START"),l&&!A&&n(s)&&(P(s),e.addEventListener("pointermove",Y))},R=s=>{re("TOUCH START"),l&&!A&&n(s)&&(v=!0,P(s),e.removeEventListener("pointermove",Y),e.addEventListener("touchmove",X))},O=s=>{if(re("DRAG",s.type),!!o)if(p)s.cancelable&&s.preventDefault(),a(...D(s,q(s)));else{s.type==="pointermove"&&e.setPointerCapture(s.pointerId);let y=q(s);L(y)&&(P(s),p=!0,i(...D(s,y),!1,s),t&&e.classList.add(t))}},Y=s=>{O(s)},X=s=>{O(s)},U=(s,y)=>{S=null,!(s.type.includes("pointer")&&(e.removeEventListener("pointermove",Y),e.releasePointerCapture(s.pointerId),v))&&(s.type.includes("touch")&&(e.removeEventListener("touchmove",X),v=!1),!!p&&(re("END",s.type,y),c(q(s),x,!1,y),t&&e.classList.remove(t),p=!1))},j=s=>{U(s),m&&s.target!==e&&!v&&f(s.target)},Q=s=>void U(s,!0),ee=s=>{if(!o){o=!0;return}U(s)},_=s=>void U(s),ue=s=>{A?a(s,[0,0],!0):(A=!0,i([0,0],[0,0],!0)),fe()},fe=nt(()=>{A=!1,c([0,0],[0,0],!0)},200),J=0,Z=s=>{if(!p){if(J+=s.wheelDelta,Math.abs(J)<100)return;let b=-J;s.preventDefault(),J=0,ue([b,b])}},w,te=function(){o=!1,w&&clearTimeout(w),w=setTimeout(function(){o=!0},100)};return st([z(e,"touchstart",R),z(e,"touchend",ee),z(e,"touchcancel",_),z(e,"pointerdown",N),z(e,"pointerup",j),z(e,"pointercancel",Q),r?z(e,"wheel",Z):null,De(window,"scroll",te),De(document.body,"scroll",te),()=>void e.removeEventListener("pointermove",Y)])}function ie({el:e,drag:n,click:i,scroll:c,cursor:a=!1},f){if(window.csGlobal&&window.csGlobal.disableSlideInteractions)return()=>{};if(!n&&!i)return()=>{};a&&e.classList.add("can-drag");let{unsubscribe:t,getState:l,dispatch:m}=et.subscribe(e,"slider"),r=lt(e,{...f({getState:l,dispatch:m}),className:a?"is-drag":"",click:i,drag:n,scroll:c});return()=>{a&&e.classList.remove("can-drag"),r(),t()}}var Ve=e=>{let n=!1,i=!1,c=!1,a=null,f=[0,0],t=[0,0],l=W([1,1],[se,se]),m,r,d=u=>{let g=u-r;r=u,!c&&a&&(t=ge(t,ve(W(f,a),[ct*g,0]))),t=ve(t,l);let p=ve(t,[g,g]),v=ge(f,p),A=!1,S=!1;if(l[0]<1||l[1]<1){let h=.001*g,T=Math.abs(t[0])<=h&&t[0]!==0,D=Math.abs(t[1])<=h&&t[1]!==0;T&&(t[0]=0),D&&(t[1]=0),(T&&D||T&&t[1]===0||D&&t[0]===0)&&(S=!0)}if(a){let h=W(a,v),T=W(a,f);h[0]>0^T[0]>0&&(v[0]=a[0],t[0]=0),h[1]>0^T[1]>0&&(v[1]=a[1],t[1]=0),it(Fe(a,1),Fe(v,1))&&(v=a,a=null,A=!0,S=!0,t=[0,0])}i&&(i=!1,S=!0,t=[0,0]),f=v;let x=e(v,{snapped:A,stalled:S});S&&!a?n=!1:m=requestAnimationFrame(d),typeof x=="function"&&x()},o=()=>{i=!1,!n&&(n=!0,r=performance.now(),cancelAnimationFrame(m),requestAnimationFrame(d))};return{start:()=>{o()},snapTo:u=>{o(),a=u?[u[0],0]:null},stall:()=>{i=!0,a=null},suspend:u=>{c=!!u},isSuspended:()=>c,setPosition:([u])=>{o(),f=[u,0]},setVelocity:([u],g=!0)=>{g&&o(),t=[u,0]},setFriction:u=>{l=W([1,1],[u,u])},resetFriction:()=>{l=[se,se]}}},dt=.2,oe=e=>Math.abs(e)<dt?0:e>0?1:-1;var{store:ut,util:ft}=window.csGlobal.rivet,{teardown:pt,listener:Le,intersect:mt,getEasing:gt,round:vt,onPageVisibilityChange:yt,triggerScan:mn,oncePassive:ht,listenerPassive:bt}=ft,Ce=({onPlay:e,onPause:n,onReset:i,duration:c})=>{let a=new Set,f=!1,t=()=>{!f&&a.size<=0&&(f=!0,e(o))},l=u=>{a.has(u)&&a.delete(u),t()},m=u=>{let g=f;f=!1,a.has(u)||a.add(u),g&&n(o)};m("main");let o={getDuration:()=>(typeof c=="function"?c():c)||1e3,release:l,hold:m,reset:()=>{i(o,f),t()},setup:()=>{}};return o},wt=({duration:e,interval:n,animate:i,easing:c="linear"})=>{let a,f,t,l,m=0,r=0,d=gt(c);i(0);let o=p=>{let v=vt(d((p-f)/t),1e3);v!==m&&i(v),m=v,l=requestAnimationFrame(o)},u=p=>{f=window.performance.now(),r?f=window.performance.now()-p*r:i(0),m=r,t=p,r=0,cancelAnimationFrame(l),l=requestAnimationFrame(o)},g=p=>{let v=p;r&&(v=p-p*r),u(p),a=setInterval(()=>{u(p),v!==p&&(clearInterval(a),cancelAnimationFrame(l),g(p)),n()},v)};return Ce({duration:e,onPlay({getDuration:p}){g(p())},onPause(){r=m,clearInterval(a),cancelAnimationFrame(l)},onReset({getDuration:p},v){clearInterval(a),cancelAnimationFrame(l),v&&g(p())}})},St=({duration:e})=>{let n={play:()=>{},pause:()=>{},reset:()=>{}};return{...Ce({duration:e,onPlay:(...a)=>n.play(...a),onPause:(...a)=>n.pause(...a),onReset:(...a)=>n.reset(...a)}),setup:a=>{n={...n,...a}}}},xt=({mode:e,...n})=>e==="interval"?wt(n):e==="marquee"?St(n):{reset:()=>{},setup:()=>{},hold:()=>{},release:()=>{}},At=({autoplay:e,stacked:n})=>n&&e==="marquee"?"off":e,ae=(e,n,i)=>{let c=At(i),{unsubscribe:a,getState:f,dispatch:t}=ut.subscribe(e,"slider",(g,p,v)=>{v&&requestAnimationFrame(()=>{l.release("main")}),!v&&p.includes("lastUserInteraction")&&l.reset()},["lastUserInteraction"]),l=xt({mode:c,duration:()=>f().autoplayDuration||1e3,interval:()=>t(g=>({...g,autoplayAdvance:1})),animate:g=>{n.style.setProperty("--x-slide-autoplay-progress",g)}}),m=Le(window,"tco-drag-start",()=>{l.hold("dragging")}),r=Le(window,"tco-drag-end",()=>{l.release("dragging")}),d=null,o=null;i.autoplayStartInView&&(l.hold("viewport"),d=mt(e,({isIntersecting:g})=>{g?(l.release("viewport"),Array.from(e.querySelectorAll('[loading="lazy"]')).forEach(p=>{p.removeAttribute("loading")})):l.hold("viewport")},{threshold:.15,top:"0px",bottom:"0px"}),o=yt(g=>{g?l.release("vis"):l.hold("vis")})),f().autoplayer=l;let u=[a,m,r,d,o];if(i.pauseOnHover){let g=Pt(e,l);u.push(g)}return{unsubscribe:pt(u),autoplayer:l}};function Pt(e,n){let i=e.parentNode;return bt(i,"mouseover",function(){n.hold(),ht(i,"mouseout",function(){n.release()})})}var qe=(e,n,i,c)=>e(()=>{let{unsubscribe:a,autoplayer:f}=ae(n,i,c),t=(c.swipe||"").includes("x"),l=(c.swipe||"").includes("y"),m=([o,u])=>t&&l?Math.abs(o)>=Math.abs(u)?0:1:l?1:0,r=o=>oe(o[m(o)]),d=ie({el:n,drag:!!c.swipe,checkY:l},({dispatch:o,getState:u})=>({onBeginMove:()=>void f.hold("stacked-drag"),onEndMove:(g,p,v,A)=>{if(!A){let S=r(p);S!==0&&o(x=>({...x,advance:S}))}f.release("stacked-drag")}}));return()=>{a(),d()}});var{store:kt,util:Et}=window.csGlobal.rivet,{multiplyVectors:Be,once:Sn,oncePassive:Dt,vectorsEq:ye,listener:Tt,wrapNumber:Ft,clamp:It,addVectors:B,subtractVectors:$,balanceFromDifference:Mt,makeSortByKey:Vt,elementMeta:H,onViewportChange:Lt,onLoad:Ct}=Et,qt=e=>n=>Math.floor(n/e),Bt=({subprime:e=0,totalSlides:n=0,slidesPerScrollPage:i=1})=>It(qt(i)(e)*i,0,n),he=e=>n=>{if(!n)return null;let i=n;do i=i[e];while(i&&!i.matches("[data-x-slide]"));return i};function Nt(e,n,{wrapAround:i}){if(!i)return()=>{};let c=()=>{let{clones:t=[]}=H.get(e);t.forEach(l=>l.remove())},a=t=>l=>({before:t,clone:H.get(l).createClone()}),f=Tt(e,"tco-slide-refresh",()=>{let{slides:t=[]}=H.get(e);c();let l=[...t.map(a(!0)).reverse(),...t.map(a(!1))],m=0;t.forEach(function(d){m+=d.getBoundingClientRect().width});let r=window.innerWidth/m;for(r=Math.ceil(r),r=Math.min(6,r),m===0&&(r=2),r-=2;r>0;)l=[...t.map(a(!0)).reverse(),...l,...t.map(a(!1))],r-=2;l.forEach(({clone:d,before:o})=>{n[o?"prepend":"append"](d)}),H.update(e,d=>({...d,clones:l.map(({clone:o})=>o)})),e.dispatchEvent(new CustomEvent("tco-slides-cloned"))});return()=>{f(),c()}}var Rt=(e,n,i)=>{let c=()=>H.get(e).slides;c.first=()=>c()[0],c.last=()=>{let r=c();return r[r.length-1]};let a=Nt(e,n,i),f=(r,d)=>{let o=he(d<0?"previousElementSibling":"nextElementSibling");return(u,g)=>{if(d===0)return r[u];let p=o(r[g]);for(;p;){if(H.get(p).index===u)return p;p=o(p)}return r[u]}},t=(r,{prevSubprime:d=0,totalSlides:o,advanceDirection:u})=>{let g=c();if(!i.wrapAround)return g[r];let p=u===0?Mt(r,d,o):u;return f(g,p)(r,d)};return{anticipateSnapTarget:r=>t(Bt(r),r),teardownSlides:()=>{a()},getSlides:c}},Ot=(e,{justify:n})=>{let i,c=()=>{i=new WeakMap};c();let a=({offsetLeft:d,offsetTop:o,offsetWidth:u,offsetHeight:g})=>[d,o,u,g],f=d=>{if(!i.has()){let o=a(d);return i.set(d,o),o}return i.get(d)};return{getElementOffsets:f,getElementDimensions:d=>{let[,,o,u]=f(d);return[o,u]},getElementCoordinates:d=>{let[o,u]=f(d);return[o,u]},getVectorFromTarget:d=>{if(!d)return[0,0];let[o,u,g,p]=f(d),v=[o,u];return n==="center"?B(v,[g/2,p/2]):n==="end"?B(v,[g,p]):v},normalizeJustifiedVector:d=>{let[,,o,u]=f(e);return n==="center"?$(d,[o/2,u/2]):n==="end"?$(d,[o,u]):d},teardown:Lt(c)}},Ut=(e,n,{getElementDimensions:i,getElementOffsets:c,getElementCoordinates:a,normalizeJustifiedVector:f,getVectorFromTarget:t},{contain:l,wrapAround:m})=>{let r=0;return d=>{let o=e.first(),u=e.last();if(m){let g=$(d,t(he("previousElementSibling")(o))),p=$(d,t(he("nextElementSibling")(u)));if(g[r]<=0)return[d,B(t(u),g)];if(p[r]>=0)return[d,B(t(o),p)]}else{if(l){let v=f(d);if(v[r]<0){let h=Be(f([0,0]),[-1,-1]);return[h,h]}let A=i(n.parentElement),S=B(a(u),i(u)),x=$(S,A);if(f(x),v[r]>x[r]){let h=B(x,$(x,f(x)));return[h,h]}}let g=t(o),p=t(u);if(d[r]<g[r])return[g,g];if(d[r]>p[r])return[p,p]}return[d,null]}},Ne=(e,n,i,c,a)=>e(()=>{let{wrapAround:f=!1,snap:t=!1,int:l=""}=a,{unsubscribe:m,autoplayer:r}=ae(n,i,a),d=a.autoplay==="marquee";d&&r.hold("marquee-positioned");let o=Number.parseFloat(a.speed)||.1;a.direction==="reverse"&&(o=-o);let{getSlides:u,anticipateSnapTarget:g,teardownSlides:p}=Rt(n,c,a),v=Ot(c,a),{normalizeJustifiedVector:A,getElementDimensions:S,getVectorFromTarget:x,teardown:h}=v,T=Ut(u,c,v,a),D=!1,I=[0,0],M=[0,0],L=[0,0],P=s=>{if(ye(s,I)&&D)return!1;I=s,M=I;let[y]=A(s);return c.style.setProperty("transform",`translate3d(${y*-1}px,0,0)`),!0},q,N=([s])=>{let[y,b]=T([s,0]);w.suspend(!0),r.hold("transition"),c.style.removeProperty("transition"),P(y),clearTimeout(q),q=setTimeout(()=>{c.style.setProperty("transition","none"),c.offsetHeight,b&&P(b),r.release("transition"),w.suspend(!1),w.stall()},X().transitionDuration)},R=s=>x(g(s)),O=s=>d&&o<0?x(u.last()):R(s),{unsubscribe:Y,getState:X,dispatch:U}=kt.subscribe(n,"slider",(s,y)=>{if(!s.positioned||!s.pageLoaded)return;if(!D){c.style.setProperty("transition","none"),c.offsetHeight,P(O(s)),c.offsetHeight,c.style.removeProperty("transition"),D=!0,requestAnimationFrame(()=>{r.release("marquee-positioned")});return}if(d)return;if(y.includes("lastAutoBasisAdvance")){let k=Be(S(c.parentElement),[s.autoBasisAdvanceAmount,s.autoBasisAdvanceAmount]),F=B(M,k),[,V]=T(F);N(F),Z(V||F);return}let b=s.allowSnapTransition&&y.includes("subprime")||y.includes("snapBack"),E=y.includes("styleUpdate");(b||E)&&N(R(s))},["isTouchInteraction","allowSnapTransition","styleUpdate","subprime","slidesPerPage","snapBack","lastAutoBasisAdvance"]),j=(s,y,b=0)=>{let{slideData:E}=X(),k=[];b!==0&&(k=E.filter(({isPageStart:C,balance:K})=>C&&K===b).map(({index:C})=>C),k.length===0&&f&&(k=E.filter(({isPageStart:C})=>C).map(({index:C})=>C)));let F=A(B(s,y)),V=Array.from(c.children),pe=Math.floor(V.length/2),ne=[];for(let C=0;C<V.length;C++){let K=V[Ft(C+pe,V.length)],{index:me,isClone:Ke}=H.get(K),{isPageStart:Qe}=E[me];if(!Qe||k.length>0&&!k.includes(me))continue;let Pe=x(K),Ze=S(K)[0],je=$(A(Pe),F),ke=Math.abs(je[0]),Ee={slide:K,vector:Pe,isClone:Ke,index:me,distance:ke};if(k.length===0&&ke<Ze/2)return Ee;ne.push(Ee)}return ne.sort(Vt("distance")).shift()},Q=l.includes("drag"),ee=!1,_=!1,ue=ie({el:n,drag:Q,click:l.includes("click")&&!d,cursor:Q,scroll:Q&&!d},({dispatch:s})=>({canBeginMove:({target:y})=>{if(u().length<=0||_)return!1;let b=y.closest(".tco-element-preview");return!b||b.matches(".x-slide, .x-slide-container-viewport")},onBeginMove:(y,b,E,k)=>{E&&(ee=!0),r.hold("user-drag"),L=I,w.suspend(!0),w.snapTo(null),w.setVelocity([0,0]),w.setPosition(L),s(F=>({...F,isTouchInteraction:!0,allowSnapTransition:!1}))},onEndMove:(y,b,E,k)=>{w.suspend(!1),w.setVelocity(b),r.hold("physics"),E&&(w.stall(),ee=!1);let F={isTouchInteraction:!1};if(t&&!d){let V=k?0:oe(b[0]),{index:pe,vector:ne}=j(E||k?I:L,k?[0,0]:y,V);F.softNavigateTo=pe,w.snapTo(ne)}s(V=>({...V,...F})),r.release("user-drag")},onMove:(y,b,E)=>{if(!_)if(E){let k=B(I,y);if(f)w.setPosition(k);else{let[F]=T(k);w.setPosition(ye(k,F)?k:F)}}else w.snapTo(null),w.setPosition(B(L,y))},onClick:y=>{let b=H.get(y.closest("[data-x-slide]")).index;typeof b!="undefined"&&s(E=>({...E,navigateToIndex:b}))}})),fe=s=>{let[y,b]=T(s);return f?(b&&d&&w.setPosition(b),b||s):(w.isSuspended()||y&&b&&ye(y,b)&&(_=!0,w.snapTo(b),Z(b)),s)},J=s=>{X().prime!==s&&U(y=>({...y,softNavigateTo:s}))},Z=s=>{J(j(s,[0,0]).index)},w=Ve((s,{snapped:y,stalled:b})=>{let E=fe(s);if(P(E),(b||y)&&(_=!1),d&&(Z(E),y&&(o*=-1,w.resetFriction(),w.setVelocity([o,o]))),b)return()=>{r.release("physics")}});c.style.setProperty("transition","none"),r.setup({play:()=>{w.setFriction(0),w.setPosition(I),w.setVelocity([o,o])},pause:()=>{w.setVelocity([0,0],!1),w.resetFriction(),w.stall()}});let te=Dt(n,"tco-slides-cloned",()=>{U(s=>({...s,positioned:!0}))});return()=>{p(),te(),m(),Y(),Ct(()=>{U(s=>s),w.start()}),ue(),h()}});var{drive:Ht,attach:Re,util:Gt,debug:Wt}=window.csGlobal.rivet,{elementMeta:G,getCachedJsonAttribute:zt,listener:$t}=Gt,Oe=e=>!!G.get(e).isClone,Ue=e=>e?Array.from(e.children).filter(n=>!Oe(n)&&n.matches(".x-slide")):[],He=e=>Ue(e).filter(n=>G.get(n).ready),ce=(e,n)=>{if(!n){let{index:i}=G.get(e);if(typeof i!="undefined")return i}return Ue(e.parentElement).indexOf(e)},Ge=e=>{if(!e.hasAttribute("data-cs-observeable-id"))return;let n=e.getAttribute("data-cs-observeable-id");e.classList.remove("tco-element-preview"),e.removeAttribute("data-cs-observeable-container"),e.removeAttribute("data-cs-observe"),e.removeAttribute("data-cs-observeable"),e.removeAttribute("data-cs-observeable-id"),e.classList.add(`tco-observe-${n}`)},Yt=(e,n)=>{let i=e.cloneNode(!0);return i.classList.add("is-virtual"),i.setAttribute("aria-hidden",!0),Ge(i),Array.from(i.querySelectorAll("[data-cs-observeable-id]")).map(Ge),Array.from(i.querySelectorAll("[id]")).map(c=>c.removeAttribute("id")),()=>{let c=i.cloneNode(!0);return G.set(c,{...n,index:ce(e),isClone:!0}),c}};Re("[data-x-slide]",e=>{if(Oe(e))return;let n=e.closest("[data-x-slide-container]"),{wrapAround:i,enter:c="effect",exit:a="effect"}=zt(n,"data-x-slide-container"),f={enter:c,exit:a};G.update(e,l=>({...l,index:ce(e),ready:!0,effects:f,createClone:i?Yt(e,{effects:f}):null})),n.dispatchEvent(new CustomEvent("tco-slide-added-or-removed"));let t=$t(n,"tco-slide-reindex",()=>{G.update(e,l=>({...l,index:ce(e,!0)}))});return()=>{G.del(e),t(),n.dispatchEvent(new CustomEvent("tco-slide-added-or-removed"))}},50);Re("[data-x-slide]",e=>(G.update(e,n=>({...n,effectRivet:["effects",{key:()=>`slideData.${ce(e)}.active`,condition:!0,enter:"effect",exit:"effect"},"slider"]})),Ht([[e,"classname",{key:"slideData.meta(index).active",classname:"is-current-slide",condition:!0},"slider"],Wt(e)&&[e,"attr",{key:"subprime",attr:"data-prime-slide",value:!0,condition:["meta(index)","==="]},"slider"],[e,"effects",{key:"slideData.meta(index).active",condition:!0,enter:"meta(effects.enter)",exit:"meta(effects.exit)"},"slider"],[e,"prop",{key:"slideData.meta(index).distance",prop:"--x-slide-distance"},"slider"],[e,"prop",{key:"slideData.meta(index).balance",prop:"--x-slide-balance"},"slider"]])),200);var{attach:Xt,store:We,drive:_t,util:ze}=window.csGlobal.rivet,{onLoad:Jt,wrapNumber:Kt,clamp:Qt,makeStateSynchronizer:Zt,distanceInRange:$e,balanceFromDifference:jt,listenerPassive:le,elementMeta:be,parseTime:Ye}=ze,en=e=>n=>Math.floor(n/e),tn=({slidesPerScrollPage:e,totalSlides:n})=>e?Math.ceil(n/e):n,nn=()=>{let e=null,n=!0,i=(f,t)=>{n=t?!!t.keyboardNavigation:!0,e=f.closest("[data-x-slide-context], [data-x-slide-container], [data-x-slide-pagination], [data-x-slide-next], [data-x-slide-prev], [data-x-slide-goto]")};le(window,"focusin",({target:f})=>{i(f)});let c=["ArrowRight","ArrowDown"],a=["ArrowLeft","ArrowUp"];return le(window.document,"keyup",({key:f})=>{if(e&&n){let t=c.includes(f),l=a.includes(f);if(t||l){let m=We.makeDispatch(e,"slider");m&&m(r=>({...r,advance:t?1:-1}))}}}),i},sn=nn(),rn=()=>({advance:e,autoplayAdvance:n,navigateToIndex:i,softNavigateTo:c,navigateTo:a,...f})=>{let t={...f},{scrollBySlide:l,wrapAround:m,autoBasis:r}=t.options,d=!l&&r;typeof t.autoplayDuration!="number"&&(t={...t,autoplayDuration:Ye(t.autoplayDuration)}),typeof t.transitionDuration!="number"&&(t={...t,transitionDuration:Ye(t.transitionDuration)}),typeof t.slidesPerPage!="number"&&(t={...t,slidesPerPage:Number(t.slidesPerPage)}),t.slidesPerScrollPage=l?1:t.slidesPerPage;let o=en(t.slidesPerScrollPage),u=tn(t),g=o(t.prime),p,v;if(typeof c!="undefined"&&(v=c),typeof i!="undefined"&&(v=o(i)),t.options.autoplay!=="marquee"&&((typeof e!="undefined"||typeof a!="undefined"||typeof i!="undefined")&&(t={...t,lastUserInteraction:Date.now(),allowSnapTransition:!0}),typeof e!="undefined"&&t.ready&&(p=e),typeof a!="undefined"&&t.ready&&(v=a,t={...t,advanceDirection:0})),typeof n!="undefined"&&(p=n),typeof p!="undefined"&&(d?t={...t,lastAutoBasisAdvance:Date.now(),autoBasisAdvanceAmount:p,allowSnapTransition:!1}:(t={...t,advanceDirection:p>0?1:-1},v=Kt(g+p,u))),typeof v!="undefined"){let x=v*t.slidesPerScrollPage;t={...t,prime:Qt(x,0,t.totalSlides-1)}}let A=o(t.prime),S=o(t.subprime);return t={...t,current:A+1,total:u,atStart:A===0,atEnd:A===u-1,slideData:Array.from({length:t.totalSlides}).map((x,h)=>{let D=h%t.slidesPerScrollPage===0;if(o(h)===S)return{active:!0,distance:0,balance:0,index:h,isPageStart:D};let M=t.subprime,L=t.subprime+t.slidesPerScrollPage-1;if(m){let N=jt(h,t.subprime,t.totalSlides),R=N===1?L:M,O=Math.min($e(h,R,t.totalSlides),$e(R,h,t.totalSlides));return{active:!1,distance:O,balance:N,index:h,isPageStart:D}}let P=h>M?1:-1,q=h>M?h-L:M-h;return{active:!1,balance:P,distance:q,index:h,isPageStart:D}})},t};Xt("[data-x-slide-container]",(e,n)=>{let i=e.closest("[data-x-slide-context]")||e,c=e.querySelector(".x-slide-container"),{stacked:a=!1,autoplay:f="off",adaptiveHeight:t=!1,wrapAround:l,startingSlide:m=1}=n,r=d=>{let o=e.querySelector(".x-slide-container-content"),u=()=>be.update(e,P=>({...P,slides:He(c)}));u();let g=be.get(e).slides.length;m=g>0?Math.min(g,m):m,m=Math.max(1,m);let p=m-1,v=document.readyState==="complete",A=_t([[i,"define",{options:n,loading:!0,ready:!1,advanceDirection:0,autoplayDuration:"var(--x-slide-container-autoplay-transition-duration, 5s)",transitionDuration:"var(--x-slide-container-transition-duration, 1s)",isTouchInteraction:!1,allowSnapTransition:!0,current:m,prime:p,subprime:p,prevSubprime:p,slideData:[],styleUpdate:m,slidesPerPage:"var(--x-slides-per-page,1)",lastUserInteraction:Date.now(),autoBasisAdvanceAmount:1,lastAutoBasisAdvance:Date.now(),positioned:!!(a||!l),pageLoaded:v,total:g,totalSlides:g,_reducer:rn()},"slider"],[i,"prop",{key:"current",prop:"--x-slide-current"},"slider"],[i,"prop",{key:"total",prop:"--x-slide-total"},"slider"],[e,"onready"],[e,"onload"],[e,"onscan"],[e,"inc",{state:"slider",key:"styleUpdate"}],[e,"onload",{},"loaded"],[e,"onready",{},"ready"],[e,"set",{state:"slider",key:"ready",value:!0,defer:!0},"ready"],[e,"classname",{key:"ready",classname:"is-ready",condition:!0},"slider"],[e,"set",{state:"slider",key:"transitionDuration",value:"var(--x-slide-container-transition-duration, 1s)"}],f!=="off"&&[e,"set",{state:"slider",key:"autoplayDuration",value:"var(--x-slide-container-autoplay-transition-duration, 5s)"}],!a&&[e,"set",{state:"slider",key:"slidesPerPage",value:"var(--x-slides-per-page)"}],t&&[o,"height",{key:"subprime",selector:".is-current-slide"},"slider"]]),S=ze.rivetListener(e,"loaded",()=>{e.classList.remove("is-loading")}),x=Zt((P,q)=>{let N=P,R=T().transitionDuration;D(O=>({...O,prevSubprime:O.subprime,subprime:N})),setTimeout(q,R)}),{unsubscribe:h,getState:T,dispatch:D}=We.subscribe(e,"slider",({prime:P})=>void x(P)),I=le(e,"tco-slide-added-or-removed",()=>{u(),D(P=>({...P,totalSlides:be.get(e).slides.length})),e.dispatchEvent(new CustomEvent("tco-slide-reindex")),e.dispatchEvent(new CustomEvent("tco-slide-refresh"))}),M=d();e.dispatchEvent(new CustomEvent("tco-slide-refresh"));let L=le(e,"pointerup",()=>sn(e,n));return[h,A,I,S,M,L,v?null:Jt(()=>{D(P=>({...P,pageLoaded:document.readyState==="complete"}))})]};return a?qe(r,e,i,n):Ne(r,e,i,c,n)},100);var{csHooks:on}=window.csGlobal,{attach:de,store:we,drive:Xe,util:an,debug:Dn}=window.csGlobal.rivet,{makeStateSynchronizer:cn,getTransitionDuration:ln,listener:Se,elementMeta:dn}=an;de("[data-x-slide-pagination]",e=>{let n=cn((l,m)=>{Array.from(e.querySelectorAll("li")).forEach((r,d)=>{let o=d===l-1;r.classList.toggle("is-active",o),!o&&r.hasAttribute("aria-current")&&r.removeAttribute("aria-current"),o&&!r.hasAttribute("aria-current")&&r.setAttribute("aria-current","step")}),setTimeout(m,ln(e.querySelector("li")))}),i=l=>{let m=(r,d,o)=>r+`<li data-x-slide-index="${o+1}" aria-label="Slider Page ${o+1}"><span class="visually-hidden">${o}</span></li>`;e.innerHTML=Array.from({length:Math.max(1,l)}).reduce(m,""),n.reset()};i(1);let c=xe(e),{unsubscribe:a,dispatch:f}=we.subscribe(c,"slider",({total:l,current:m},r,d)=>{(d||r.includes("total"))&&i(l),n(m)},["current","total","slidesPerPage","styleUpdate"]),t=Ae(e);return[Se(e,t,l=>{l.preventDefault();let m=l.target.closest("li");if(m){let r=Number.parseInt(m.getAttribute("data-x-slide-index"),10)-1;isNaN(r)||f(d=>({...d,navigateTo:r}))}}),a]},1e3);var un=(e,n,i)=>e?Xe([[n,"effects",{key:i===-1?"atStart":"atEnd",condition:[!1,"==="],enter:"effect",exit:"effect"},"slider"],[n,"attr",{key:i===-1?"atStart":"atEnd",attr:"disabled",value:"",condition:[!0,"==="]},"slider"]]):()=>{},_e=e=>{let n=Number.parseInt(e);return isNaN(n)||n<=0?1:n},Je=e=>(n,i)=>{var r,d;let c=_e(i)*e,a=xe(n),{unsubscribe:f,getState:t,dispatch:l}=we.subscribe(a,"slider"),m=Ae(n);return[un(!((d=(r=t())==null?void 0:r.options)==null?void 0:d.wrapAround)&&i!=="noDisable",n,e),Se(n,m,o=>{l(u=>({...u,advance:c}))},{passive:!0}),f]};de("[data-x-slide-next]",Je(1),1e3);de("[data-x-slide-prev]",Je(-1),1e3);de("[data-x-slide-goto]",(e,n)=>{let i=_e(n),c=i-1,a=xe(e),f=!!e.getAttribute("data-x-slide-goto-keep-active");f=on.apply("slide-goto-keep-active",f,e);let{unsubscribe:t,getState:l,dispatch:m}=we.subscribe(a,"slider",function(o){!f||(i===o.current?e.classList.add("x-active"):e.classList.remove("x-active"))}),r=["effects",{key:"current",condition:[c+1,"=="],enter:"effect",exit:"effect"},"slider"];dn.update(e,o=>({...o,effectRivet:r}));let d=Ae(e);return[Se(e,d,o=>{m(u=>({...u,navigateTo:c}))},{passive:!0}),t,Xe([[e,...r]])]});function xe(e){let n=e;if(e.hasAttribute("data-x-slider-id")){let i=e.getAttribute("data-x-slider-id"),c=document.getElementById(i);c&&(n=c)}return n}function Ae(e){return e.getAttribute("data-x-slide-goto-trigger")||"click"}})();

# Copyright (C) 2025 cornerstone
# This file is distributed under the same license as the cornerstone package.
msgid ""
msgstr ""
"Project-Id-Version: cornerstone\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-KeywordsList: __;_e;_ex:1,2c;_n:1,2;_n_noop:1,2;_nx:1,2,4c;_nx_noop:1,2,3c;_x:1,2c;esc_attr__;esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: cornerstone/includes/config/keybindings.php:15, cornerstone/includes/i18n/app.php:111
msgid "Save"
msgstr ""

#: cornerstone/includes/config/keybindings.php:16, cornerstone/includes/i18n/app.php:143
msgid "Undo"
msgstr ""

#: cornerstone/includes/config/keybindings.php:17, cornerstone/includes/i18n/app.php:144
msgid "Redo"
msgstr ""

#: cornerstone/includes/config/keybindings.php:18
msgid "Reload Tab"
msgstr ""

#: cornerstone/includes/config/keybindings.php:19
msgid "Search Documents"
msgstr ""

#: cornerstone/includes/config/keybindings.php:20, cornerstone/includes/config/preference-controls.php:92, cornerstone/includes/elements/registry-setup.php:756, cornerstone/includes/i18n/app.php:214
msgid "Outline"
msgstr ""

#: cornerstone/includes/config/keybindings.php:21, cornerstone/includes/i18n/app.php:215
msgid "Inspector"
msgstr ""

#: cornerstone/includes/config/keybindings.php:22
msgid "Element Library"
msgstr ""

#: cornerstone/includes/config/keybindings.php:23, cornerstone/includes/i18n/admin.php:36, cornerstone/includes/i18n/app.php:217, cornerstone/includes/classes/Services/Admin.php:353
msgid "Settings"
msgstr ""

#: cornerstone/includes/config/keybindings.php:24, cornerstone/includes/i18n/admin.php:66, cornerstone/includes/i18n/app.php:237, cornerstone/includes/i18n/common.php:28
msgid "Theme Options"
msgstr ""

#: cornerstone/includes/config/keybindings.php:25
msgid "Open Fonts "
msgstr ""

#: cornerstone/includes/config/keybindings.php:26
msgid "Open Colors "
msgstr ""

#: cornerstone/includes/config/keybindings.php:27
msgid "Hide/Show UI"
msgstr ""

#: cornerstone/includes/config/keybindings.php:28
msgid "Inspector Breakout Mode"
msgstr ""

#: cornerstone/includes/config/keybindings.php:29
msgid "Delete Element"
msgstr ""

#: cornerstone/includes/config/keybindings.php:30
msgid "Duplicate Element"
msgstr ""

#: cornerstone/includes/config/keybindings.php:31
msgid "Copy Element"
msgstr ""

#: cornerstone/includes/config/keybindings.php:32
msgid "Paste Element"
msgstr ""

#: cornerstone/includes/config/keybindings.php:33
msgid "Paste Element Style"
msgstr ""

#: cornerstone/includes/config/keybindings.php:34
msgid "Find (focus available search)"
msgstr ""

#: cornerstone/includes/config/keybindings.php:35
msgid "Close Open Window"
msgstr ""

#: cornerstone/includes/config/keybindings.php:38
msgid "Open Element Manager"
msgstr ""

#: cornerstone/includes/config/keybindings.php:39
msgid "Open Dev Toolkit"
msgstr ""

#: cornerstone/includes/config/keybindings.php:40
msgid "Open Code Editors"
msgstr ""

#: cornerstone/includes/config/keybindings.php:41
msgid "Open Max"
msgstr ""

#: cornerstone/includes/config/keybindings.php:43
msgid "Open Global Parameters"
msgstr ""

#: cornerstone/includes/config/keybindings.php:44
msgid "Open Element Parameters"
msgstr ""

#: cornerstone/includes/config/keybindings.php:46
msgid "Open Action History"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:6, cornerstone/includes/elements/registry-setup.php:377, cornerstone/includes/i18n/app.php:1134
msgid "Dynamic Content"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:7
msgid "Show controls to open Dynamic Content wherever it is supported."
msgstr ""

#: cornerstone/includes/config/preference-controls.php:11
msgid "WordPress Toolbar"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:12
msgid "Allow WordPress to display the toolbar above the app. Requires a page refresh to take effect."
msgstr ""

#: cornerstone/includes/config/preference-controls.php:16
msgid "Context Menu"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:17
msgid "Allow context menu to appear when alt-clicking in the live preview."
msgstr ""

#: cornerstone/includes/config/preference-controls.php:21
msgid "Code Editors"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:22
msgid "Add custom CSS and JavaScript to your documents or entire site"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:26
msgid "Dev Toolkit"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:27
msgid "Experimental functionality used by Themeco developers. Use at your own risk."
msgstr ""

#: cornerstone/includes/config/preference-controls.php:33
msgid "Show All Documents"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:34
msgid "If enabled, this will show all documents created regardless of if they are Cornerstone Pages"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:39
msgid "Expanded Font Family"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:40
msgid "The font family display text will sample text or it will display the sample preview as the font family name"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:48
msgid "Enable Max"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:50
msgid "The Best Templates & Training Right In Your Builder."
msgstr ""

#: cornerstone/includes/config/preference-controls.php:60
msgid "Interface"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:65, cornerstone/includes/integration/codemirror.php:50
msgid "Theme"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:66
msgid "Select how you would like the application UI to appear."
msgstr ""

#: cornerstone/includes/config/preference-controls.php:70, cornerstone/includes/integration/theme-color-header.php:23, cornerstone/includes/classes/Services/FontAwesome.php:107
msgid "Light"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:71, cornerstone/includes/integration/theme-color-header.php:28
msgid "Dark"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:78
msgid "Workspace"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:79
msgid "Decide which side of the screen you prefer the workspace"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:83, cornerstone/includes/elements/registry-setup.php:596, cornerstone/includes/i18n/app.php:420, cornerstone/includes/i18n/app.php:458, cornerstone/includes/i18n/app.php:1071, cornerstone/includes/_classes/classic/class-control-mixins.php:259, cornerstone/includes/_classes/classic/class-control-mixins.php:662, cornerstone/includes/elements/classic/column/controls.php:39, cornerstone/includes/elements/classic/_alternate/blockquote.php:51, cornerstone/includes/elements/classic/_alternate/button.php:161, cornerstone/includes/elements/classic/_alternate/callout.php:76, cornerstone/includes/elements/classic/_alternate/feature-box.php:276, cornerstone/includes/elements/classic/_alternate/feature-list.php:97, cornerstone/includes/elements/classic/_alternate/image.php:129, cornerstone/includes/elements/classic/_alternate/prompt.php:77, cornerstone/includes/elements/classic/_alternate/tabs.php:46
msgid "Left"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:84, cornerstone/includes/elements/registry-setup.php:851, cornerstone/includes/i18n/app.php:417, cornerstone/includes/i18n/app.php:460, cornerstone/includes/i18n/app.php:1072, cornerstone/includes/_classes/classic/class-control-mixins.php:261, cornerstone/includes/_classes/classic/class-control-mixins.php:664, cornerstone/includes/elements/classic/column/controls.php:37, cornerstone/includes/elements/classic/_alternate/blockquote.php:53, cornerstone/includes/elements/classic/_alternate/button.php:159, cornerstone/includes/elements/classic/_alternate/callout.php:78, cornerstone/includes/elements/classic/_alternate/feature-box.php:278, cornerstone/includes/elements/classic/_alternate/feature-list.php:98, cornerstone/includes/elements/classic/_alternate/image.php:127, cornerstone/includes/elements/classic/_alternate/prompt.php:78, cornerstone/includes/elements/classic/_alternate/tabs.php:47
msgid "Right"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:93
msgid "Display the Outline and Settings tabs opposite of Workspace panel"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:97
msgid "Nested"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:98
msgid "Split"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:99
msgid "Adjacent"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:100, cornerstone/includes/elements/registry-setup.php:684, cornerstone/includes/views/partials/modal.php:33
msgid "Modal"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:109
msgid "Status<br/>Indicators"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:110
msgid "Select the contexts where you would like to see element status indicators."
msgstr ""

#: cornerstone/includes/config/preference-controls.php:114
msgid "Workspace and Breadcrumbs"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:115
msgid "Breadcrumbs Only"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:116
msgid "Workspace Only"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:117, cornerstone/includes/elements/registry-setup.php:723, cornerstone/includes/i18n/app.php:324
msgid "Off"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:126
msgid "Default Element"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:127
msgid "When inserting an element, this is the default element to use"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:130, cornerstone/includes/elements/registry-setup.php:894, cornerstone/includes/elements/definitions/section.php:345, cornerstone/includes/elements/classic/section/definition.php:11
msgid "Section"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:131, cornerstone/includes/elements/registry-setup.php:855, cornerstone/includes/i18n/app.php:505, cornerstone/includes/elements/definitions/layout-row.php:496
msgid "Row"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:132, cornerstone/includes/elements/registry-setup.php:367, cornerstone/includes/i18n/app.php:1141, cornerstone/includes/elements/definitions/layout-div.php:508
msgid "Div"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:141
msgid "Open Library Default"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:142
msgid "Instead of using the default element, open the Element Library whenever you click on the insert areas. This can also be controlled through CTRL / MOD (Mac) to open the library. When this preference is enabled, this reverses how the modifier works"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:149
msgid "Preference Toolbar"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:150
msgid "Display the preference window button in the App toolbar"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:157
msgid "CS Favicon"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:158
msgid "Use the Cornerstone icon as the favicon when you are viewing this App. Requires reload of Cornerstone"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:164
msgid "Functionality"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:177
msgid "Workflow"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:186
msgid "Inset Preview"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:190
msgid "Rich Text Editor Default"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:191
msgid "By default, start text editors in rich text mode whenever possible."
msgstr ""

#: cornerstone/includes/config/preference-controls.php:195
msgid "Preserve Inspector Group"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:196
msgid "When navigating between elements this will keep the same group open if it exists on the subsequent element. Hold cmd/ctrl to invert."
msgstr ""

#: cornerstone/includes/config/preference-controls.php:200
msgid "Show Element Icons"
msgstr ""

#: cornerstone/includes/config/preference-controls.php:201
msgid "If enabled, the icon of the elements will display in the Element Library and Outline"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:87, cornerstone/includes/elements/registry-setup.php:517, cornerstone/includes/i18n/admin.php:34, cornerstone/includes/i18n/app.php:211, cornerstone/includes/loopers/breadcrumbs.php:14, cornerstone/includes/classes/Services/Breadcrumbs.php:35, cornerstone/includes/elements/definitions/breadcrumbs.php:25
msgid "Home"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:88, cornerstone/includes/classes/Services/Breadcrumbs.php:39, cornerstone/includes/classes/Services/Conditionals.php:443, cornerstone/includes/classes/Services/Conditionals.php:876
msgid "Blog"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:89
msgid "Work"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:90
msgid "Project 1"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:91
msgid "Project 2"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:92, cornerstone/includes/i18n/app.php:701
msgid "About"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:93
msgid "The End"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:94
msgid "Project 3"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:95, cornerstone/includes/elements/prefab-elements.php:1160, cornerstone/includes/elements/registry-setup.php:905, cornerstone/includes/integration/woocommerce.php:279, cornerstone/includes/integration/woocommerce.php:559
msgid "Shop"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:96
msgid "Contact"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:97
msgid "Start Here"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:98
msgid "See Projects"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:99
msgid "An Illustrative Blurb"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:100
msgid "A Descriptive Line"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:101
msgid "Learn More"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:102
msgid "No More to See Here"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:103
msgid "More Expressive Text"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:104
msgid "Buy Stuff"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:105
msgid "Reach Out"
msgstr ""

#: cornerstone/includes/config/sample-nav.php:106
msgid "Read Things"
msgstr ""

#: cornerstone/includes/dynamiccontent/cookies.php:22, cornerstone/includes/dynamiccontent/cookies.php:39, cornerstone/includes/elements/registry-setup.php:698, cornerstone/includes/extend/custom-sidebars.php:352, cornerstone/includes/i18n/app.php:122, cornerstone/includes/i18n/app.php:915, cornerstone/includes/classes/Documents/Component.php:328, cornerstone/includes/classes/Documents/GlobalBlock.php:125, cornerstone/includes/integration/Api/ApiThemeOptions.php:23
msgid "Name"
msgstr ""

#: cornerstone/includes/dynamiccontent/cookies.php:46, cornerstone/includes/elements/registry-setup.php:1064
msgid "Value"
msgstr ""

#: cornerstone/includes/dynamiccontent/cookies.php:53
msgid "Expires"
msgstr ""

#: cornerstone/includes/dynamiccontent/cookies.php:60, cornerstone/includes/i18n/app.php:957, cornerstone/includes/integration/Api/ApiControls.php:171
msgid "Path"
msgstr ""

#: cornerstone/includes/dynamiccontent/cookies.php:70
msgid "Domain"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:64
msgid "Article"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:65
msgid "Aside"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:66, cornerstone/includes/elements/registry-setup.php:182, cornerstone/includes/i18n/app.php:937, cornerstone/includes/classes/Services/Conditionals.php:249, cornerstone/includes/elements/classic/_alternate/author.php:8
msgid "Author"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:67, cornerstone/includes/elements/registry-setup.php:287, cornerstone/includes/classes/Services/Conditionals.php:776
msgid "Comments"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:68, cornerstone/includes/elements/registry-setup.php:301, cornerstone/includes/i18n/app.php:311, cornerstone/includes/i18n/app.php:443, cornerstone/includes/i18n/app.php:518, cornerstone/includes/i18n/app.php:651, cornerstone/includes/i18n/app.php:661, cornerstone/includes/i18n/common.php:43, cornerstone/includes/classes/Services/ElementLibrary.php:26, cornerstone/includes/elements/definitions/form-integration.php:284, cornerstone/includes/integration/csv/controls.php:51, cornerstone/includes/elements/classic/alert/controls.php:22, cornerstone/includes/elements/classic/block-grid-item/controls.php:18, cornerstone/includes/elements/classic/pricing-table-column/controls.php:18, cornerstone/includes/elements/classic/_alternate/accordion-item.php:33, cornerstone/includes/elements/classic/_alternate/card.php:76, cornerstone/includes/elements/classic/_alternate/card.php:237, cornerstone/includes/elements/classic/_alternate/code.php:24, cornerstone/includes/elements/classic/_alternate/columnize.php:25, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:37, cornerstone/includes/elements/classic/_alternate/promo.php:25, cornerstone/includes/elements/classic/_alternate/prompt.php:34, cornerstone/includes/elements/classic/_alternate/prompt.php:38, cornerstone/includes/elements/classic/_alternate/protect.php:37, cornerstone/includes/elements/classic/_alternate/raw-content.php:26, cornerstone/includes/elements/classic/_alternate/slide.php:32, cornerstone/includes/elements/classic/_alternate/tab.php:32
msgid "Content"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:69, cornerstone/includes/elements/registry-setup.php:332, cornerstone/includes/i18n/app.php:926
msgid "Count"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:70, cornerstone/includes/elements/registry-setup.php:346
msgid "Date"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:71
msgid "Featured image for “{{dc:post:title}}”"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:72
msgid "Divider"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:73, cornerstone/includes/classes/Documents/Content.php:577, cornerstone/includes/classes/Documents/Layout.php:135, cornerstone/includes/classes/Services/Conditionals.php:680
msgid "Featured Image"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:74, cornerstone/includes/integration/woocommerce.php:230
msgid "Featured"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:75
msgid "Figure"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:76
msgid "Gravatar"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:77
msgid "Intro"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:78
msgid "Last Divider"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:79
msgid "Leave a Comment"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:80, cornerstone/includes/elements/registry-setup.php:612, cornerstone/includes/i18n/app.php:488, cornerstone/includes/i18n/app.php:532, cornerstone/includes/i18n/app.php:794, cornerstone/includes/elements/classic/icon-list-item/controls.php:39, cornerstone/includes/elements/classic/_alternate/creative-cta.php:132, cornerstone/includes/elements/classic/_alternate/image.php:54
msgid "Link"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:81
msgid "List"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:82, cornerstone/includes/elements/registry-setup.php:603
msgid "List Item"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:83
msgid "Main Post"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:84
msgid "Mega Menu"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:85
msgid "Meta Line"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:86
msgid "More Posts"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:87
msgid "2+ Comments"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:88
msgid "Negative Offset"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:89
msgid "0 Comments"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:90
msgid "Not Last Divider"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:91
msgid "1 Comment"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:92, cornerstone/includes/elements/registry-setup.php:794
msgid "Posts"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:93
msgid "Published"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:94, cornerstone/includes/i18n/app.php:920
msgid "Meta"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:95, cornerstone/includes/i18n/app.php:930, cornerstone/includes/classes/Services/ElementLibrary.php:34
msgid "Post"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:96, cornerstone/includes/i18n/app.php:932
msgid "Term"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:97
msgid "Technique"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:98, cornerstone/includes/elements/prefab-elements.php:1200
msgid "Terms"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:99, cornerstone/includes/elements/registry-setup.php:992, cornerstone/includes/i18n/admin.php:15, cornerstone/includes/i18n/app.php:464, cornerstone/includes/elements/definitions/text.php:74, cornerstone/includes/elements/classic/text/definition.php:11, cornerstone/includes/elements/classic/_alternate/button.php:24, cornerstone/includes/elements/classic/_alternate/creative-cta.php:32, cornerstone/includes/elements/classic/_alternate/custom-headline.php:34, cornerstone/includes/elements/classic/_alternate/feature-headline.php:34, cornerstone/includes/elements/classic/_alternate/google-map-marker.php:47
msgid "Text"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:100
msgid "The Author"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:101, cornerstone/includes/elements/registry-setup.php:1007, cornerstone/includes/elements/definitions/the-content.php:73
msgid "The Content"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:102, cornerstone/includes/elements/registry-setup.php:1008
msgid "The Excerpt"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:103
msgid "The Title"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:104, cornerstone/includes/elements/registry-setup.php:1016, cornerstone/includes/i18n/app.php:85, cornerstone/includes/i18n/app.php:399, cornerstone/includes/i18n/app.php:442, cornerstone/includes/i18n/app.php:531, cornerstone/includes/i18n/app.php:914, cornerstone/includes/classes/Documents/Component.php:324, cornerstone/includes/classes/Documents/Content.php:508, cornerstone/includes/classes/Documents/GlobalBlock.php:120, cornerstone/includes/classes/Documents/Layout.php:156, cornerstone/includes/elements/definitions/form-integration.php:233, cornerstone/includes/elements/classic/_alternate/author.php:24, cornerstone/includes/elements/classic/_alternate/callout.php:26, cornerstone/includes/elements/classic/_alternate/feature-box.php:26, cornerstone/includes/elements/classic/_alternate/social-sharing.php:22
msgid "Title"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:120, cornerstone/includes/i18n/app.php:940, cornerstone/includes/integration/woocommerce.php:212
msgid "WooCommerce"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:124
msgid "BuddyPress"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:128
msgid "bbPress"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:136
msgid "Link Box"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:146
msgid "H Flex"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:158
msgid "V Flex"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:170
msgid "Div (Global Margin)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:183, cornerstone/includes/i18n/app.php:945
msgid "Site Title"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:192, cornerstone/includes/i18n/app.php:946
msgid "Site Tagline"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:201
msgid "Site Home Link"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:211
msgid "Site Admin Link"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:229
msgid "Archive Title"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:242
msgid "Archive Description"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:255
msgid "Archive Link"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:293
msgid "No excerpt"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:315
msgid "Image (Full Width)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:328
msgid "Author (Vertical)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:415
msgid "Author (Horizontal)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:719
msgid "Terms (Cloud)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:786
msgid "Terms (Minimal)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:890
msgid "Terms (Column)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1020
msgid "Add to Cart Button"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1045
msgid "Shop Title"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1056, cornerstone/includes/i18n/app.php:994
msgid "Product Title"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1067
msgid "Product Long Description"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1074, cornerstone/includes/i18n/app.php:996
msgid "Product Short Description"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1083, cornerstone/includes/i18n/app.php:1005
msgid "Product Additional Information"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1092, cornerstone/includes/i18n/app.php:1006
msgid "Product Reviews"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1101, cornerstone/includes/i18n/app.php:997
msgid "Product Image"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1110, cornerstone/includes/i18n/app.php:986
msgid "Product Price"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1121
msgid "Product Rating"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1134
msgid "Cart Total"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1144
msgid "Cart Items"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1158
msgid "Shop Link"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1168
msgid "Cart Link"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1170, cornerstone/includes/elements/registry-setup.php:251, cornerstone/includes/integration/woocommerce.php:290, cornerstone/includes/integration/woocommerce.php:437, cornerstone/includes/integration/woocommerce.php:533
msgid "Cart"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1178
msgid "Checkout Link"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1180, cornerstone/includes/integration/woocommerce.php:301, cornerstone/includes/integration/woocommerce.php:448, cornerstone/includes/integration/woocommerce.php:539
msgid "Checkout"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1188
msgid "Account Link"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1190, cornerstone/includes/integration/woocommerce.php:312, cornerstone/includes/integration/woocommerce.php:459, cornerstone/includes/integration/woocommerce.php:545
msgid "Account"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1198
msgid "Terms Link"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1212
msgid "Product Tabs"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1217, cornerstone/includes/extend/custom-sidebars.php:353, cornerstone/includes/i18n/app.php:917, cornerstone/includes/elements/definitions/form-integration.php:234
msgid "Description"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1222
msgid "Additional Information"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1227
msgid "Reviews ({{dc:woocommerce:product_review_count}})"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1247
msgid "Posts (Tiles)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1401
msgid "Posts (Minimal)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1563
msgid "Posts (List)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:1698
msgid "Posts (Magazine)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2031
msgid "Looper List (Baseline)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2121
msgid "Looper List (Centered)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2219
msgid "Static List (Baseline)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2468
msgid "Static List (Centered)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2766, cornerstone/includes/elements/registry-setup.php:704, cornerstone/includes/elements/definitions/deprecated-nav-modal.php:132
msgid "Navigation Modal"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2808, cornerstone/includes/elements/registry-setup.php:705
msgid "Navigation Off Canvas"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2848
msgid "Search Dropdown"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2891
msgid "Search Modal"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2939
msgid "Your Items"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2956
msgid "Cart Dropdown"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2977
msgid "Cart Modal"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:2996
msgid "Cart Off Canvas"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3115
msgid "Rooks"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3115
msgid "Unsung heroes of the game"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3116
msgid "Bishops"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3116
msgid "Simply cannot walk straight"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3143
msgid "Mega Menu Dropdown"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3166
msgid "Mega Menu Modal"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3190
msgid "Mega Menu Off Canvas"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3341
msgid "Slide Navigation"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3419
msgid "Slider (Inline)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3476
msgid "Slider (Stacked)"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3517
msgid "Submit Button"
msgstr ""

#: cornerstone/includes/elements/prefab-elements.php:3519, cornerstone/includes/elements/registry-setup.php:970, cornerstone/includes/elements/definitions/comment-form.php:28, cornerstone/includes/views/partials/search.php:91
msgid "Submit"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:130
msgid "1st"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:131
msgid "2nd"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:132
msgid "3rd"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:133
msgid "Abbr"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:134
msgid "Above"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:135
msgid "Above & Below"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:136
msgid "Above & Below Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:137
msgid "Absolute"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:138, cornerstone/includes/elements/definitions/accordion.php:605, cornerstone/includes/elements/classic/_alternate/accordion.php:8
msgid "Accordion"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:139, cornerstone/includes/elements/definitions/accordion-item.php:24, cornerstone/includes/elements/classic/_alternate/accordion-item.php:8
msgid "Accordion Item"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:140, cornerstone/includes/elements/definitions/accordion.php:618, cornerstone/includes/elements/classic/_alternate/accordion.php:24
msgid "Accordion Item 1"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:141, cornerstone/includes/elements/definitions/accordion.php:619, cornerstone/includes/elements/classic/_alternate/accordion.php:25
msgid "Accordion Item 2"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:142
msgid "Active"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:143
msgid "Active Links"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:144
msgid "Adaptive"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:145, cornerstone/includes/i18n/app.php:93
msgid "Add"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:146
msgid "Add Items"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:147, cornerstone/includes/elements/definitions/tp-wc-add-to-cart-form.php:117
msgid "Add to Cart Form"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:148, cornerstone/includes/i18n/admin.php:111, cornerstone/includes/i18n/app.php:450, cornerstone/includes/classes/Services/ElementLibrary.php:37
msgid "Advanced"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:149, cornerstone/includes/elements/classic/_alternate/button.php:118
msgid "After"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:150, cornerstone/includes/elements/definitions/alert.php:280, cornerstone/includes/elements/classic/alert/definition.php:11
msgid "Alert"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:151, cornerstone/includes/i18n/app.php:457
msgid "Align"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:152
msgid "Align Horizontal"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:153
msgid "Align Self"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:154
msgid "Align Vertical"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:155, cornerstone/includes/i18n/common.php:76, cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:89, cornerstone/includes/elements/classic/_alternate/blockquote.php:45, cornerstone/includes/elements/classic/_alternate/callout.php:71, cornerstone/includes/elements/classic/_alternate/prompt.php:71
msgid "Alignment"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:156, cornerstone/includes/extend/portfolio.php:374, cornerstone/includes/extend/portfolio.php:357, cornerstone/includes/i18n/app.php:647
msgid "All"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:157
msgid "All Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:158
msgid "All Terms"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:159
msgid "Allow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:160, cornerstone/includes/i18n/app.php:485, cornerstone/includes/elements/classic/_alternate/promo.php:42
msgid "Alt Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:161
msgid "Always Show"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:162
msgid "Anchor"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:163
msgid "Anchors"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:164
msgid "Ancestors"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:165, cornerstone/includes/_classes/classic/class-control-mixins.php:274, cornerstone/includes/_classes/classic/class-control-mixins.php:793, cornerstone/includes/elements/classic/_alternate/creative-cta.php:115, cornerstone/includes/elements/classic/_alternate/slider.php:37
msgid "Animation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:166
msgid "Animation Delay"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:167
msgid "Animation Frames"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:168
msgid "Speed Multiplier"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:169
msgid "Animation Transition"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:170, cornerstone/includes/i18n/app.php:95, cornerstone/includes/classes/Services/Conditionals.php:492, cornerstone/includes/classes/Services/Conditionals.php:914
msgid "Any"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:171
msgid "API Key"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:172
msgid "Application"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:173
msgid "ARIA Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:174
msgid "ARIA Hidden"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:175
msgid "Array"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:176
msgid "Asc"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:177, cornerstone/includes/i18n/app.php:153
msgid "Ascending"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:178, cornerstone/includes/elements/control-partials/aspect-ratio.php:9, cornerstone/includes/elements/classic/_alternate/embedded-video.php:30, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:58
msgid "Aspect Ratio"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:179
msgid "At Edges"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:180
msgid "Attachment"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:181, cornerstone/includes/elements/definitions/audio.php:316
msgid "Audio"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:183
msgid "Author<br/>Name"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:184
msgid "Author<br/>Review"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:185
msgid "Authors"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:186, cornerstone/includes/i18n/app.php:343, cornerstone/includes/i18n/app.php:448, cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:63, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:105
msgid "Auto"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:187
msgid "Auto Flow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:188, cornerstone/includes/i18n/app.php:452, cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:72, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:83
msgid "Autoplay"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:189
msgid "Avatar"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:190
msgid "Avatar Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:191
msgid "Avoid"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:192
msgid "Axis"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:193, cornerstone/includes/i18n/app.php:87
msgid "Back"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:194
msgid "Back Background Layers"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:195
msgid "Backface"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:196, cornerstone/includes/elements/classic/_alternate/card.php:296
msgid "Back Button"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:197, cornerstone/includes/elements/classic/_alternate/card.php:233
msgid "Back Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:198
msgid "Back Delay"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:199
msgid "Back Icon"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:200
msgid "Back Label"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:201
msgid "Back Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:202
msgid "Back Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:203
msgid "Back Speed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:204
msgid "Backdrop"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:205
msgid "Backdrop & Close"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:206
msgid "Backdrop Filter"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:207, cornerstone/includes/i18n/app.php:535, cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:70
msgid "Background"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:208, cornerstone/includes/elements/classic/section/controls.php:66
msgid "Background Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:209
msgid "Background Layers"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:210, cornerstone/includes/elements/definitions-pro/bar.php:765
msgid "Bar"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:211, cornerstone/includes/i18n/app.php:382, plugins/cornerstone-charts/extension/Controls/ChartColorOptions.php:12
msgid "Base"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:212
msgid "Base Font Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:213, cornerstone/includes/i18n/app.php:341
msgid "Baseline"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:214
msgid "Base Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:215
msgid "Base Typography"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:216, cornerstone/includes/elements/classic/_alternate/button.php:117
msgid "Before"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:217
msgid "Behavior"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:218
msgid "{{prefix}} Behavior"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:219
msgid "Below"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:220
msgid "Blend"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:221, cornerstone/includes/elements/classic/_alternate/button.php:86
msgid "Block"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:222, cornerstone/includes/i18n/app.php:407
msgid "Blur"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:223
msgid "Body"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:224
msgid "Body Scroll"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:225, cornerstone/includes/_classes/classic/class-control-mixins.php:696
msgid "Border"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:226
msgid "{{prefix}} Border"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:227
msgid "Border Radius"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:228
msgid "{{prefix}} Border Radius"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:229
msgid "Both"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:230, cornerstone/includes/i18n/app.php:418, cornerstone/includes/i18n/app.php:1073, cornerstone/includes/elements/classic/column/controls.php:38, cornerstone/includes/elements/classic/_alternate/button.php:160, cornerstone/includes/elements/classic/_alternate/image.php:128
msgid "Bottom"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:231, cornerstone/includes/i18n/app.php:425
msgid "Bottom Left"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:232
msgid "Bottom Offset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:233, cornerstone/includes/i18n/app.php:424
msgid "Bottom Right"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:234
msgid "Box Shadow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:235
msgid "{{prefix}} Box Shadow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:236, cornerstone/includes/loopers/breadcrumbs.php:11, cornerstone/includes/elements/definitions/breadcrumbs.php:513
msgid "Breadcrumbs"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:237
msgid "Breakpoint to Hide #"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:238
msgid "Breakpoints"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:239
msgid "Brief"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:240
msgid "Bttm"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:241
msgid "Burger"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:242, cornerstone/includes/elements/definitions/button.php:85, cornerstone/includes/elements/classic/_alternate/button.php:8
msgid "Button"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:243
msgid "Button Navigation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:244
msgid "Buttons"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:245
msgid "Buttons Container"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:246
msgid "Buttons Placement"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:247, cornerstone/includes/elements/definitions/comment-form.php:27
msgid "Cancel Reply"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:248
msgid "Cancel Reply Link"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:249, cornerstone/includes/elements/definitions/card.php:350, cornerstone/includes/elements/classic/_alternate/card.php:8
msgid "Card"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:250
msgid "Carousel"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:252, cornerstone/includes/elements/definitions-pro/layout-cell.php:354
msgid "Cell"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:253
msgid "Cells"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:254, cornerstone/includes/i18n/app.php:335, cornerstone/includes/i18n/app.php:459, cornerstone/includes/_classes/classic/class-control-mixins.php:260, cornerstone/includes/_classes/classic/class-control-mixins.php:663, cornerstone/includes/elements/classic/_alternate/blockquote.php:52, cornerstone/includes/elements/classic/_alternate/callout.php:77, cornerstone/includes/elements/classic/_alternate/feature-box.php:277
msgid "Center"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:255
msgid "Center At"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:256
msgid "Centered"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:257
msgid "Character Speed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:258
msgid "Checkbox"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:259
msgid "Checkboxes & Radios"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:260
msgid "Children"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:261, cornerstone/includes/elements/classic/_alternate/feature-box.php:167, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:133, cornerstone/includes/elements/classic/_alternate/image.php:30
msgid "Circle"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:262
msgid "Cite"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:263, cornerstone/includes/elements/classic/_alternate/blockquote.php:37
msgid "Citation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:264, cornerstone/includes/i18n/app.php:370, cornerstone/includes/_classes/classic/class-control-mixins.php:113, cornerstone/includes/_classes/classic/class-control-mixins.php:610
msgid "Class"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:265, cornerstone/includes/views/partials/search.php:92, cornerstone/includes/elements/classic/_alternate/clear.php:8
msgid "Clear"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:266
msgid "Clear Placement"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:267, cornerstone/includes/i18n/app.php:441, cornerstone/includes/elements/classic/_alternate/button.php:178, cornerstone/includes/elements/classic/_alternate/image.php:147
msgid "Click"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:268, cornerstone/includes/i18n/admin.php:55, cornerstone/includes/i18n/app.php:183
msgid "Close"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:269
msgid "Closest Corner"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:270
msgid "Closest Side"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:271, cornerstone/includes/classes/Services/Conditionals.php:781
msgid "Closed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:272
msgid "Close Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:273
msgid "Closing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:274
msgid "Closing Mark Align"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:275
msgid "Code"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:276, cornerstone/includes/i18n/app.php:406, cornerstone/includes/i18n/app.php:465, cornerstone/includes/i18n/app.php:787, cornerstone/includes/elements/classic/section/controls.php:35, cornerstone/includes/elements/classic/_alternate/line.php:20
msgid "Color"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:277
msgid "Color Burn"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:278
msgid "Color Dodge"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:279, cornerstone/includes/i18n/admin.php:64, cornerstone/includes/i18n/common.php:30
msgid "Colors"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:280, cornerstone/includes/i18n/app.php:506, cornerstone/includes/elements/definitions/layout-column.php:329, cornerstone/includes/elements/classic/pricing-table/controls.php:16
msgid "Column"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:281
msgid "Column Fill"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:282, cornerstone/includes/i18n/app.php:260, cornerstone/includes/elements/classic/block-grid/controls.php:32, cornerstone/includes/elements/classic/row/controls.php:14
msgid "Columns"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:283
msgid "{{prefix}} Columns"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:284, cornerstone/includes/elements/definitions/comment-form.php:258
msgid "Comment Form"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:285, cornerstone/includes/elements/definitions/comment-list.php:291
msgid "Comment List"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:286, cornerstone/includes/elements/definitions/comment-pagination.php:79
msgid "Comment Pagination"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:288
msgid "Comments Closed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:289
msgid "Compact"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:290
msgid "Composite"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:291
msgid "Complete"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:292
msgid "Complete Message"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:293
msgid "Condition"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:294, cornerstone/includes/i18n/app.php:376, cornerstone/includes/i18n/app.php:1130, cornerstone/includes/classes/Documents/Layout.php:169
msgid "Conditions"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:295
msgid "Config"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:296
msgid "Configuration"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:297, cornerstone/includes/i18n/app.php:494
msgid "Contain"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:298, cornerstone/includes/elements/definitions-pro/container.php:243
msgid "Container"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:299
msgid "Containers"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:300
msgid "Contain At"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:302
msgid "Content Alignment"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:303
msgid "Content Area"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:304
msgid "Content Break"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:305
msgid "Content End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:306
msgid "Content Height"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:307
msgid "Content Length"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:308
msgid "Content Lists"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:309
msgid "Content Max Length"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:310
msgid "Content Scrolling"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:311
msgid "Content Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:312
msgid "Content Spacing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:313
msgid "Content Sizing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:314
msgid "{{prefix}} Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:315
msgid "%s Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:316
msgid "Content X"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:317
msgid "Content Y"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:318
msgid "Controls"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:319
msgid "{{prefix}} Controls Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:320
msgid "{{prefix}} Controls Colors"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:321, cornerstone/includes/i18n/admin.php:87, cornerstone/includes/i18n/app.php:72
msgid "Copy"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:322, cornerstone/includes/i18n/app.php:499
msgid "Cover"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:323, cornerstone/includes/elements/definitions/creative-cta.php:111, cornerstone/includes/elements/classic/_alternate/creative-cta.php:8
msgid "Creative CTA"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:324
msgid "Crossfade"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:325, cornerstone/includes/elements/definitions/tp-wc-cross-sells.php:79, cornerstone/includes/integration/woocommerce/Loopers/CrossSell.php:11
msgid "Cross Sells"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:326
msgid "Current"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:327
msgid "Current Post Terms"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:328
msgid "Current Page Children"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:329, cornerstone/includes/elements/classic/_alternate/text-type.php:160
msgid "Cursor"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:330
msgid "Cursor Color"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:331, cornerstone/includes/i18n/app.php:810
msgid "Compress"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:333, cornerstone/includes/elements/definitions/countdown.php:615
msgid "Countdown"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:334
msgid "Countdown End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:335, cornerstone/includes/elements/definitions/counter.php:371, cornerstone/includes/elements/classic/_alternate/counter.php:8
msgid "Counter"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:336, cornerstone/includes/i18n/app.php:110, cornerstone/includes/i18n/common.php:44, cornerstone/includes/elements/control-partials/looper-provider.php:331
msgid "Custom"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:337, cornerstone/includes/i18n/app.php:1131
msgid "Custom Attributes"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:338
msgid "{{prefix}} Custom Attributes"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:339
msgid "Custom Colors"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:340
msgid "Custom Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:341
msgid "Custom Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:342, cornerstone/includes/i18n/app.php:313
msgid "Customize"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:343, cornerstone/includes/functions/helpers.php:1368
msgid "D"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:344
msgid "Darken"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:345, cornerstone/includes/i18n/app.php:348, cornerstone/includes/_classes/classic/class-control-mixins.php:159, cornerstone/includes/_classes/classic/class-control-mixins.php:703, cornerstone/includes/elements/classic/_alternate/feature-list.php:147
msgid "Dashed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:347, cornerstone/includes/functions/helpers.php:1376, cornerstone/includes/classes/Services/Conditionals.php:495, cornerstone/includes/classes/Services/Conditionals.php:917, cornerstone/includes/elements/control-partials/seconds-select.php:22
msgid "Day"
msgid_plural "Days"
msgstr[0] ""
msgstr[1] ""

#: cornerstone/includes/elements/registry-setup.php:348
msgid "Decimal"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:349
msgid "Decimal (Leading Zero)"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:350, cornerstone/includes/i18n/app.php:467
msgid "Decoration"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:351, cornerstone/includes/i18n/admin.php:31, cornerstone/includes/i18n/app.php:120, cornerstone/includes/classes/Controllers/Choices.php:178, cornerstone/includes/integration/QueryBuilder/Controls/MetaQuery.php:114
msgid "Default"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:352
msgid "Delay"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:353, cornerstone/includes/integration/csv/controls.php:22
msgid "Delimiter"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:354
msgid "Dense"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:355
msgid "Desc"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:356, cornerstone/includes/i18n/app.php:154
msgid "Descending"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:357, cornerstone/includes/i18n/app.php:486
msgid "Describe Your Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:358, cornerstone/includes/i18n/app.php:312
msgid "Design"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:359
msgid "Difference"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:360
msgid "Digit"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:361, cornerstone/includes/i18n/app.php:483, cornerstone/includes/integration/woocommerce.php:262
msgid "Dimensions"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:362, cornerstone/includes/i18n/app.php:504
msgid "Direction"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:363
msgid "Disable"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:364
msgid "Disable Preview"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:365
msgid "Disc"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:366
msgid "Display"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:368
msgid "Dots"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:369, cornerstone/includes/i18n/app.php:347, cornerstone/includes/_classes/classic/class-control-mixins.php:158, cornerstone/includes/_classes/classic/class-control-mixins.php:702, cornerstone/includes/elements/classic/_alternate/feature-list.php:148
msgid "Dotted"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:370, cornerstone/includes/i18n/app.php:349, cornerstone/includes/_classes/classic/class-control-mixins.php:160, cornerstone/includes/_classes/classic/class-control-mixins.php:704
msgid "Double"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:371
msgid "Down"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:372
msgid "Drag"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:373, cornerstone/includes/elements/classic/_alternate/google-map.php:75
msgid "Draggable"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:374
msgid "Dropdown"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:375
msgid "Dropdown Custom Attributes"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:376, cornerstone/includes/elements/classic/column/controls.php:60
msgid "Duration"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:378, cornerstone/includes/elements/control-partials/dynamic-rendering.php:12
msgid "Dynamic Rendering"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:379, cornerstone/includes/i18n/app.php:100, cornerstone/includes/i18n/app.php:373, cornerstone/includes/integration/Api/ApiJSON.php:40, cornerstone/includes/integration/Api/ApiRaw.php:15
msgid "Edit"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:380
msgid "Edit Mask"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:381
msgid "Effect"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:382
msgid "Effects"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:383, cornerstone/includes/i18n/app.php:270
msgid "Element"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:384, cornerstone/includes/i18n/app.php:377, cornerstone/includes/i18n/app.php:1132
msgid "Element CSS"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:385, cornerstone/includes/i18n/admin.php:62, cornerstone/includes/i18n/app.php:218, cornerstone/includes/i18n/app.php:271, cornerstone/includes/i18n/app.php:1040
msgid "Elements"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:386
msgid "Ellipse"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:387
msgid "Ellipsis"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:388, cornerstone/includes/i18n/app.php:523, cornerstone/includes/classes/Services/Social.php:30, cornerstone/includes/elements/classic/_alternate/social-sharing.php:78
msgid "Email"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:389, cornerstone/includes/elements/definitions/form-integration.php:274
msgid "Embed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:390, cornerstone/includes/classes/Services/Conditionals.php:794
msgid "Empty"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:391
msgid "Empty Icons"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:392
msgid "Empty Terms"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:393
msgid "Enable"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:394
msgid "Enable Close Button"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:395
msgid "Enable Multi-Column Layout"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:396
msgid "Enable Secondary"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:397, cornerstone/includes/i18n/app.php:336, cornerstone/includes/i18n/app.php:337
msgid "End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:398
msgid "End & Mid<br/># Count"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:399
msgid "End Date"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:400
msgid "End # Count"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:401
msgid "End Rotation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:402
msgid "Ending Delay"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:403
msgid "Enter"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:404
msgid "Entrance"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:405
msgid "Equal Height"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:406
msgid "Exclude"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:407
msgid "Exclusion"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:408, cornerstone/includes/i18n/app.php:62
msgid "Exit"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:409
msgid "Exit Pointer Events"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:410, cornerstone/includes/elements/classic/_alternate/social-sharing.php:38
msgid "Facebook"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:411, cornerstone/includes/elements/classic/_alternate/slider.php:42
msgid "Fade"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:412
msgid "Fade Stop"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:413
msgid "Farthest Corner"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:414
msgid "Farthest Side"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:415
msgid "Family"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:416, cornerstone/includes/i18n/app.php:913, cornerstone/includes/i18n/app.php:1017
msgid "Field"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:417, cornerstone/includes/i18n/app.php:500
msgid "Fill"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:418, cornerstone/includes/i18n/app.php:328
msgid "Fill Space"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:419
msgid "Filter"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:420
msgid "First Dropdown"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:421
msgid "Fixed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:422
msgid "Fixed Height"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:423
msgid "Flex"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:424
msgid "Flexbox"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:425
msgid "{{prefix}} Flexbox"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:426
msgid "Flex End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:427
msgid "Flex Start"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:428
msgid "{{prefix}} Flex"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:429, cornerstone/includes/elements/classic/_alternate/card.php:26
msgid "Flip Direction"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:430
msgid "Flip X"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:431
msgid "Flip Y"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:432, cornerstone/includes/i18n/app.php:471, cornerstone/includes/i18n/app.php:713
msgid "Font"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:433
msgid "Font Family"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:434
msgid "Font Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:435
msgid "Font Style"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:436
msgid "Font Weight"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:437, cornerstone/includes/i18n/app.php:660, cornerstone/includes/i18n/app.php:1074, cornerstone/includes/i18n/common.php:47, cornerstone/includes/classes/Documents/Content.php:619, cornerstone/includes/classes/Documents/ThemeLayout.php:32
msgid "Footer"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:438, cornerstone/includes/i18n/app.php:650, cornerstone/includes/i18n/common.php:48
msgid "Footers"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:439, cornerstone/includes/elements/definitions/form-integration.php:231, cornerstone/includes/integration/Forminator/Forminator.php:39
msgid "Form"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:440, plugins/cornerstone-ai/OpenAI/TextToSpeechControls.php:73
msgid "Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:441
msgid "Formation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:442
msgid "Forms"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:443
msgid "Forward Button"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:444
msgid "Forward Icon"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:445
msgid "Frame"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:446
msgid "Free"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:447
msgid "Free Scroll"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:448
msgid "Freeze Last Frame"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:449
msgid "Front"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:450
msgid "Front Background Layers"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:451, cornerstone/includes/elements/classic/_alternate/card.php:72
msgid "Front Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:452
msgid "Front Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:453
msgid "Front Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:454
msgid "Full"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:455
msgid "Fullwidth"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:456
msgid "Fwd"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:457
msgid "Fwd Delay"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:458
msgid "Fwd Speed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:459, cornerstone/includes/i18n/app.php:514, cornerstone/includes/elements/definitions/gap.php:121, cornerstone/includes/elements/classic/_alternate/gap.php:8
msgid "Gap"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:460
msgid "Gap Height"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:461
msgid "Gap Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:462
msgid "Gap Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:463, cornerstone/includes/i18n/app.php:362
msgid "Get Started"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:464
msgid "Global Container"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:465, cornerstone/includes/i18n/app.php:766
msgid "Google"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:466
msgid "Google Map Styles"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:467, cornerstone/includes/elements/classic/_alternate/creative-cta.php:48, cornerstone/includes/elements/classic/_alternate/feature-box.php:63, cornerstone/includes/elements/classic/_alternate/feature-list.php:64
msgid "Graphic"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:468
msgid "{{prefix}} Graphic"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:469
msgid "%s Graphic"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:470
msgid "{{prefix}} Graphic Icon Colors"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:471
msgid "{{prefix}} Graphic Icon"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:472
msgid "%s Graphic Icon"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:473
msgid "{{prefix}} Graphic Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:474
msgid "%s Graphic Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:475, cornerstone/includes/i18n/app.php:1142, cornerstone/includes/elements/definitions-pro/layout-grid.php:2727, cornerstone/includes/elements/definitions-pro/layout-grid.php:2781
msgid "Grid"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:476, cornerstone/includes/i18n/app.php:350, cornerstone/includes/_classes/classic/class-control-mixins.php:161, cornerstone/includes/_classes/classic/class-control-mixins.php:705
msgid "Groove"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:477, cornerstone/includes/i18n/app.php:166
msgid "Group"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:478, cornerstone/includes/classes/Documents/Component.php:338
msgid "Group Name"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:479
msgid "Groups"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:480
msgid "Grouped"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:481, cornerstone/includes/i18n/app.php:330
msgid "Grow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:482
msgid "Grow & Shrink"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:483
msgid "Grow Out"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:484
msgid "Gutters"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:485, cornerstone/includes/functions/helpers.php:1369, cornerstone/includes/i18n/app.php:491
msgid "H"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:486
msgid "H1"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:487
msgid "H1 Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:488
msgid "H2"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:489
msgid "H2 Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:490
msgid "H3"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:491
msgid "H3 Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:492
msgid "H4"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:493
msgid "H4 Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:494
msgid "H5"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:495
msgid "H5 Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:496
msgid "H6"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:497
msgid "H6 Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:498
msgid "H Top"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:499
msgid "H Bottom"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:500
msgid "Hard Light"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:501
msgid "Half Full"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:502, cornerstone/includes/i18n/app.php:659, cornerstone/includes/i18n/common.php:41, cornerstone/includes/classes/Documents/Content.php:612, cornerstone/includes/classes/Documents/ThemeLayout.php:28
msgid "Header"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:503
msgid "Header Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:504, cornerstone/includes/i18n/app.php:649, cornerstone/includes/i18n/common.php:42, cornerstone/includes/integration/Api/ApiControls.php:55
msgid "Headers"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:505, cornerstone/includes/elements/definitions/headline.php:113
msgid "Headline"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:506
msgid "Headlines"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:507
msgid "Headline Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:508
msgid "Headline Spacing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:509, cornerstone/includes/i18n/app.php:475, cornerstone/includes/elements/classic/_alternate/google-map.php:83, cornerstone/includes/elements/classic/_alternate/line.php:28
msgid "Height"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:510
msgid "Hero"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:511
msgid "Hide"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:512, cornerstone/includes/i18n/app.php:381, cornerstone/includes/i18n/app.php:1133
msgid "Hide During Breakpoints"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:513
msgid "Hide Empty"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:514
msgid "Hide Initially"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:515
msgid "Hide on End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:516
msgid "Hidden"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:518
msgid "Home Label"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:519
msgid "Home Link"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:520
msgid "Hook"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:521
msgid "Horiz"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:522, cornerstone/includes/i18n/app.php:512, cornerstone/includes/elements/classic/_alternate/recent-posts.php:83
msgid "Horizontal"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:523, cornerstone/includes/functions/helpers.php:1377, cornerstone/includes/elements/control-partials/seconds-select.php:19
msgid "Hour"
msgid_plural "Hours"
msgstr[0] ""
msgstr[1] ""

#: cornerstone/includes/elements/registry-setup.php:524, cornerstone/includes/i18n/app.php:383, cornerstone/includes/i18n/app.php:439, cornerstone/includes/elements/classic/_alternate/button.php:177, cornerstone/includes/elements/classic/_alternate/image.php:146
msgid "Hover"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:525
msgid "Hover Behavior"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:526, cornerstone/includes/i18n/app.php:462
msgid "HTML Tag"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:527
msgid "Hue"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:528, cornerstone/includes/elements/definitions/icon.php:83, cornerstone/includes/elements/classic/icon-list-item/controls.php:18, cornerstone/includes/elements/classic/_alternate/button.php:126, cornerstone/includes/elements/classic/_alternate/card.php:136, cornerstone/includes/elements/classic/_alternate/card.php:150, cornerstone/includes/elements/classic/_alternate/creative-cta.php:54, cornerstone/includes/elements/classic/_alternate/creative-cta.php:63, cornerstone/includes/elements/classic/_alternate/feature-box.php:68, cornerstone/includes/elements/classic/_alternate/feature-box.php:82, cornerstone/includes/elements/classic/_alternate/feature-headline.php:74, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:66, cornerstone/includes/elements/classic/_alternate/feature-list.php:69, cornerstone/includes/elements/classic/_alternate/icon.php:8, cornerstone/includes/elements/classic/_alternate/icon.php:21
msgid "Icon"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:529, cornerstone/includes/i18n/app.php:320, cornerstone/includes/elements/classic/icon-list/controls.php:12
msgid "Icons"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:530, cornerstone/includes/extend/custom-sidebars.php:354, cornerstone/includes/i18n/app.php:369, cornerstone/includes/i18n/app.php:924, cornerstone/includes/_classes/classic/class-control-mixins.php:100, cornerstone/includes/_classes/classic/class-control-mixins.php:598
msgid "ID"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:531
msgid "Ignore"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:532
msgid "{{prefix}} Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:533, cornerstone/includes/i18n/app.php:482, cornerstone/includes/integration/woocommerce.php:256, cornerstone/includes/elements/definitions/image.php:84, cornerstone/includes/elements/classic/section/controls.php:36, cornerstone/includes/elements/classic/_alternate/card.php:137, cornerstone/includes/elements/classic/_alternate/card.php:194, cornerstone/includes/elements/classic/_alternate/creative-cta.php:55, cornerstone/includes/elements/classic/_alternate/creative-cta.php:89, cornerstone/includes/elements/classic/_alternate/feature-box.php:69, cornerstone/includes/elements/classic/_alternate/feature-box.php:95, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:79, cornerstone/includes/elements/classic/_alternate/feature-list.php:70, cornerstone/includes/elements/classic/_alternate/google-map-marker.php:63, cornerstone/includes/elements/classic/_alternate/image.php:8
msgid "Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:534
msgid "Image Offset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:535
msgid "Images"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:536
msgid "<img/> Element"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:537, cornerstone/includes/elements/definitions/lottie.php:245
msgid "SVG"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:538
msgid "In"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:539
msgid "In-Out"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:540
msgid "Include"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:541
msgid "Include sticky posts"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:542
msgid "Indicator"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:543
msgid "Indicators"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:544
msgid "Individual Tabs"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:545, cornerstone/includes/i18n/app.php:145
msgid "Inherit"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:546
msgid "Initial"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:547, cornerstone/includes/elements/control-partials/dropdown.php:199
msgid "Inline"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:548
msgid "Inline Block"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:549, cornerstone/includes/i18n/app.php:372
msgid "Inline CSS"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:550
msgid "Inner"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:551
msgid "Inner Stop"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:552
msgid "Inner Stop Begin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:553
msgid "Inner Stop End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:554
msgid "Input"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:555
msgid "Inputs"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:556
msgid "Input Placement"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:557, cornerstone/includes/i18n/app.php:352, cornerstone/includes/_classes/classic/class-control-mixins.php:163, cornerstone/includes/_classes/classic/class-control-mixins.php:707
msgid "Inset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:558, cornerstone/includes/i18n/app.php:414
msgid "Inside"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:559, cornerstone/includes/i18n/app.php:432, cornerstone/includes/i18n/app.php:733
msgid "Italic"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:560, cornerstone/includes/i18n/app.php:384, plugins/cornerstone-charts/extension/Controls/ChartColorOptions.php:13
msgid "Interaction"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:561
msgid "Intersect"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:562
msgid "Int. Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:563
msgid "Interactive Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:564
msgid "{{prefix}} Interactive Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:565
msgid "{{prefix}} Interactive Primary Graphic Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:566
msgid "{{prefix}} Interactive Secondary Graphic Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:567, cornerstone/includes/elements/control-partials/dropdown.php:165, cornerstone/includes/elements/classic/pricing-table-column/controls.php:63
msgid "Interval"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:568, cornerstone/includes/i18n/app.php:89, cornerstone/includes/elements/definitions/accordion-item-elements.php:223, cornerstone/includes/elements/definitions/accordion-item.php:220, cornerstone/includes/elements/classic/block-grid/controls.php:16, cornerstone/includes/elements/classic/icon-list-item/definition.php:17, cornerstone/includes/elements/classic/_alternate/accordion-item.php:14, cornerstone/includes/elements/classic/_alternate/accordion.php:28, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:14
msgid "Item"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:569, cornerstone/includes/elements/registry-setup.php:574, cornerstone/includes/i18n/app.php:90
msgid "Items"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:570
msgid "Item<br/>Address"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:571
msgid "Item<br/>Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:572
msgid "Item<br/>Name"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:573
msgid "Item<br/>Telephone"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:575
msgid "Items Alignment"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:576
msgid "Items Placement"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:577
msgid "Items Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:578
msgid "Items Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:579
msgid "Items X"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:580
msgid "Items Y"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:583, cornerstone/includes/i18n/app.php:461, cornerstone/includes/_classes/classic/class-control-mixins.php:262, cornerstone/includes/_classes/classic/class-control-mixins.php:665
msgid "Justify"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:584
msgid "Keep Margin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:585, cornerstone/includes/i18n/app.php:921
msgid "Key"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:586, plugins/cornerstone-charts/extension/Elements/Label.php:28
msgid "Label"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:587
msgid "Label Placement"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:588
msgid "Label Spacing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:589
msgid "Labels"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:590, cornerstone/includes/elements/classic/_alternate/google-map-marker.php:31, cornerstone/includes/elements/classic/_alternate/google-map.php:43
msgid "Latitude"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:591
msgid "Layers"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:592, cornerstone/includes/i18n/app.php:662, cornerstone/includes/i18n/common.php:49, cornerstone/includes/classes/Services/ElementLibrary.php:22
msgid "Layout"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:593
msgid "Leading Zero"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:594, cornerstone/includes/elements/definitions/comment-form.php:24
msgid "Leave a Reply"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:595, cornerstone/includes/elements/definitions/comment-form.php:25
msgid "Leave a Reply to %s"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:597
msgid "Legend"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:598, cornerstone/includes/i18n/app.php:925
msgid "Length"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:599
msgid "Letter Spacing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:600
msgid "&lt;li&gt;"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:601
msgid "Lighten"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:602
msgid "List Inset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:604, cornerstone/includes/i18n/common.php:75
msgid "Lists"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:605, cornerstone/includes/elements/definitions/line.php:164, cornerstone/includes/elements/classic/_alternate/line.php:8
msgid "Line"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:606
msgid "Line Height"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:607
msgid "Line Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:608
msgid "Line Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:609
msgid "Linear"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:610, cornerstone/includes/i18n/app.php:1137
msgid "Link Child Interactions"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:611
msgid "{{prefix}} Link"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:613, cornerstone/includes/elements/classic/_alternate/social-sharing.php:54
msgid "LinkedIn"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:614
msgid "Links"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:615
msgid "Links Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:616
msgid "Links Text Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:617
msgid "Load"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:618
msgid "Loaded"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:619
msgid "Loading"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:620
msgid "Load / reset on element toggle"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:621, cornerstone/includes/i18n/admin.php:49
msgid "Location"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:622
msgid "Logged In As Label"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:623, cornerstone/includes/elements/classic/_alternate/google-map-marker.php:39, cornerstone/includes/elements/classic/_alternate/google-map.php:51
msgid "Longitude"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:624, cornerstone/includes/i18n/app.php:451, cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:80, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:130, cornerstone/includes/elements/classic/_alternate/text-type.php:144
msgid "Loop"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:625
msgid "Loop Animation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:626
msgid "Loop Amount"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:627
msgid "Loop Typing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:628
msgid "Looped"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:629, cornerstone/includes/i18n/app.php:1129
msgid "Looper Consumer"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:630, cornerstone/includes/i18n/app.php:1128
msgid "Looper Provider"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:631
msgid "Lower"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:632
msgid "Lower Alpha"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:633
msgid "Lower Greek"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:634
msgid "Lower Latin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:635
msgid "Lower Roman"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:636
msgid "LTR"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:637
msgid "LTR Delimiter"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:638
msgid "Luminosity"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:639, cornerstone/includes/elements/registry-setup.php:881, cornerstone/includes/functions/helpers.php:1370
msgid "M"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:640
msgid "Many"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:641, cornerstone/includes/elements/definitions/map.php:368
msgid "Map"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:642, cornerstone/includes/elements/definitions/map-marker.php:191
msgid "Map Marker"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:643, cornerstone/includes/elements/classic/_alternate/google-map.php:22
msgid "Map Markers"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:644, cornerstone/includes/_classes/classic/class-control-mixins.php:634
msgid "Margin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:645
msgid "{{prefix}} Margin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:646, cornerstone/includes/elements/definitions/map-marker.php:198, cornerstone/includes/elements/classic/_alternate/google-map.php:26
msgid "Marker"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:647
msgid "Marker Insets"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:648
msgid "Markers"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:649
msgid "Marks"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:650
msgid "Marks Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:651
msgid "Marquee"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:652
msgid "Mask"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:653
msgid "Masking Begin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:654
msgid "Masking End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:655
msgid "Masking Length"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:656
msgid "Masking Start"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:657
msgid "Masking Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:658, cornerstone/includes/i18n/admin.php:81, cornerstone/includes/loopers/range.php:32
msgid "Max"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:659
msgid "Max Columns"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:660
msgid "Max Height"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:661
msgid "Max Scale"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:662, cornerstone/includes/elements/classic/_alternate/feature-box.php:316, cornerstone/includes/elements/classic/_alternate/feature-list.php:128
msgid "Max Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:663
msgid "Maximum"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:664, cornerstone/includes/classes/Services/ElementLibrary.php:27
msgid "Media"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:665
msgid "Median"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:666
msgid "Menu"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:667
msgid "Menu Item"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:668
msgid "Menu Order"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:669, cornerstone/includes/elements/classic/_alternate/callout.php:34, cornerstone/includes/elements/classic/_alternate/callout.php:38
msgid "Message"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:670
msgid "Messages"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:671
msgid "Messaging"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:672, cornerstone/includes/i18n/app.php:449, cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:64, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:106
msgid "Metadata"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:673
msgid "Mid # Count"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:674, cornerstone/includes/loopers/range.php:24
msgid "Min"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:675
msgid "Min Height"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:676
msgid "Min / Max"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:677
msgid "Min Scale"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:678
msgid "Min Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:679, cornerstone/includes/elements/definitions/tp-wc-cart.php:92
msgid "Mini-Cart"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:680
msgid "Minimum"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:681
msgid "Mix Blend Mode"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:682
msgid "Mobile Behavior"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:683
msgid "Mobile"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:685
msgid "Modal Custom Attributes"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:686
msgid "Mode"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:687
msgid "Modified"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:688
msgid "More"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:689
msgid "More Horizontal"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:690
msgid "More Vertical"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:691
msgid "Move By"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:692
msgid "Movement"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:693
msgid "Multiply"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:694
msgid "Multiplier"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:695
msgid "Must have all selected terms"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:696, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:122
msgid "Mute"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:697, cornerstone/includes/elements/classic/alert/controls.php:38
msgid "Muted"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:699
msgid "Name / ID"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:700, cornerstone/includes/elements/definitions/nav-collapsed.php:216
msgid "Navigation Collapsed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:701, cornerstone/includes/elements/definitions/nav-dropdown.php:156
msgid "Navigation Dropdown"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:702, cornerstone/includes/elements/definitions/nav-inline.php:118
msgid "Navigation Inline"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:703, cornerstone/includes/elements/definitions/nav-layered.php:199
msgid "Navigation Layered"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:706, cornerstone/includes/i18n/app.php:862
msgid "Newest"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:707
msgid "Newline"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:708
msgid "Next"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:709
msgid "No Available Comments"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:710
msgid "No Comments"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:711
msgid "No Controls"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:712
msgid "No Components"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:713
msgid "No Output If Empty"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:714
msgid "No Pointer Events"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:715
msgid "No Repeat"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:716
msgid "No Touch"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:717, cornerstone/includes/i18n/app.php:163, cornerstone/includes/i18n/app.php:345, cornerstone/includes/i18n/app.php:447, cornerstone/includes/i18n/app.php:501, cornerstone/includes/classes/Controllers/Choices.php:172, cornerstone/includes/_classes/classic/class-control-mixins.php:156, cornerstone/includes/_classes/classic/class-control-mixins.php:328, cornerstone/includes/_classes/classic/class-control-mixins.php:700, cornerstone/includes/elements/classic/column/controls.php:35, cornerstone/includes/elements/classic/section/controls.php:34, cornerstone/includes/elements/classic/section/controls.php:124, cornerstone/includes/elements/classic/section/controls.php:184, cornerstone/includes/elements/classic/_alternate/button.php:142, cornerstone/includes/elements/classic/_alternate/image.php:27, cornerstone/includes/elements/classic/_alternate/image.php:107, cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:62, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:104, cornerstone/includes/elements/classic/_alternate/widget-area.php:22
msgid "None"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:718, cornerstone/includes/i18n/app.php:431, cornerstone/includes/i18n/app.php:435, cornerstone/includes/i18n/app.php:728
msgid "Normal"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:719
msgid "# Per Page"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:720, cornerstone/includes/classes/Services/Conditionals.php:967
msgid "Number"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:721
msgid "Number Start & End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:722, cornerstone/includes/i18n/app.php:492
msgid "Object Fit"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:724, cornerstone/includes/elements/classic/column/controls.php:48, cornerstone/includes/elements/classic/_alternate/recent-posts.php:62
msgid "Offset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:725
msgid "Offset Top"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:726
msgid "Offset Trigger"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:727
msgid "Offset Side"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:728
msgid "Offset Sides"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:729, cornerstone/includes/views/partials/off-canvas.php:30
msgid "Off Canvas"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:730
msgid "Off Canvas Custom Attributes"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:731
msgid "&lt;ol&gt; Inset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:732
msgid "&lt;ol&gt; Nested"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:733
msgid "&lt;ol&gt; Top"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:734, cornerstone/includes/i18n/app.php:863
msgid "Oldest"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:735, cornerstone/includes/i18n/app.php:323
msgid "On"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:736
msgid "On Flick"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:737
msgid "One"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:738, cornerstone/includes/elements/control-partials/seconds-select.php:38
msgid "Once"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:739
msgid "Opacity"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:740, cornerstone/includes/classes/Services/Conditionals.php:780
msgid "Open"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:741
msgid "Opening"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:742
msgid "Opening Mark Align"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:743, cornerstone/includes/i18n/app.php:611, cornerstone/includes/i18n/common.php:27, cornerstone/includes/integration/woocommerce.php:263, cornerstone/includes/elements/classic/_alternate/counter.php:83
msgid "Options"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:744
msgid "Order"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:745
msgid "Orderby"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:746
msgid "Order By"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:747, cornerstone/includes/elements/classic/_alternate/recent-posts.php:78
msgid "Orientation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:748
msgid "Origin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:749
msgid "Out"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:750, cornerstone/includes/i18n/app.php:413
msgid "Outside"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:751
msgid "Outer"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:752
msgid "Outer Stop"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:753
msgid "Outer Stop Begin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:754
msgid "Outer Stop End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:755, cornerstone/includes/i18n/app.php:353, cornerstone/includes/_classes/classic/class-control-mixins.php:164, cornerstone/includes/_classes/classic/class-control-mixins.php:708
msgid "Outset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:757
msgid "Overflow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:758
msgid "Overflow X"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:759
msgid "Overflow Y"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:760
msgid "Overlap"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:761
msgid "Overlay"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:762
msgid "Overwrites"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:763
msgid "Packing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:764, cornerstone/includes/_classes/classic/class-control-mixins.php:139, cornerstone/includes/_classes/classic/class-control-mixins.php:645, cornerstone/includes/elements/classic/_alternate/card.php:51, cornerstone/includes/elements/classic/_alternate/creative-cta.php:24
msgid "Padding"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:765
msgid "{{prefix}} Padding"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:766
msgid "Page"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:767
msgid "Page Count"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:768
msgid "Paged"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:769
msgid "Paged Count"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:770
msgid "Pagination"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:771
msgid "Panels"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:772, cornerstone/includes/elements/classic/section/controls.php:87
msgid "Parallax"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:773
msgid "Params"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:774
msgid "{{prefix}} Particle"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:775
msgid "Particles"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:776, cornerstone/includes/elements/classic/section/controls.php:78
msgid "Pattern"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:777
msgid "Perspective"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:778
msgid "Perspective Origin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:779
msgid "Pingbacks"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:780
msgid "Pings"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:781, cornerstone/includes/elements/classic/_alternate/social-sharing.php:62
msgid "Pinterest"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:782
msgid "Placeholder"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:783
msgid "Placeholder Opacity"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:784, cornerstone/includes/i18n/app.php:437
msgid "Placement"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:785
msgid "Play Animation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:786
msgid "Play Once"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:787
msgid "Play When Visible"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:788
msgid "Player"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:789
msgid "Pointer"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:790
msgid "Pointer Events"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:791, cornerstone/includes/i18n/app.php:412, cornerstone/includes/i18n/app.php:493, cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:77
msgid "Position"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:792, cornerstone/includes/elements/definitions/post-nav.php:81
msgid "Post Navigation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:793, cornerstone/includes/elements/definitions/post-pagination.php:78
msgid "Post Pagination"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:795
msgid "Poster"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:796, cornerstone/includes/elements/classic/_alternate/text-type.php:30
msgid "Prefix"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:797
msgid "Prefix & Suffix"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:798, cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:57, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:99
msgid "Preload"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:799
msgid "Prev"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:800
msgid "Prev / Next"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:801
msgid "Prev / Next Icons"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:802
msgid "Prev / Next Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:803
msgid "Prev / Next Type"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:804, cornerstone/includes/i18n/app.php:516, cornerstone/includes/i18n/app.php:723
msgid "Preview"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:805
msgid "Previous"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:806, cornerstone/includes/elements/classic/pricing-table-column/controls.php:55
msgid "Price"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:807, cornerstone/includes/elements/registry-setup.php:808
msgid "Primary"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:809
msgid "Primary Icon"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:810
msgid "Primary Particle"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:811
msgid "%s Primary"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:812
msgid "Primary Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:813
msgid "{{prefix}} Primary Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:814
msgid "{{prefix}} Primary Graphic Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:815, cornerstone/includes/elements/definitions/tp-wc-product-gallery.php:181
msgid "Product Gallery"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:816, cornerstone/includes/elements/definitions/tp-wc-product-pagination.php:79
msgid "Product Pagination"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:817, cornerstone/includes/elements/definitions/tp-wc-products.php:75
msgid "Products"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:818
msgid "Progress"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:819
msgid "Published After"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:820
msgid "Published Before"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:821
msgid "Quantity"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:822
msgid "Query Builder"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:823
msgid "Query String"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:824, cornerstone/includes/elements/definitions/quote.php:487, cornerstone/includes/elements/classic/_alternate/blockquote.php:26, cornerstone/includes/elements/classic/_alternate/blockquote.php:30
msgid "Quote"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:825
msgid "Radial"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:826
msgid "Radio"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:827
msgid "Radius"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:828
msgid "Rail"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:829, cornerstone/includes/elements/classic/_alternate/slider.php:93
msgid "Random"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:830, cornerstone/includes/elements/definitions/rating.php:71
msgid "Rating"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:831
msgid "Ratio"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:832, cornerstone/includes/elements/definitions/raw-content.php:124, cornerstone/includes/elements/classic/_alternate/raw-content.php:8
msgid "Raw Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:833, cornerstone/includes/elements/classic/_alternate/recent-posts.php:8
msgid "Recent Posts"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:834, cornerstone/includes/elements/classic/_alternate/social-sharing.php:70
msgid "Reddit"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:835, cornerstone/includes/elements/definitions/tp-wc-related-products.php:74, cornerstone/includes/integration/woocommerce/Loopers/RelatedProducts.php:14
msgid "Related Products"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:836
msgid "Relative"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:837
msgid "Remove Button"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:838
msgid "Remove Lottie Element"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:839
msgid "Repeat"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:840
msgid "Repeat X"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:841
msgid "Repeat Y"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:842
msgid "Repeat Both"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:843
msgid "Reply Title"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:844
msgid "Reply To Title"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:845
msgid "Reset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:846
msgid "Retina"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:847
msgid "Retina Ready"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:848, cornerstone/includes/i18n/app.php:510, cornerstone/includes/elements/definitions/layout-slide-container.php:499
msgid "Reverse"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:849
msgid "Reverse On Leave"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:850, cornerstone/includes/i18n/app.php:351, cornerstone/includes/_classes/classic/class-control-mixins.php:162, cornerstone/includes/_classes/classic/class-control-mixins.php:706
msgid "Ridge"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:852
msgid "Root"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:853
msgid "Round"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:854
msgid "Round Whole"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:856, cornerstone/includes/i18n/app.php:259, cornerstone/includes/elements/classic/section/controls.php:14
msgid "Rows"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:857
msgid "RTL"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:858
msgid "RTL Delimiter"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:859
msgid "Rule Color"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:860
msgid "Rule Style"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:861
msgid "Rule Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:862, cornerstone/includes/elements/registry-setup.php:879, cornerstone/includes/functions/helpers.php:1371
msgid "S"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:863
msgid "Saturation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:864
msgid "Scale"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:865, cornerstone/includes/i18n/app.php:502
msgid "Scale Down"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:866
msgid "Scale Up"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:867
msgid "Scaling"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:868
msgid "Schema"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:869
msgid "123 Imaginary Drive"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:870
msgid "Gordon Ramsay"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:871
msgid "In-N-Out"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:872
msgid "FastFoodRestaurant"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:873
msgid "Screen"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:874
msgid "Scroll"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:875
msgid "Scrolling"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:876
msgid "Scroll By"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:877
msgid "Scroll Position Seek"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:878, cornerstone/includes/i18n/app.php:200, cornerstone/includes/_classes/classic/class-control-mixins.php:240
msgid "XS"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:880, cornerstone/includes/i18n/app.php:199, cornerstone/includes/_classes/classic/class-control-mixins.php:239
msgid "SM"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:882, cornerstone/includes/i18n/app.php:198, cornerstone/includes/_classes/classic/class-control-mixins.php:238
msgid "MD"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:883
msgid "L"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:884, cornerstone/includes/i18n/app.php:197, cornerstone/includes/_classes/classic/class-control-mixins.php:237
msgid "LG"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:885, cornerstone/includes/i18n/app.php:196, cornerstone/includes/_classes/classic/class-control-mixins.php:236
msgid "XL"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:886, cornerstone/includes/i18n/app.php:81, cornerstone/includes/classes/Services/Conditionals.php:865, cornerstone/includes/elements/definitions/search-inline.php:75, cornerstone/includes/views/partials/search.php:103, cornerstone/includes/elements/classic/_alternate/search.php:8
msgid "Search"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:887
msgid "Sec"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:888
msgid "Secondary"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:889
msgid "Secondary Icon"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:890
msgid "Secondary Particle"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:891
msgid "Secondary Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:892
msgid "%s Secondary"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:893
msgid "{{prefix}} Secondary Graphic Image"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:895, cornerstone/includes/i18n/app.php:258
msgid "Sections"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:896, cornerstone/includes/i18n/app.php:104, plugins/cornerstone-charts/extension/Controls/ChartColorOptions.php:11
msgid "Select"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:897, cornerstone/includes/i18n/app.php:809
msgid "Selector"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:898, cornerstone/includes/i18n/app.php:509
msgid "Self Flex"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:899
msgid "Self Only"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:900
msgid "Separators"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:901, cornerstone/includes/i18n/app.php:368
msgid "Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:902
msgid "{{prefix}} Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:903
msgid "Shadow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:904, cornerstone/includes/elements/classic/_alternate/button.php:50
msgid "Shape"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:906, cornerstone/includes/elements/definitions/tp-wc-shop-notices.php:116
msgid "Shop Notices"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:907, cornerstone/includes/elements/definitions/tp-wc-shop-sort.php:112
msgid "Shop Sort"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:908, cornerstone/includes/elements/definitions/form-integration.php:232
msgid "Show"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:909, cornerstone/includes/elements/classic/_alternate/text-type.php:152
msgid "Show Cursor"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:910
msgid "Shrink Amount"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:911
msgid "Sides"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:912
msgid "Side Offset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:913
msgid "Simple"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:914, cornerstone/includes/i18n/app.php:473, cornerstone/includes/elements/classic/_alternate/button.php:66, cornerstone/includes/elements/classic/_alternate/gap.php:25
msgid "Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:915
msgid "{{prefix}} Size"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:916, cornerstone/includes/elements/definitions/layout-slide.php:334, cornerstone/includes/elements/classic/_alternate/slide.php:8, cornerstone/includes/elements/classic/_alternate/slider.php:43
msgid "Slide"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:917
msgid "Slide Bottom"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:918, cornerstone/includes/elements/definitions/layout-slide-container.php:914
msgid "Slide Container"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:919
msgid "Slide Left"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:920, cornerstone/includes/elements/definitions/slide-pagination.php:265
msgid "Slide Pagination"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:921
msgid "Slide Right"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:922
msgid "Slide Top"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:923
msgid "Slide Bottom / Scale Up"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:924
msgid "Slide Left / Scale Up"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:925
msgid "Slide Right / Scale Up"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:926
msgid "Slide Top / Scale Up"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:927, cornerstone/includes/classes/Services/ElementLibrary.php:29, cornerstone/includes/elements/classic/_alternate/slider.php:8
msgid "Slider"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:928, cornerstone/includes/elements/classic/_alternate/slider.php:21
msgid "Slides"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:929
msgid "Slides to Show"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:930, cornerstone/includes/i18n/app.php:136, cornerstone/includes/i18n/app.php:916, cornerstone/includes/classes/Documents/Content.php:515
msgid "Slug"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:931
msgid "Snap"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:932, cornerstone/includes/elements/definitions/social.php:105
msgid "Social"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:933
msgid "Soft Light"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:934, cornerstone/includes/i18n/app.php:346, cornerstone/includes/i18n/app.php:796, cornerstone/includes/classes/Services/FontAwesome.php:105, cornerstone/includes/_classes/classic/class-control-mixins.php:157, cornerstone/includes/_classes/classic/class-control-mixins.php:701, cornerstone/includes/elements/classic/_alternate/feature-list.php:146
msgid "Solid"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:935, cornerstone/includes/i18n/app.php:487
msgid "Source"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:936
msgid "Sources<br>(1 Per Line)"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:937
msgid "Space"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:938, cornerstone/includes/i18n/app.php:338
msgid "Space Around"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:939, cornerstone/includes/i18n/app.php:339
msgid "Space Between"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:940, cornerstone/includes/i18n/app.php:340
msgid "Space Evenly"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:941, cornerstone/includes/i18n/app.php:474
msgid "Spacing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:942
msgid "Specialty"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:943
msgid "Specific Posts"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:944
msgid "Specific Terms"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:945
msgid "Speed"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:946, cornerstone/includes/i18n/app.php:411
msgid "Spread"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:947
msgid "Sparse"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:948, cornerstone/includes/elements/classic/_alternate/button.php:56, cornerstone/includes/elements/classic/_alternate/feature-box.php:165, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:131
msgid "Square"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:949, cornerstone/includes/elements/definitions/layout-slide-container.php:891
msgid "Stacked"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:950, cornerstone/includes/i18n/app.php:279, cornerstone/includes/i18n/app.php:326, cornerstone/includes/elements/classic/pricing-table/controls.php:25, cornerstone/includes/elements/classic/pricing-table-column/controls.php:12
msgid "Standard"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:951, cornerstone/includes/i18n/app.php:333, cornerstone/includes/i18n/app.php:334
msgid "Start"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:952
msgid "Start Rotation"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:953
msgid "Starts At"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:954, cornerstone/includes/elements/classic/_alternate/accordion-item.php:41
msgid "Starts Open"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:955, cornerstone/includes/elements/definitions/statbar.php:436
msgid "Statbar"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:956
msgid "Static"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:957
msgid "Sticky"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:958
msgid "Stop"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:959, cornerstone/includes/i18n/app.php:342
msgid "Stretch"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:960, cornerstone/includes/classes/Services/Conditionals.php:957
msgid "String"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:961
msgid "Stroke"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:962
msgid "Stroke Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:963, cornerstone/includes/i18n/app.php:371, cornerstone/includes/i18n/app.php:430, cornerstone/includes/_classes/classic/class-control-mixins.php:126, cornerstone/includes/_classes/classic/class-control-mixins.php:622, cornerstone/includes/elements/classic/_alternate/image.php:22
msgid "Style"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:964
msgid "Sub Indicator"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:965
msgid "{{prefix}} Sub Indicator"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:966
msgid "%s Sub Indicator"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:967
msgid "Sub Links"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:968
msgid "Sub Menu Items"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:969
msgid "Sub Menu Trigger"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:971
msgid "Submits"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:972
msgid "Submit Label"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:973
msgid "Submit Placement"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:974
msgid "Subheadline"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:975
msgid "{{prefix}} Subheadline"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:976
msgid "%s Subheadline"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:977
msgid "Subtract"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:978, cornerstone/includes/elements/classic/_alternate/text-type.php:49
msgid "Suffix"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:979
msgid "Swipe"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:980
msgid "Symbol"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:981, cornerstone/includes/elements/definitions/tab-elements.php:24, cornerstone/includes/elements/definitions/tab.php:24, cornerstone/includes/elements/classic/_alternate/tab.php:8, cornerstone/includes/elements/classic/_alternate/tabs.php:28
msgid "Tab"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:982, cornerstone/includes/elements/definitions/tabs.php:559, cornerstone/includes/elements/classic/_alternate/tabs.php:24
msgid "Tab 1"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:983, cornerstone/includes/elements/definitions/tabs.php:562, cornerstone/includes/elements/classic/_alternate/tabs.php:25
msgid "Tab 2"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:984
msgid "Tab List"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:985, cornerstone/includes/elements/definitions/tabs.php:546, cornerstone/includes/elements/classic/_alternate/tabs.php:8, cornerstone/includes/elements/classic/_alternate/tabs.php:21
msgid "Tabs"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:986
msgid "#target-element (optional)"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:987
msgid "Targets"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:988
msgid "Taxonomy"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:989
msgid "Taxonomies"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:990, cornerstone/includes/i18n/app.php:626, cornerstone/includes/classes/Services/Templates.php:80
msgid "Template"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:991, cornerstone/includes/elements/definitions/testimonial.php:505
msgid "Testimonial"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:993
msgid "%s Text"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:994, cornerstone/includes/_classes/classic/class-control-mixins.php:252, cornerstone/includes/_classes/classic/class-control-mixins.php:656
msgid "Text Align"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:995, cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:63, cornerstone/includes/elements/classic/_alternate/counter.php:89, cornerstone/includes/elements/classic/_alternate/creative-cta.php:148, cornerstone/includes/elements/classic/_alternate/custom-headline.php:66, cornerstone/includes/elements/classic/_alternate/feature-headline.php:66
msgid "Text Color"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:996
msgid "{{prefix}} Text Columns"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:997
msgid "Text Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:998
msgid "%s Text Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:999
msgid "Text Decoration"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1000
msgid "Text Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1001
msgid "{{prefix}} Text Format"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1002
msgid "Text Overflow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1003
msgid "{{prefix}} Text Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1004
msgid "Text Shadow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1005
msgid "{{prefix}} Text Shadow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1006
msgid "Text Transform"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1009
msgid "Thickness"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1010
msgid "Threshold"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1011, cornerstone/includes/elements/classic/_alternate/image.php:28
msgid "Thumbnail"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1012
msgid "Thumbnail Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1013
msgid "Time Rail"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1014
msgid "{{prefix}} Time Rail Setup"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1015
msgid "Timing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1017
msgid "Toggle"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1018
msgid "{{prefix}} Toggle"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1019
msgid "{{prefix}} Toggle Colors"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1020, cornerstone/includes/elements/definitions/deprecated-content-area-dropdown.php:59, cornerstone/includes/elements/definitions/deprecated-search-dropdown.php:59, cornerstone/includes/elements/definitions/deprecated-tp-wc-cart-dropdown.php:76, cornerstone/includes/elements/definitions/layout-dropdown.php:63, cornerstone/includes/elements/definitions/nav-dropdown.php:82, cornerstone/includes/elements/definitions/tp-bbp-dropdown.php:58, cornerstone/includes/elements/definitions/tp-bp-dropdown.php:70
msgid "Toggle Dropdown Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1021, cornerstone/includes/elements/definitions/deprecated-content-area-modal.php:59, cornerstone/includes/elements/definitions/deprecated-nav-modal.php:68, cornerstone/includes/elements/definitions/deprecated-search-modal.php:59, cornerstone/includes/elements/definitions/deprecated-tp-wc-cart-modal.php:73, cornerstone/includes/elements/definitions/layout-modal.php:59
msgid "Toggle Modal Content"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1022, cornerstone/includes/i18n/app.php:416, cornerstone/includes/i18n/app.php:1070, cornerstone/includes/elements/classic/column/controls.php:36, cornerstone/includes/elements/classic/_alternate/button.php:158, cornerstone/includes/elements/classic/_alternate/feature-box.php:291, cornerstone/includes/elements/classic/_alternate/feature-list.php:111, cornerstone/includes/elements/classic/_alternate/image.php:126, cornerstone/includes/elements/classic/_alternate/tabs.php:45
msgid "Top"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1023, cornerstone/includes/i18n/app.php:422
msgid "Top Left"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1024
msgid "Top Links"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1025
msgid "Top Menu Items"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1026
msgid "Top Offset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1027, cornerstone/includes/i18n/app.php:423
msgid "Top Right"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1028
msgid "Total"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1029
msgid "Total Placement"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1030
msgid "Touch Scroll"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1031
msgid "Trackbacks"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1032, cornerstone/includes/i18n/app.php:477
msgid "Transform"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1033
msgid "Transform Origin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1034
msgid "Transition"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1035
msgid "Transition Length"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1036
msgid "Transition Stop"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1037, cornerstone/includes/i18n/app.php:438
msgid "Trigger"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1038
msgid "Trigger Offset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1039
msgid "Trigger Selector"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1040, cornerstone/includes/elements/classic/_alternate/social-sharing.php:46
msgid "Twitter"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1041, cornerstone/includes/i18n/app.php:147, cornerstone/includes/i18n/app.php:434, cornerstone/includes/i18n/app.php:517, cornerstone/includes/i18n/app.php:533, cornerstone/includes/elements/definitions/form-integration.php:271, cornerstone/includes/integration/csv/controls.php:41, cornerstone/includes/elements/classic/alert/controls.php:32, cornerstone/includes/elements/classic/_alternate/button.php:34
msgid "Type"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1042
msgid "Typography"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1043
msgid "Types"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1044
msgid "Typed Text (1 Per Line)"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1045
msgid "Typing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1046
msgid "{{prefix}} Typing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1047
msgid "&lt;ul&gt; Inset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1048
msgid "&lt;ul&gt; Nested"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1049
msgid "&lt;ul&gt; Top"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1050, cornerstone/includes/i18n/app.php:468
msgid "Underline"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1051
msgid "Unit"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1052
msgid "Units"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1053
msgid "Up"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1054
msgid "Upper"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1055
msgid "Upper Alpha"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1056
msgid "Upper Greek"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1057
msgid "Upper Latin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1058
msgid "Upper Roman"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1059, cornerstone/includes/elements/definitions/tp-wc-upsells.php:75, cornerstone/includes/integration/woocommerce/Loopers/Upsell.php:11
msgid "Upsells"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1060, cornerstone/includes/i18n/app.php:519, cornerstone/includes/i18n/app.php:918, cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:49, cornerstone/includes/_classes/classic/class-control-mixins.php:751
msgid "URL"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1061
msgid "Use Effects"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1062
msgid "Use Global Container"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1063
msgid "Uses get_the_terms to find terms associated with the current post."
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1065
msgid "Vert"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1066, cornerstone/includes/i18n/app.php:513, cornerstone/includes/elements/classic/_alternate/recent-posts.php:84
msgid "Vertical"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1067, cornerstone/includes/elements/definitions/video.php:397, cornerstone/includes/elements/classic/section/controls.php:37
msgid "Video"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1068
msgid "Viewport"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1069
msgid "Viewport Begin"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1070
msgid "Viewport End"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1071
msgid "Viewport Length"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1072
msgid "Viewport Offset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1073
msgid "Viewport Start"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1074
msgid "Viewport Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1075, cornerstone/includes/integration/woocommerce.php:239
msgid "Visible"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1076
msgid "Visible Length"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1077
msgid "Visible Stop"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1078
msgid "Wavy"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1079, cornerstone/includes/i18n/app.php:472, cornerstone/includes/integration/woocommerce.php:264
msgid "Weight"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1080, cornerstone/includes/elements/definitions/widget-area.php:146, cornerstone/includes/elements/classic/_alternate/widget-area.php:8
msgid "Widget Area"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1081
msgid "Widget Spacing"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1082, cornerstone/includes/i18n/app.php:405
msgid "Width"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1083, cornerstone/includes/classes/Services/ElementLibrary.php:31
msgid "WordPress"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1084
msgid "WP Query"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1085, cornerstone/includes/i18n/app.php:511
msgid "Wrap"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1086
msgid "X"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1087
msgid "X Axis"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1088, cornerstone/includes/i18n/app.php:408
msgid "X Offset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1089
msgid "X Overflow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1090
msgid "X Translate"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1091
msgid "X & Y"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1092
msgid "Y"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1093
msgid "Y Axis"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1094, cornerstone/includes/i18n/app.php:409
msgid "Y Offset"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1095
msgid "Y Overflow"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1096
msgid "Y Translate"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1097
msgid "Z-Index"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1098
msgid "Z-Index Stack"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1099, cornerstone/includes/elements/classic/_alternate/google-map.php:59
msgid "Zoom"
msgstr ""

#: cornerstone/includes/elements/registry-setup.php:1100
msgid "Zoom Level"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:222
msgid "Sidebars"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:289
msgid "No sidebars"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:303
msgid "You haven&rsquo;t added any sidebars yet. Add one using the form on the right hand side."
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:319
msgid "Manage Sidebars"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:349
msgid "Before Title"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:358
msgid "After Widget"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:359
msgid "Child Page Display"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:360
msgid "Blog Display"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:361
msgid "Shop Display"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:369
msgid "All Pages and Posts"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:435
msgid "All Taxonomies"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:496
msgid "Post Types"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:532
msgid "Enable sidebar on shop index page"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:530
msgid "Enable sidebar on blog index page"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:528
msgid "Enable parent page sidebar on child pages"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:547
msgid "Update Sidebar"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:548
msgid "Delete this sidebar?"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:621
msgid "Enter in the name of a new sidebar below. This name will be used as part of a unique identifier for each sidebar you create."
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:623
msgid "Add Sidebar"
msgstr ""

#: cornerstone/includes/extend/custom-sidebars.php:640
msgid "<strong>Your sidebar settings have been saved!</strong> You can now go manage the <a href=\"%s\">widgets</a> for the sidebars that you have created."
msgstr ""

#: cornerstone/includes/extend/portfolio.php:96
msgid "Portfolio"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:97
msgid "Portfolio Item"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:98
msgid "Add New Item"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:99, cornerstone/includes/extend/portfolio.php:101
msgid "Add New Portfolio Item"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:100
msgid "Edit Portfolio Item"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:102
msgid "View Item"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:103
msgid "Search Portfolio"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:104
msgid "No portfolio items found"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:105
msgid "No portfolio items found in trash"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:127, cornerstone/includes/extend/portfolio.php:141
msgid "Portfolio Tags"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:128
msgid "Portfolio Tag"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:129
msgid "Search Portfolio Tags"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:130
msgid "Popular Portfolio Tags"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:131
msgid "All Portfolio Tags"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:132
msgid "Parent Portfolio Tag"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:133
msgid "Parent Portfolio Tag:"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:134
msgid "Edit Portfolio Tag"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:135
msgid "Update Portfolio Tag"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:136
msgid "Add New Portfolio Tag"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:137
msgid "New Portfolio Tag Name"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:138
msgid "Separate portfolio tags with commas"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:139
msgid "Add or remove portfolio tags"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:140
msgid "Choose from the most used portfolio tags"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:162, cornerstone/includes/extend/portfolio.php:176
msgid "Portfolio Categories"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:163
msgid "Portfolio Category"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:164
msgid "Search Portfolio Categories"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:165
msgid "Popular Portfolio Categories"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:166
msgid "All Portfolio Categories"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:167
msgid "Parent Portfolio Category"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:168
msgid "Parent Portfolio Category:"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:169
msgid "Edit Portfolio Category"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:170
msgid "Update Portfolio Category"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:171
msgid "Add New Portfolio Category"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:172
msgid "New Portfolio Category Name"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:173
msgid "Separate portfolio categories with commas"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:174
msgid "Add or remove portfolio categories"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:175
msgid "Choose from the most used portfolio categories"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:370
msgid "Filter by Category"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:455, cornerstone/includes/shortcodes/share.php:59
msgid "Share on Facebook"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:457
msgid "Share on X / Twitter"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:459, cornerstone/includes/shortcodes/share.php:61
msgid "Share on LinkedIn"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:460, cornerstone/includes/shortcodes/share.php:62
msgid "Share on Pinterest"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:462, cornerstone/includes/shortcodes/share.php:63
msgid "Share on Reddit"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:464, cornerstone/includes/shortcodes/share.php:68, cornerstone/includes/elements/classic/_alternate/social-sharing.php:88
msgid "Hey, thought you might enjoy this! Check it out when you have a chance:"
msgstr ""

#: cornerstone/includes/extend/portfolio.php:464, cornerstone/includes/shortcodes/share.php:74
msgid "Share via Email"
msgstr ""

#: cornerstone/includes/functions/helpers.php:1378, cornerstone/includes/elements/control-partials/seconds-select.php:16
msgid "Minute"
msgid_plural "Minutes"
msgstr[0] ""
msgstr[1] ""

#: cornerstone/includes/functions/helpers.php:1379, cornerstone/includes/elements/control-partials/seconds-select.php:13
msgid "Second"
msgid_plural "Seconds"
msgstr[0] ""
msgstr[1] ""

#: cornerstone/includes/functions/helpers.php:1831, cornerstone/includes/i18n/app.php:175
msgid "Publish"
msgstr ""

#: cornerstone/includes/functions/helpers.php:1835
msgid "Privately Published"
msgstr ""

#: cornerstone/includes/functions/helpers.php:1838
msgid "Scheduled"
msgstr ""

#: cornerstone/includes/functions/helpers.php:1841
msgid "Pending Review"
msgstr ""

#: cornerstone/includes/functions/helpers.php:1844
msgid "Draft"
msgstr ""

#: cornerstone/includes/functions/helpers.php:1865
msgid "(no parent)"
msgstr ""

#: cornerstone/includes/functions/video.php:155
msgid "Video source missing"
msgstr ""

#: cornerstone/includes/i18n/admin.php:7
msgid "Yes, proceed"
msgstr ""

#: cornerstone/includes/i18n/admin.php:8
msgid "No, take me back"
msgstr ""

#: cornerstone/includes/i18n/admin.php:13
msgid "Edit with Cornerstone"
msgstr ""

#: cornerstone/includes/i18n/admin.php:14
msgid "Visual"
msgstr ""

#: cornerstone/includes/i18n/admin.php:16, cornerstone/includes/i18n/admin.php:33, cornerstone/includes/i18n/common.php:19, cornerstone/includes/i18n/common.php:33, cornerstone/includes/integration/WordPress/post-status.php:9
msgid "Cornerstone"
msgstr ""

#: cornerstone/includes/i18n/admin.php:17
msgid "Edit with WordPress"
msgstr ""

#: cornerstone/includes/i18n/admin.php:18
msgid "Insert Shortcode"
msgstr ""

#: cornerstone/includes/i18n/admin.php:19
msgid "Updating"
msgstr ""

#: cornerstone/includes/i18n/admin.php:20
msgid "Yep"
msgstr ""

#: cornerstone/includes/i18n/admin.php:21
msgid "Nope"
msgstr ""

#: cornerstone/includes/i18n/admin.php:22
msgid "Hold up! You&apos;re welcome to make changes to the content. However, these will not be reflected in Cornerstone. If you edit the page in Cornerstone again, any changes made here will be overwritten. Do you wish to continue?"
msgstr ""

#: cornerstone/includes/i18n/admin.php:23
msgid "Hold up! The content has been modified outside of Cornerstone. Editing in Cornerstone will replace the current content. Do you wish to continue?"
msgstr ""

#: cornerstone/includes/i18n/admin.php:24
msgid "Please save this content at least once before editing."
msgstr ""

#: cornerstone/includes/i18n/admin.php:25, cornerstone/includes/i18n/admin.php:150
msgid "Go Back"
msgstr ""

#: cornerstone/includes/i18n/admin.php:26
msgid "Loading..."
msgstr ""

#: cornerstone/includes/i18n/admin.php:35, cornerstone/includes/i18n/app.php:173, cornerstone/includes/classes/Documents/Content.php:521, cornerstone/includes/elements/control-partials/looper-provider.php:183
msgid "Status"
msgstr ""

#: cornerstone/includes/i18n/admin.php:38, cornerstone/includes/i18n/admin.php:40, cornerstone/includes/i18n/admin.php:83, cornerstone/includes/i18n/app.php:165
msgid "Update"
msgstr ""

#: cornerstone/includes/i18n/admin.php:39
msgid "Save Settings"
msgstr ""

#: cornerstone/includes/i18n/admin.php:41, cornerstone/includes/i18n/app.php:1159
msgid "Once you are satisfied with your settings, click the button below to save them."
msgstr ""

#: cornerstone/includes/i18n/admin.php:42
msgid "Updating..."
msgstr ""

#: cornerstone/includes/i18n/admin.php:43
msgid "Settings Saved!"
msgstr ""

#: cornerstone/includes/i18n/admin.php:44
msgid "Sorry! Unable to Save"
msgstr ""

#: cornerstone/includes/i18n/admin.php:46, cornerstone/includes/_classes/dynamic-content/class-dynamic-content-user.php:119
msgid "Role"
msgstr ""

#: cornerstone/includes/i18n/admin.php:47
msgid "Manage access to Cornerstone."
msgstr ""

#: cornerstone/includes/i18n/admin.php:51, cornerstone/includes/i18n/admin.php:99
msgid "System"
msgstr ""

#: cornerstone/includes/i18n/admin.php:53
msgid "Permissions"
msgstr ""

#: cornerstone/includes/i18n/admin.php:54
msgid "Configure"
msgstr ""

#: cornerstone/includes/i18n/admin.php:56, cornerstone/includes/elements/definitions/form-integration.php:447, cornerstone/includes/integration/ScrollProgress/ScrollProgress.php:41
msgid "Enabled"
msgstr ""

#: cornerstone/includes/i18n/admin.php:57
msgid "Toggle All Permissions"
msgstr ""

#: cornerstone/includes/i18n/admin.php:58, cornerstone/includes/i18n/app.php:118
msgid "Insert"
msgstr ""

#: cornerstone/includes/i18n/admin.php:59
msgid "Import Terms"
msgstr ""

#: cornerstone/includes/i18n/admin.php:61, cornerstone/includes/i18n/app.php:628, cornerstone/includes/i18n/common.php:24, cornerstone/includes/classes/Services/Templates.php:79, cornerstone/includes/integration/Twig/src/ThemeOptions.php:44
msgid "Templates"
msgstr ""

#: cornerstone/includes/i18n/admin.php:63, cornerstone/includes/i18n/app.php:69, cornerstone/includes/i18n/app.php:933, cornerstone/includes/classes/Services/Conditionals.php:42, cornerstone/includes/classes/Services/Conditionals.php:70, cornerstone/includes/integration/Api/ApiDynamicContent.php:29
msgid "Global"
msgstr ""

#: cornerstone/includes/i18n/admin.php:65, cornerstone/includes/i18n/app.php:714, cornerstone/includes/i18n/app.php:754, cornerstone/includes/i18n/common.php:29
msgid "Fonts"
msgstr ""

#: cornerstone/includes/i18n/admin.php:67
msgid "Theme Options Import"
msgstr ""

#: cornerstone/includes/i18n/admin.php:68
msgid "Theme Options Export"
msgstr ""

#: cornerstone/includes/i18n/admin.php:69
msgid "Export Documents (Alpha)"
msgstr ""

#: cornerstone/includes/i18n/admin.php:70
msgid "All Classic Elements"
msgstr ""

#: cornerstone/includes/i18n/admin.php:72, cornerstone/includes/i18n/admin.php:78, cornerstone/includes/i18n/app.php:114
msgid "Create"
msgstr ""

#: cornerstone/includes/i18n/admin.php:73, cornerstone/includes/i18n/app.php:874
msgid "Delete"
msgstr ""

#: cornerstone/includes/i18n/admin.php:74
msgid "Lock / Unlock Elements"
msgstr ""

#: cornerstone/includes/i18n/admin.php:75
msgid "Edit Custom CSS"
msgstr ""

#: cornerstone/includes/i18n/admin.php:76
msgid "Edit Custom JS"
msgstr ""

#: cornerstone/includes/i18n/admin.php:79
msgid "Manage Library"
msgstr ""

#: cornerstone/includes/i18n/admin.php:80, cornerstone/includes/i18n/app.php:655, cornerstone/includes/views/admin/home-box-templates.php:4
msgid "Themeco Templates"
msgstr ""

#: cornerstone/includes/i18n/admin.php:86, cornerstone/includes/i18n/app.php:872
msgid "Inspect"
msgstr ""

#: cornerstone/includes/i18n/admin.php:88, cornerstone/includes/i18n/app.php:77
msgid "Paste"
msgstr ""

#: cornerstone/includes/i18n/admin.php:89
msgid "Paste Style"
msgstr ""

#: cornerstone/includes/i18n/admin.php:90
msgid "Clear Style"
msgstr ""

#: cornerstone/includes/i18n/admin.php:91, cornerstone/includes/i18n/app.php:594
msgid "Apply Preset"
msgstr ""

#: cornerstone/includes/i18n/admin.php:92
msgid "Manage"
msgstr ""

#: cornerstone/includes/i18n/admin.php:93, cornerstone/includes/i18n/app.php:593
msgid "Save Preset"
msgstr ""

#: cornerstone/includes/i18n/admin.php:94
msgid "Show In Library"
msgstr ""

#: cornerstone/includes/i18n/admin.php:100, cornerstone/includes/i18n/admin.php:106
msgid "Clear System Cache"
msgstr ""

#: cornerstone/includes/i18n/admin.php:101
msgid "For slower page loads Elements will remember the CSS generated when they were last saved. This is automatically cleared when Cornerstone is updated. It may be useful to clear manually if any Elements are missing styling"
msgstr ""

#: cornerstone/includes/i18n/admin.php:107
msgid "Clearing..."
msgstr ""

#: cornerstone/includes/i18n/admin.php:108
msgid "Cleared!"
msgstr ""

#: cornerstone/includes/i18n/admin.php:109
msgid "Clear System Cache Error"
msgstr ""

#: cornerstone/includes/i18n/admin.php:113, cornerstone/includes/integration/Api/ApiLooper.php:21
msgid "External API"
msgstr ""

#: cornerstone/includes/i18n/admin.php:114
msgid "Allow List"
msgstr ""

#: cornerstone/includes/i18n/admin.php:115
msgid "Enter all external domains that you would like to allow your website to access. Leave this empty to allow any domain. Each new domain should be entered onto a new line. Include the protocol (e.g. http or https)"
msgstr ""

#: cornerstone/includes/i18n/admin.php:116
msgid "Easily connect to a number of different External Endpoints. When enabled extra Looper Providers, Theme Options, and Dynamic Content features will be added to Cornerstone"
msgstr ""

#: cornerstone/includes/i18n/admin.php:118
msgid "Storing Content as HTML"
msgstr ""

#: cornerstone/includes/i18n/admin.php:119
msgid "Storing Content as Shortcodes"
msgstr ""

#: cornerstone/includes/i18n/admin.php:120
msgid "Migrate"
msgstr ""

#: cornerstone/includes/i18n/admin.php:121
msgid "Migrate to HTML"
msgstr ""

#: cornerstone/includes/i18n/admin.php:122
msgid "Migrate to Shortcodes"
msgstr ""

#: cornerstone/includes/i18n/admin.php:123
msgid "Please create a backup of your site before running the document storage migration. If this is disabled it will convert your site to shortcodes and if it's set to HTML it will migrate all your content documents to use HTML as the storage type. Using HTML storage will have better SEO and 3rd party plugin support."
msgstr ""

#: cornerstone/includes/i18n/admin.php:125, cornerstone/includes/integration/html-storage.php:188
msgid "Content Storage"
msgstr ""

#: cornerstone/includes/i18n/admin.php:127
msgid "Document storage migration was successful"
msgstr ""

#: cornerstone/includes/i18n/admin.php:129
msgid "Beta"
msgstr ""

#: cornerstone/includes/i18n/admin.php:131, cornerstone/includes/i18n/app.php:226, cornerstone/includes/views/admin/home-box-support.php:25, cornerstone/includes/views/admin/home-box-support.php:32
msgid "Docs"
msgstr ""

#: cornerstone/includes/i18n/admin.php:139
msgid "Nothing to report."
msgstr ""

#: cornerstone/includes/i18n/admin.php:140
msgid "New version available!"
msgstr ""

#: cornerstone/includes/i18n/admin.php:141
msgid "Unable to check for updates. Try again later."
msgstr ""

#: cornerstone/includes/i18n/admin.php:142
msgid "Checking&hellip;"
msgstr ""

#: cornerstone/includes/i18n/admin.php:143
msgid "Visit the <a href=\"http://theme.co/changelog/#cornerstone\">Themeco Changelog</a> for more information."
msgstr ""

#: cornerstone/includes/i18n/admin.php:144
msgid "<a href=\"%s\">Validate to enable automatic updates</a>"
msgstr ""

#: cornerstone/includes/i18n/admin.php:146
msgid "This Cornerstone license is ​<strong>not validated</strong>​. <a href=\"%s\">Fix</a>."
msgstr ""

#: cornerstone/includes/i18n/admin.php:147
msgid "Verifying license&hellip;"
msgstr ""

#: cornerstone/includes/i18n/admin.php:148
msgid "<strong>Uh oh</strong>, we couldn&apos;t check if this license was valid. <a data-tco-error-details href=\"#\">Details.</a>"
msgstr ""

#: cornerstone/includes/i18n/admin.php:151
msgid "Login or Register"
msgstr ""

#: cornerstone/includes/i18n/admin.php:152, cornerstone/includes/views/admin/home-sidebar.php:10
msgid "Manage Licenses"
msgstr ""

#: cornerstone/includes/i18n/admin.php:153
msgid "By revoking validation, you will no longer receive automatic updates. The site will still be linked in your Themeco account, so you can re-validate at anytime.<br/><br/> Visit \"Licenses\" in your Themeco account to transfer a license to another site."
msgstr ""

#: cornerstone/includes/i18n/admin.php:154
msgid "Yes, revoke validation"
msgstr ""

#: cornerstone/includes/i18n/admin.php:155
msgid "Stay validated"
msgstr ""

#: cornerstone/includes/i18n/admin.php:156
msgid "Revoking&hellip;"
msgstr ""

#: cornerstone/includes/i18n/admin.php:157
msgid "<strong>Validation revoked.</strong> You can re-assign licenses from <a href=\"%s\" target=\"_blank\">Manage Licenses</a>."
msgstr ""

#: cornerstone/includes/i18n/admin.php:158
msgid "We&apos;ve checked the code, but it <strong>doesn&apos;t appear to be an Cornerstone purchase code or Themeco license.</strong> Please double check the code and try again."
msgstr ""

#: cornerstone/includes/i18n/admin.php:159
msgid "This looks like a <strong>brand new purchase code that hasn&apos;t been added to a Themeco account yet.</strong> Login to your existing account or register a new one to continue."
msgstr ""

#: cornerstone/includes/i18n/admin.php:160
msgid "Your code is valid, but <strong>we couldn&apos;t automatically link it to your site.</strong> You can add this site from within your Themeco account."
msgstr ""

#: cornerstone/includes/i18n/admin.php:161
msgid "Your code is valid but looks like it has <strong>already been used on another site.</strong> You can revoke and re-assign within your Themeco account."
msgstr ""

#: cornerstone/includes/i18n/admin.php:163
msgid "Could not establish connection. For assistance, please start by reviewing our article on troubleshooting <a href=\"https://theme.co/docs/problems-with-product-validation/\">connection issues.</a>"
msgstr ""

#: cornerstone/includes/i18n/app.php:52
msgid "Powered by Themeco"
msgstr ""

#: cornerstone/includes/i18n/app.php:58
msgid "Apply"
msgstr ""

#: cornerstone/includes/i18n/app.php:59
msgid "Loading…"
msgstr ""

#: cornerstone/includes/i18n/app.php:60
msgid "You don&apos;t have permission to do that."
msgstr ""

#: cornerstone/includes/i18n/app.php:61
msgid "WordPress Admin"
msgstr ""

#: cornerstone/includes/i18n/app.php:63
msgid "You have unsaved changes that will be lost. Would you like to proceed?"
msgstr ""

#: cornerstone/includes/i18n/app.php:70
msgid "Blank Canvas"
msgstr ""

#: cornerstone/includes/i18n/app.php:71
msgid "Clone Existing"
msgstr ""

#: cornerstone/includes/i18n/app.php:73
msgid "Copy {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:74
msgid "Copy of {{title}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:75
msgid "{{title}} ({{index}})"
msgstr ""

#: cornerstone/includes/i18n/app.php:76
msgid "{{title}} (Copy)"
msgstr ""

#: cornerstone/includes/i18n/app.php:78, cornerstone/includes/i18n/common.php:13
msgid "{{label}} {{index}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:79
msgid "{{context}} {{label}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:80
msgid "{{label}} ({{context}})"
msgstr ""

#: cornerstone/includes/i18n/app.php:82
msgid "Search {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:83
msgid "Go Validate"
msgstr ""

#: cornerstone/includes/i18n/app.php:84
msgid "Your license must be validated before installing."
msgstr ""

#: cornerstone/includes/i18n/app.php:86
msgid "{{context}} Title"
msgstr ""

#: cornerstone/includes/i18n/app.php:88
msgid "Back to {{to}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:91, cornerstone/includes/i18n/common.php:15
msgid "Add {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:92
msgid "Add Item"
msgstr ""

#: cornerstone/includes/i18n/app.php:94
msgid "Add New"
msgstr ""

#: cornerstone/includes/i18n/app.php:96, cornerstone/includes/i18n/common.php:6
msgid "Untitled"
msgstr ""

#: cornerstone/includes/i18n/app.php:97
msgid "Untitled {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:98, cornerstone/includes/i18n/app.php:213
msgid "Document"
msgstr ""

#: cornerstone/includes/i18n/app.php:99
msgid "Documents"
msgstr ""

#: cornerstone/includes/i18n/app.php:101
msgid "Edit {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:102
msgid "Would you like to go edit this {{context}}?"
msgstr ""

#: cornerstone/includes/i18n/app.php:103, cornerstone/includes/i18n/common.php:86
msgid "Unassigned"
msgstr ""

#: cornerstone/includes/i18n/app.php:105
msgid "Select a Key"
msgstr ""

#: cornerstone/includes/i18n/app.php:106
msgid "Select {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:107
msgid "{{context}} Selected"
msgstr ""

#: cornerstone/includes/i18n/app.php:108
msgid "– Choose –"
msgstr ""

#: cornerstone/includes/i18n/app.php:109
msgid "Cust."
msgstr ""

#: cornerstone/includes/i18n/app.php:112
msgid "Save {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:113
msgid "Save and Name"
msgstr ""

#: cornerstone/includes/i18n/app.php:115
msgid "Created New {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:116
msgid "Remove"
msgstr ""

#: cornerstone/includes/i18n/app.php:117
msgid "Create {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:119
msgid "Insert {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:121
msgid "Default {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:123
msgid "{{context}} Name"
msgstr ""

#: cornerstone/includes/i18n/app.php:124
msgid "Name {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:125
msgid "No {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:126
msgid "No {{context}} Found"
msgstr ""

#: cornerstone/includes/i18n/app.php:127
msgid "Try refining your search to locate your desired {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:128
msgid "Click + to create new {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:129
msgid "{{type}} Settings"
msgstr ""

#: cornerstone/includes/i18n/app.php:130
msgid "{{context}} CSS"
msgstr ""

#: cornerstone/includes/i18n/app.php:131
msgid "{{context}} JS"
msgstr ""

#: cornerstone/includes/i18n/app.php:132
msgid "CSS"
msgstr ""

#: cornerstone/includes/i18n/app.php:133
msgid "JavaScript"
msgstr ""

#: cornerstone/includes/i18n/app.php:134, cornerstone/includes/classes/Services/Conditionals.php:195, cornerstone/includes/classes/Services/Conditionals.php:302, cornerstone/includes/classes/Services/Conditionals.php:468, cornerstone/includes/classes/Services/Conditionals.php:639, cornerstone/includes/elements/classic/_alternate/recent-posts.php:34
msgid "Post Type"
msgstr ""

#: cornerstone/includes/i18n/app.php:135, cornerstone/includes/classes/Documents/Content.php:566, cornerstone/includes/classes/Services/Conditionals.php:670
msgid "Page Template"
msgstr ""

#: cornerstone/includes/i18n/app.php:137
msgid "{{context}} Created"
msgstr ""

#: cornerstone/includes/i18n/app.php:138
msgid "{{context}} Added"
msgstr ""

#: cornerstone/includes/i18n/app.php:139
msgid "{{context}} Moved"
msgstr ""

#: cornerstone/includes/i18n/app.php:140
msgid "{{context}} Duplicated"
msgstr ""

#: cornerstone/includes/i18n/app.php:141
msgid "{{context}} Deleted"
msgstr ""

#: cornerstone/includes/i18n/app.php:142
msgid "{{context}} Updated"
msgstr ""

#: cornerstone/includes/i18n/app.php:146, cornerstone/includes/integration/Api/ApiControls.php:16
msgid "Run"
msgstr ""

#: cornerstone/includes/i18n/app.php:148
msgid "Download"
msgstr ""

#: cornerstone/includes/i18n/app.php:149
msgid "Download ({{count}})"
msgstr ""

#: cornerstone/includes/i18n/app.php:150
msgid "Delete ({{count}})"
msgstr ""

#: cornerstone/includes/i18n/app.php:151
msgid "{{context}} {{subcontext}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:152, cornerstone/includes/classes/Services/Conditionals.php:838
msgid "Last Modified"
msgstr ""

#: cornerstone/includes/i18n/app.php:155
msgid "Import"
msgstr ""

#: cornerstone/includes/i18n/app.php:156
msgid "Export"
msgstr ""

#: cornerstone/includes/i18n/app.php:157
msgid "Export Documents"
msgstr ""

#: cornerstone/includes/i18n/app.php:158
msgid "Alpha feature, this is not going to work on large sites"
msgstr ""

#: cornerstone/includes/i18n/app.php:159
msgid "Existing {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:160
msgid "Multiple"
msgstr ""

#: cornerstone/includes/i18n/app.php:161
msgid "Yes"
msgstr ""

#: cornerstone/includes/i18n/app.php:162
msgid "No"
msgstr ""

#: cornerstone/includes/i18n/app.php:164, cornerstone/includes/i18n/common.php:18, cornerstone/includes/i18n/common.php:34
msgid "Launch"
msgstr ""

#: cornerstone/includes/i18n/app.php:167
msgid "All Documents"
msgstr ""

#: cornerstone/includes/i18n/app.php:168
msgid "Disabled"
msgstr ""

#: cornerstone/includes/i18n/app.php:176
msgid "Private"
msgstr ""

#: cornerstone/includes/i18n/app.php:182
msgid "Resize"
msgstr ""

#: cornerstone/includes/i18n/app.php:184
msgid "Expand"
msgstr ""

#: cornerstone/includes/i18n/app.php:185
msgid "Collapse"
msgstr ""

#: cornerstone/includes/i18n/app.php:190
msgid "Preview Size"
msgstr ""

#: cornerstone/includes/i18n/app.php:191
msgid "Extra Large"
msgstr ""

#: cornerstone/includes/i18n/app.php:192, cornerstone/includes/elements/classic/_alternate/button.php:75
msgid "Large"
msgstr ""

#: cornerstone/includes/i18n/app.php:193
msgid "Medium"
msgstr ""

#: cornerstone/includes/i18n/app.php:194, cornerstone/includes/elements/classic/_alternate/button.php:73
msgid "Small"
msgstr ""

#: cornerstone/includes/i18n/app.php:195
msgid "Extra Small"
msgstr ""

#: cornerstone/includes/i18n/app.php:201
msgid "1200px &amp; Up"
msgstr ""

#: cornerstone/includes/i18n/app.php:202
msgid "979px-1200px"
msgstr ""

#: cornerstone/includes/i18n/app.php:203
msgid "767px-979px"
msgstr ""

#: cornerstone/includes/i18n/app.php:204
msgid "480px-767px"
msgstr ""

#: cornerstone/includes/i18n/app.php:205
msgid "320px-480px"
msgstr ""

#: cornerstone/includes/i18n/app.php:206
msgid "{{size}} <span>{{desc}}</span>"
msgstr ""

#: cornerstone/includes/i18n/app.php:212
msgid "Managers"
msgstr ""

#: cornerstone/includes/i18n/app.php:216
msgid "Globals"
msgstr ""

#: cornerstone/includes/i18n/app.php:219
msgid "Cheatsheet"
msgstr ""

#: cornerstone/includes/i18n/app.php:220
msgid "Shortcuts"
msgstr ""

#: cornerstone/includes/i18n/app.php:221, cornerstone/includes/i18n/app.php:234, cornerstone/includes/views/admin/home-box-support.php:4, cornerstone/includes/views/admin/home-box-support.php:32
msgid "Support"
msgstr ""

#: cornerstone/includes/i18n/app.php:222
msgid "Forum"
msgstr ""

#: cornerstone/includes/i18n/app.php:223
msgid "Help"
msgstr ""

#: cornerstone/includes/i18n/app.php:227
msgid "Search our thorough online documenation to answer any question you might have."
msgstr ""

#: cornerstone/includes/i18n/app.php:230
msgid "Videos"
msgstr ""

#: cornerstone/includes/i18n/app.php:231
msgid "Go deeper with an extensive library of helpful content demonstrating new tips and tricks."
msgstr ""

#: cornerstone/includes/i18n/app.php:235
msgid "Still having trouble? Our team is available to help 24/7/365 in our online forum."
msgstr ""

#: cornerstone/includes/i18n/app.php:238
msgid "Theme Options imported successfully. Please reload Cornerstone"
msgstr ""

#: cornerstone/includes/i18n/app.php:243
msgid "Error"
msgstr ""

#: cornerstone/includes/i18n/app.php:244, cornerstone/includes/i18n/app.php:543
msgid "Uh oh!"
msgstr ""

#: cornerstone/includes/i18n/app.php:245
msgid "Details helpful to Themeco support can be found in the browser&apos;s developer tools console."
msgstr ""

#: cornerstone/includes/i18n/app.php:246
msgid "Document Error: {{context}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:248
msgid "Could not parse JSON"
msgstr ""

#: cornerstone/includes/i18n/app.php:251
msgid "Uploaded file successfully"
msgstr ""

#: cornerstone/includes/i18n/app.php:252
msgid "Please send an attachment name to upload the file"
msgstr ""

#: cornerstone/includes/i18n/app.php:261
msgid "Add your first Row to this Section."
msgstr ""

#: cornerstone/includes/i18n/app.php:262
msgid "Add some Sections before saving a Template"
msgstr ""

#: cornerstone/includes/i18n/app.php:263
msgid "Remove Spacing"
msgstr ""

#: cornerstone/includes/i18n/app.php:264
msgid "Would you like to remove all margin and padding from this section, its rows and its columns?"
msgstr ""

#: cornerstone/includes/i18n/app.php:272
msgid "Classic Elements"
msgstr ""

#: cornerstone/includes/i18n/app.php:273
msgid "Does not include advanced controls."
msgstr ""

#: cornerstone/includes/i18n/app.php:275
msgid "Are you sure you want to delete this {{title}}?"
msgstr ""

#: cornerstone/includes/i18n/app.php:276
msgid "Are you sure you want to delete this element&apos;s contents?"
msgstr ""

#: cornerstone/includes/i18n/app.php:278, cornerstone/includes/elements/definitions/tp-bbp-dropdown.php:84
msgid "Favorites"
msgstr ""

#: cornerstone/includes/i18n/app.php:280
msgid "Element Template"
msgstr ""

#: cornerstone/includes/i18n/app.php:282
msgid "This element could not render due to invalid template markup. This could be due to changes introduced by a third party plugin or invalid HTML. The front end of your site should still function normally"
msgstr ""

#: cornerstone/includes/i18n/app.php:287
msgid "No Active Document"
msgstr ""

#: cornerstone/includes/i18n/app.php:288
msgid "Use the Cornerstone menu to open a Document or click + to create a new one."
msgstr ""

#: cornerstone/includes/i18n/app.php:293, cornerstone/includes/i18n/app.php:591
msgid "Nothing Selected"
msgstr ""

#: cornerstone/includes/i18n/app.php:294
msgid "Click an element in the site preview, or use the magnifying glass icon on elements in the <strong>Outline</strong>"
msgstr ""

#: cornerstone/includes/i18n/app.php:295
msgid "Search Inspector..."
msgstr ""

#: cornerstone/includes/i18n/app.php:296
msgid "This action will replace all element styling. Proceed?"
msgstr ""

#: cornerstone/includes/i18n/app.php:298, cornerstone/includes/i18n/common.php:81
msgid "Undefined Element"
msgstr ""

#: cornerstone/includes/i18n/app.php:299
msgid "The definition for this element could not be located. You may need to activate a plugin. The type declared for this element is: <strong>{{type}}</strong>"
msgstr ""

#: cornerstone/includes/i18n/app.php:300
msgid "This element does not have any Inspector controls."
msgstr ""

#: cornerstone/includes/i18n/app.php:302
msgid "This Element is Locked"
msgstr ""

#: cornerstone/includes/i18n/app.php:303
msgid "You do not have permission to inspect this element type"
msgstr ""

#: cornerstone/includes/i18n/app.php:304
msgid "Unlock"
msgstr ""

#: cornerstone/includes/i18n/app.php:305
msgid "Locked Elements are closed off to internal edits and cannot be duplicated or deleted. Go to Element Manager to unlock."
msgstr ""

#: cornerstone/includes/i18n/app.php:306
msgid "Locked Elements are closed off to internal edits and cannot be duplicated or deleted. Seek an adminstrator who can unlock this element or make the changes you need."
msgstr ""

#: cornerstone/includes/i18n/app.php:308
msgid "Classic Element"
msgstr ""

#: cornerstone/includes/i18n/app.php:309
msgid "This is a classic element. These are supported, but have less controls to configure."
msgstr ""

#: cornerstone/includes/i18n/app.php:319
msgid "No icons found."
msgstr ""

#: cornerstone/includes/i18n/app.php:321
msgid "Double click to edit."
msgstr ""

#: cornerstone/includes/i18n/app.php:327
msgid "No Shrink"
msgstr ""

#: cornerstone/includes/i18n/app.php:329
msgid "Fill Space Equally"
msgstr ""

#: cornerstone/includes/i18n/app.php:331
msgid "Shrink"
msgstr ""

#: cornerstone/includes/i18n/app.php:332
msgid "Basis"
msgstr ""

#: cornerstone/includes/i18n/app.php:357
msgid "Stack"
msgstr ""

#: cornerstone/includes/i18n/app.php:358
msgid "Would you like to install the Starter Site? This gives you example pages, layouts, and content."
msgstr ""

#: cornerstone/includes/i18n/app.php:359
msgid "No, just change my stack"
msgstr ""

#: cornerstone/includes/i18n/app.php:374
msgid "Edit CSS"
msgstr ""

#: cornerstone/includes/i18n/app.php:375
msgid "Edit Parameters"
msgstr ""

#: cornerstone/includes/i18n/app.php:378
msgid "/*\n\nUse \"$el\" in this editor to\ntarget this element (it will\nbecome the generated class).\n\ne.g.\n\n$el    { property: value; }\n↓\n.el123 { property: value; }\n\n*/"
msgstr ""

#: cornerstone/includes/i18n/app.php:379
msgid "Toggle Hash"
msgstr ""

#: cornerstone/includes/i18n/app.php:380
msgid "Open this toggleable via the # in the url. So open-toggle as the toggle hash would then be opened from the URL #open-toggle"
msgstr ""

#: cornerstone/includes/i18n/app.php:386
msgid "Add a filter."
msgstr ""

#: cornerstone/includes/i18n/app.php:387
msgid "Add a transform."
msgstr ""

#: cornerstone/includes/i18n/app.php:388
msgid "Get started by manually writing in a filter below or add one using the + above."
msgstr ""

#: cornerstone/includes/i18n/app.php:389
msgid "Get started by manually writing in a transform below or add one using the + above."
msgstr ""

#: cornerstone/includes/i18n/app.php:391
msgid "{{family}} ({{source}})"
msgstr ""

#: cornerstone/includes/i18n/app.php:392
msgid "Should auld acquaintance be forgot, and never brought to mind? The flames of Love extinguished, and fully past and gone"
msgstr ""

#: cornerstone/includes/i18n/app.php:393
msgid "Not Fonts found for this search query"
msgstr ""

#: cornerstone/includes/i18n/app.php:394
msgid "Click to Edit"
msgstr ""

#: cornerstone/includes/i18n/app.php:395
msgid "Edit Text"
msgstr ""

#: cornerstone/includes/i18n/app.php:396, cornerstone/includes/elements/definitions/lottie.php:241
msgid "HTML"
msgstr ""

#: cornerstone/includes/i18n/app.php:397, cornerstone/includes/integration/Api/ApiRaw.php:5, cornerstone/includes/integration/Api/ApiRaw.php:23
msgid "Raw"
msgstr ""

#: cornerstone/includes/i18n/app.php:398, cornerstone/includes/integration/tinymce.php:20
msgid "Rich Text"
msgstr ""

#: cornerstone/includes/i18n/app.php:401
msgid "Unlinked"
msgstr ""

#: cornerstone/includes/i18n/app.php:402
msgid "Linked"
msgstr ""

#: cornerstone/includes/i18n/app.php:403
msgid "Link Sides"
msgstr ""

#: cornerstone/includes/i18n/app.php:404
msgid "Side"
msgstr ""

#: cornerstone/includes/i18n/app.php:410
msgid "Spread &amp;<br>Position"
msgstr ""

#: cornerstone/includes/i18n/app.php:419
msgctxt "Short version of bottom for dimension control"
msgid "Bttm"
msgstr ""

#: cornerstone/includes/i18n/app.php:426
msgctxt "Short version of bottom for dimension control"
msgid "Bttm Right"
msgstr ""

#: cornerstone/includes/i18n/app.php:427
msgctxt "Short version of bottom for dimension control"
msgid "Bttm Left"
msgstr ""

#: cornerstone/includes/i18n/app.php:436, cornerstone/includes/elements/classic/_alternate/button.php:143, cornerstone/includes/elements/classic/_alternate/image.php:108
msgid "Popover"
msgstr ""

#: cornerstone/includes/i18n/app.php:440, cornerstone/includes/elements/classic/_alternate/button.php:179, cornerstone/includes/elements/classic/_alternate/image.php:148
msgid "Focus"
msgstr ""

#: cornerstone/includes/i18n/app.php:445
msgid "Audio URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:446
msgid "Preload<br>Content"
msgstr ""

#: cornerstone/includes/i18n/app.php:453
msgid "Display &<br>Function"
msgstr ""

#: cornerstone/includes/i18n/app.php:455
msgid "Select Widget Area"
msgstr ""

#: cornerstone/includes/i18n/app.php:463, cornerstone/includes/elements/classic/_alternate/custom-headline.php:54, cornerstone/includes/elements/classic/_alternate/feature-headline.php:54, cornerstone/includes/elements/classic/_alternate/text-type.php:80
msgid "Looks Like"
msgstr ""

#: cornerstone/includes/i18n/app.php:469
msgid "Line Through"
msgstr ""

#: cornerstone/includes/i18n/app.php:478
msgid "Uppercase"
msgstr ""

#: cornerstone/includes/i18n/app.php:479
msgid "Capitalize"
msgstr ""

#: cornerstone/includes/i18n/app.php:480
msgid "Lowercase"
msgstr ""

#: cornerstone/includes/i18n/app.php:484
msgid "Dimensions Preview"
msgstr ""

#: cornerstone/includes/i18n/app.php:489, cornerstone/includes/elements/classic/alert/controls.php:40, cornerstone/includes/elements/classic/_alternate/button.php:137, cornerstone/includes/elements/classic/_alternate/image.php:102
msgid "Info"
msgstr ""

#: cornerstone/includes/i18n/app.php:490
msgid "W"
msgstr ""

#: cornerstone/includes/i18n/app.php:495
msgid "Attachment Srcset"
msgstr ""

#: cornerstone/includes/i18n/app.php:496
msgid "If using an Attachment ID, this will use srcset based on your configured image sizes"
msgstr ""

#: cornerstone/includes/i18n/app.php:497
msgid "Decorative"
msgstr ""

#: cornerstone/includes/i18n/app.php:498
msgid "When set this will not add in an alt tag"
msgstr ""

#: cornerstone/includes/i18n/app.php:507
msgid "Row Reverse"
msgstr ""

#: cornerstone/includes/i18n/app.php:508
msgid "Column Reverse"
msgstr ""

#: cornerstone/includes/i18n/app.php:520
msgid "e.g. http://theme.co/"
msgstr ""

#: cornerstone/includes/i18n/app.php:521
msgid "New Tab"
msgstr ""

#: cornerstone/includes/i18n/app.php:522
msgid "nofollow"
msgstr ""

#: cornerstone/includes/i18n/app.php:524
msgid "e.g. <EMAIL>"
msgstr ""

#: cornerstone/includes/i18n/app.php:525
msgid "Subject"
msgstr ""

#: cornerstone/includes/i18n/app.php:526
msgid "e.g. Howdy!"
msgstr ""

#: cornerstone/includes/i18n/app.php:527
msgid "Phone"
msgstr ""

#: cornerstone/includes/i18n/app.php:528
msgid "e.g. 18885551234"
msgstr ""

#: cornerstone/includes/i18n/app.php:530
msgid "Share"
msgstr ""

#: cornerstone/includes/i18n/app.php:541
msgid "Hey!"
msgstr ""

#: cornerstone/includes/i18n/app.php:542
msgid "Awesome!"
msgstr ""

#: cornerstone/includes/i18n/app.php:544
msgid "Here we go!"
msgstr ""

#: cornerstone/includes/i18n/app.php:545
msgid "All done!"
msgstr ""

#: cornerstone/includes/i18n/app.php:547
msgid "Refreshing preview."
msgstr ""

#: cornerstone/includes/i18n/app.php:548
msgid "Refreshing preview. Don't forget to save."
msgstr ""

#: cornerstone/includes/i18n/app.php:550
msgid "Saved!"
msgstr ""

#: cornerstone/includes/i18n/app.php:551
msgid "Failed to save."
msgstr ""

#: cornerstone/includes/i18n/app.php:552
msgid "Saved {{context}}!"
msgstr ""

#: cornerstone/includes/i18n/app.php:553
msgid "Failed to save {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:554
msgid "Downloaded {{context}}!"
msgstr ""

#: cornerstone/includes/i18n/app.php:555
msgid "Failed to download {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:556
msgid "Failed to download {{context}}. {{message}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:557
msgid "Are you sure you want to delete this {{context}}?"
msgstr ""

#: cornerstone/includes/i18n/app.php:558
msgid "Are you sure you want to delete this {{context}}? This can not be undone."
msgstr ""

#: cornerstone/includes/i18n/app.php:559
msgid "Duplicated {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:560
msgid "Failed to duplicate {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:561
msgid "Deleted {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:562
msgid "Failed to delete {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:563
msgid "{{context}} title updated."
msgstr ""

#: cornerstone/includes/i18n/app.php:564
msgid "Failed to update {{context}} title."
msgstr ""

#: cornerstone/includes/i18n/app.php:565
msgid "{{context}} created!"
msgstr ""

#: cornerstone/includes/i18n/app.php:566, cornerstone/includes/i18n/app.php:1064
msgid "Failed to create {{context}}. {{message}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:567
msgid "Updated {{context}}!"
msgstr ""

#: cornerstone/includes/i18n/app.php:568
msgid "Failed to update {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:569
msgid "Loading {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:570
msgid "{{context}} inserted!"
msgstr ""

#: cornerstone/includes/i18n/app.php:571
msgid "{{context}} inserted at the bottom of this document!"
msgstr ""

#: cornerstone/includes/i18n/app.php:572
msgid "Failed to insert {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:573
msgid "Installing {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:574
msgid "Finishing Install."
msgstr ""

#: cornerstone/includes/i18n/app.php:575
msgid "{{context}} installed!"
msgstr ""

#: cornerstone/includes/i18n/app.php:576
msgid "Failed to install {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:577
msgid "Your {{context}} needs a title."
msgstr ""

#: cornerstone/includes/i18n/app.php:578
msgid "Your {{context}} needs a name."
msgstr ""

#: cornerstone/includes/i18n/app.php:579
msgid "An existing {{context}} already has that name. Would you like to overwrite it?"
msgstr ""

#: cornerstone/includes/i18n/app.php:580
msgid "Preview Updating."
msgstr ""

#: cornerstone/includes/i18n/app.php:581
msgid "This element already has the maximum number of children."
msgstr ""

#: cornerstone/includes/i18n/app.php:582
msgid "This element requires a minimum number of children."
msgstr ""

#: cornerstone/includes/i18n/app.php:583
msgid "Rich Text Editing is disabled because we found a problem with your Wordpress Visual Editor. You can still edit the text in HTML mode."
msgstr ""

#: cornerstone/includes/i18n/app.php:584
msgid "{{context}} elements have been cloned!"
msgstr ""

#: cornerstone/includes/i18n/app.php:585
msgid "Failed to clone {{context}} elements."
msgstr ""

#: cornerstone/includes/i18n/app.php:586
msgid "No elements found in this {{context}}."
msgstr ""

#: cornerstone/includes/i18n/app.php:592
msgid "No Presets"
msgstr ""

#: cornerstone/includes/i18n/app.php:595
msgid "Replace Content"
msgstr ""

#: cornerstone/includes/i18n/app.php:596
msgid "Yes, Apply"
msgstr ""

#: cornerstone/includes/i18n/app.php:597
msgid "No thanks"
msgstr ""

#: cornerstone/includes/i18n/app.php:603
msgid "Yes, Proceed"
msgstr ""

#: cornerstone/includes/i18n/app.php:604
msgid "No Thanks"
msgstr ""

#: cornerstone/includes/i18n/app.php:605
msgid "No, Go Back"
msgstr ""

#: cornerstone/includes/i18n/app.php:612
msgid "Info:"
msgstr ""

#: cornerstone/includes/i18n/app.php:618
msgid "Current document type <strong>{{context}}</strong><br/>Only compatible templates are shown."
msgstr ""

#: cornerstone/includes/i18n/app.php:619
msgid "Open a document to view available templates."
msgstr ""

#: cornerstone/includes/i18n/app.php:625
msgid "Template Manager"
msgstr ""

#: cornerstone/includes/i18n/app.php:627
msgid "Use Template"
msgstr ""

#: cornerstone/includes/i18n/app.php:629
msgid "{{context}} Template"
msgstr ""

#: cornerstone/includes/i18n/app.php:630
msgid "{{context}} Templates"
msgstr ""

#: cornerstone/includes/i18n/app.php:631, cornerstone/includes/i18n/app.php:663
msgid "Preset"
msgstr ""

#: cornerstone/includes/i18n/app.php:632, cornerstone/includes/i18n/app.php:653
msgid "Presets"
msgstr ""

#: cornerstone/includes/i18n/app.php:633
msgid "Save as Template"
msgstr ""

#: cornerstone/includes/i18n/app.php:634
msgid "Load Template"
msgstr ""

#: cornerstone/includes/i18n/app.php:635
msgid "Save Template"
msgstr ""

#: cornerstone/includes/i18n/app.php:636
msgid "Save a snapshot of this element into your Library"
msgstr ""

#: cornerstone/includes/i18n/app.php:637
msgid "No Templates"
msgstr ""

#: cornerstone/includes/i18n/app.php:638
msgid "No {{context}} Templates"
msgstr ""

#: cornerstone/includes/i18n/app.php:639
msgid "Visit Template Manager"
msgstr ""

#: cornerstone/includes/i18n/app.php:640
msgid "Begin with a blank slate."
msgstr ""

#: cornerstone/includes/i18n/app.php:642
msgid "Are you sure you want to delete the selected template?"
msgstr ""

#: cornerstone/includes/i18n/app.php:643
msgid "Are you sure you want to delete the {{count}} selected templates?"
msgstr ""

#: cornerstone/includes/i18n/app.php:645
msgid "Are you sure you want to remove the thumbnail? Auto-generated thumbnails can not be restored"
msgstr ""

#: cornerstone/includes/i18n/app.php:648
msgid "Sites"
msgstr ""

#: cornerstone/includes/i18n/app.php:652, cornerstone/includes/i18n/common.php:50
msgid "Layouts"
msgstr ""

#: cornerstone/includes/i18n/app.php:654
msgid "My Templates"
msgstr ""

#: cornerstone/includes/i18n/app.php:658, cornerstone/includes/classes/Services/Conditionals.php:67, cornerstone/includes/classes/Services/ElementLibrary.php:35
msgid "Site"
msgstr ""

#: cornerstone/includes/i18n/app.php:664
msgid "<strong>{{type}}</strong>: {{subtype}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:665
msgid "Element Defaults"
msgstr ""

#: cornerstone/includes/i18n/app.php:666
msgid "When adding new Elements in the builders they will start with the Preset you assign."
msgstr ""

#: cornerstone/includes/i18n/app.php:668
msgid "Importing Template File"
msgstr ""

#: cornerstone/includes/i18n/app.php:669
msgid "Adding Template File"
msgstr ""

#: cornerstone/includes/i18n/app.php:670
msgid "File Imported!"
msgstr ""

#: cornerstone/includes/i18n/app.php:671
msgid "This file you chose was not valid and could not be imported."
msgstr ""

#: cornerstone/includes/i18n/app.php:672
msgid "File Failed to import. {{message}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:673
msgid "Failed to import image batch. Your server upload limit is probably preventing this upload from happening"
msgstr ""

#: cornerstone/includes/i18n/app.php:675
msgid "It looks like you have imported this template package before. Would you like to import it again?"
msgstr ""

#: cornerstone/includes/i18n/app.php:676
msgid "View your Templates to the right."
msgstr ""

#: cornerstone/includes/i18n/app.php:678
msgid "Remove Preview Image"
msgstr ""

#: cornerstone/includes/i18n/app.php:680
msgid "Working on it..."
msgstr ""

#: cornerstone/includes/i18n/app.php:681
msgid "Hang in there, trying to reconnect..."
msgstr ""

#: cornerstone/includes/i18n/app.php:682
msgid "Experiencing technical difficulties..."
msgstr ""

#: cornerstone/includes/i18n/app.php:683
msgid "We&apos;re sorry, the demo failed to finish importing."
msgstr ""

#: cornerstone/includes/i18n/app.php:690
msgid "to Document"
msgstr ""

#: cornerstone/includes/i18n/app.php:691
msgid "to Library"
msgstr ""

#: cornerstone/includes/i18n/app.php:692
msgid "Import Site"
msgstr ""

#: cornerstone/includes/i18n/app.php:693
msgid "Importing Site Templates"
msgstr ""

#: cornerstone/includes/i18n/app.php:694
msgid "All to Library"
msgstr ""

#: cornerstone/includes/i18n/app.php:695
msgid "+ Document"
msgstr ""

#: cornerstone/includes/i18n/app.php:696
msgid "Click this to reset the Max cache. It will fetch the latest purchase history and course data"
msgstr ""

#: cornerstone/includes/i18n/app.php:697
msgid "Asset Added to Document."
msgstr ""

#: cornerstone/includes/i18n/app.php:698
msgid "Asset Added to Library"
msgstr ""

#: cornerstone/includes/i18n/app.php:699
msgid "This template name already exists ({{name}}) do you want to import another copy?"
msgstr ""

#: cornerstone/includes/i18n/app.php:700
msgid "About Max"
msgstr ""

#: cornerstone/includes/i18n/app.php:702
msgid "Or Get Access to {{title}} + All Courses, Expansion Packs, & Plugins When You "
msgstr ""

#: cornerstone/includes/i18n/app.php:703
msgid "Subscribe to Max"
msgstr ""

#: cornerstone/includes/i18n/app.php:707
msgid "Exit Selection"
msgstr ""

#: cornerstone/includes/i18n/app.php:708
msgid "Make Section Template"
msgstr ""

#: cornerstone/includes/i18n/app.php:715
msgid "Font Selection"
msgstr ""

#: cornerstone/includes/i18n/app.php:716
msgid "Font Manager"
msgstr ""

#: cornerstone/includes/i18n/app.php:717
msgid "Once a new font has been added, click the arrow to the side of the label to reveal the selection tool for font families and weights. Double-click the label to rename the added font if desired. Make sure to save all changes before exiting."
msgstr ""

#: cornerstone/includes/i18n/app.php:719
msgid "Two fonts selections can not share the same name."
msgstr ""

#: cornerstone/includes/i18n/app.php:720
msgid "You need at least one font. Create another before deleting this one."
msgstr ""

#: cornerstone/includes/i18n/app.php:722
msgid "Select a Font Family"
msgstr ""

#: cornerstone/includes/i18n/app.php:724
msgid "Type here to change preview text..."
msgstr ""

#: cornerstone/includes/i18n/app.php:725
msgid "Upload Fonts"
msgstr ""

#: cornerstone/includes/i18n/app.php:726
msgid "Select Font File(s)"
msgstr ""

#: cornerstone/includes/i18n/app.php:729
msgid "Bold"
msgstr ""

#: cornerstone/includes/i18n/app.php:730
msgid "Normal ({{weight}})"
msgstr ""

#: cornerstone/includes/i18n/app.php:731
msgid "Bold ({{weight}})"
msgstr ""

#: cornerstone/includes/i18n/app.php:732, cornerstone/includes/classes/Services/FontAwesome.php:106, cornerstone/includes/elements/classic/_alternate/button.php:74
msgid "Regular"
msgstr ""

#: cornerstone/includes/i18n/app.php:734
msgid "Import Font Files"
msgstr ""

#: cornerstone/includes/i18n/app.php:735
msgid "Add to Font Manager"
msgstr ""

#: cornerstone/includes/i18n/app.php:736
msgid "Font Item Name"
msgstr ""

#: cornerstone/includes/i18n/app.php:737
msgid "Palette Fonts"
msgstr ""

#: cornerstone/includes/i18n/app.php:738
msgid "Managed Weights"
msgstr ""

#: cornerstone/includes/i18n/app.php:739
msgid "Parent Font"
msgstr ""

#: cornerstone/includes/i18n/app.php:740
msgid "Parent Weight"
msgstr ""

#: cornerstone/includes/i18n/app.php:741
msgid "Exact Weights"
msgstr ""

#: cornerstone/includes/i18n/app.php:742
msgid "Exact Weight"
msgstr ""

#: cornerstone/includes/i18n/app.php:744
msgid "Enable Subsets"
msgstr ""

#: cornerstone/includes/i18n/app.php:745
msgid "Additional Subsets"
msgstr ""

#: cornerstone/includes/i18n/app.php:747
msgid "Google Fonts URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:748
msgid "Use another service that is supports the Google Fonts format. Such as bunnyfonts (https://fonts.bunny.net/css)"
msgstr ""

#: cornerstone/includes/i18n/app.php:750
msgid "Project ID"
msgstr ""

#: cornerstone/includes/i18n/app.php:751
msgid "Load as CSS"
msgstr ""

#: cornerstone/includes/i18n/app.php:752
msgid "Load through an async JS script or load through a CSS import tag"
msgstr ""

#: cornerstone/includes/i18n/app.php:753
msgid "Available"
msgstr ""

#: cornerstone/includes/i18n/app.php:755
msgid "Weights"
msgstr ""

#: cornerstone/includes/i18n/app.php:756
msgid "Enter your Adobe Fonts project ID above and click refresh."
msgstr ""

#: cornerstone/includes/i18n/app.php:757
msgid "Your project was not found. Please check to make sure the project ID is correct and try again."
msgstr ""

#: cornerstone/includes/i18n/app.php:758
msgid "Refreshing&hellip;"
msgstr ""

#: cornerstone/includes/i18n/app.php:759
msgid "Refresh"
msgstr ""

#: cornerstone/includes/i18n/app.php:761, cornerstone/includes/i18n/app.php:770
msgid "Custom Fonts"
msgstr ""

#: cornerstone/includes/i18n/app.php:762, cornerstone/includes/i18n/app.php:768
msgid "Adobe Fonts (Typekit)"
msgstr ""

#: cornerstone/includes/i18n/app.php:763
msgid "System Fonts"
msgstr ""

#: cornerstone/includes/i18n/app.php:764
msgid "Google Fonts"
msgstr ""

#: cornerstone/includes/i18n/app.php:767
msgid "Specify which character subsets you would like to enable below. All subsets included extended support. Latin is always loaded by default."
msgstr ""

#: cornerstone/includes/i18n/app.php:769
msgid "To integrate your Adobe Fonts projects on this website, enter a project ID into the input below. You can find this by logging into Adobe Fonts, clicking on <strong>Web Projects</strong> and then looking for the <strong>Project ID</strong>."
msgstr ""

#: cornerstone/includes/i18n/app.php:771
msgid "Use this section to create custom font families. Upload custom font files to a family, then assign a weight and style. These families will become selectable in the Font Manager above. We recommend using the .woff or .woff2 file format."
msgstr ""

#: cornerstone/includes/i18n/app.php:772
msgid "Custom Font Family"
msgstr ""

#: cornerstone/includes/i18n/app.php:774
msgid "Body Copy"
msgstr ""

#: cornerstone/includes/i18n/app.php:775
msgid "Headings"
msgstr ""

#: cornerstone/includes/i18n/app.php:777
msgid "Font Display"
msgstr ""

#: cornerstone/includes/i18n/app.php:778
msgid "The <code>font-display</code> property gives users control over how the timeline for fonts being loaded into the browser should be executed. The value selected will be utilized in supported browsers where applicable across your site."
msgstr ""

#: cornerstone/includes/i18n/app.php:779
msgid "Select Value"
msgstr ""

#: cornerstone/includes/i18n/app.php:781
msgid "Failed to copy. Your site is not running HTTPS or you need to use localhost as the domain if this is a local site"
msgstr ""

#: cornerstone/includes/i18n/app.php:788
msgid "Color Manager"
msgstr ""

#: cornerstone/includes/i18n/app.php:789
msgid "Once a new color has been added, clicking it will reveal the color-picker in addition to an input where you can rename each color for clearer labeling. Duplicate and delete buttons are visible on hover. Make sure to save all changes before exiting."
msgstr ""

#: cornerstone/includes/i18n/app.php:790
msgid "Two colors selections can not share the same name."
msgstr ""

#: cornerstone/includes/i18n/app.php:792
msgid "Brand Primary"
msgstr ""

#: cornerstone/includes/i18n/app.php:793
msgid "Brand Secondary"
msgstr ""

#: cornerstone/includes/i18n/app.php:795
msgid "Link Interaction"
msgstr ""

#: cornerstone/includes/i18n/app.php:797, cornerstone/includes/i18n/app.php:799
msgid "Gradient"
msgstr ""

#: cornerstone/includes/i18n/app.php:800
msgid "From"
msgstr ""

#: cornerstone/includes/i18n/app.php:801
msgid "To"
msgstr ""

#: cornerstone/includes/i18n/app.php:802
msgid "This gradient will overwrite your current gradient"
msgstr ""

#: cornerstone/includes/i18n/app.php:808
msgid "Responsive Text"
msgstr ""

#: cornerstone/includes/i18n/app.php:811
msgid "Minimum Size"
msgstr ""

#: cornerstone/includes/i18n/app.php:812
msgid "Maximum Size"
msgstr ""

#: cornerstone/includes/i18n/app.php:818
msgid "100 - Ultra Light"
msgstr ""

#: cornerstone/includes/i18n/app.php:819
msgid "100 - Ultra Light (Italic)"
msgstr ""

#: cornerstone/includes/i18n/app.php:820
msgid "200 - Light"
msgstr ""

#: cornerstone/includes/i18n/app.php:821
msgid "200 - Light (Italic)"
msgstr ""

#: cornerstone/includes/i18n/app.php:822
msgid "300 - Book"
msgstr ""

#: cornerstone/includes/i18n/app.php:823
msgid "300 - Book (Italic)"
msgstr ""

#: cornerstone/includes/i18n/app.php:824
msgid "400 - Regular"
msgstr ""

#: cornerstone/includes/i18n/app.php:825
msgid "400 - Regular (Italic)"
msgstr ""

#: cornerstone/includes/i18n/app.php:826
msgid "500 - Medium"
msgstr ""

#: cornerstone/includes/i18n/app.php:827
msgid "500 - Medium (Italic)"
msgstr ""

#: cornerstone/includes/i18n/app.php:828
msgid "600 - Semi-Bold"
msgstr ""

#: cornerstone/includes/i18n/app.php:829
msgid "600 - Semi-Bold (Italic)"
msgstr ""

#: cornerstone/includes/i18n/app.php:830
msgid "700 - Bold"
msgstr ""

#: cornerstone/includes/i18n/app.php:831
msgid "700 - Bold (Italic)"
msgstr ""

#: cornerstone/includes/i18n/app.php:832
msgid "800 - Extra Bold"
msgstr ""

#: cornerstone/includes/i18n/app.php:833
msgid "800 - Extra Bold (Italic)"
msgstr ""

#: cornerstone/includes/i18n/app.php:834
msgid "900 - Ultra Bold"
msgstr ""

#: cornerstone/includes/i18n/app.php:835
msgid "900 - Ultra Bold (Italic)"
msgstr ""

#: cornerstone/includes/i18n/app.php:841
msgid "/* Enter CSS you would like added only for this {{context}}. */ "
msgstr ""

#: cornerstone/includes/i18n/app.php:842
msgid "/* Enter CSS you would like added to your entire site. */ "
msgstr ""

#: cornerstone/includes/i18n/app.php:843
msgid "/* Enter Javascript you would like added only for this {{context}}. */ "
msgstr ""

#: cornerstone/includes/i18n/app.php:844
msgid "/* Enter Javascript you would like added to your entire site. */ "
msgstr ""

#: cornerstone/includes/i18n/app.php:850
msgid "Menu: %s"
msgstr ""

#: cornerstone/includes/i18n/app.php:851
msgid "Location: %s"
msgstr ""

#: cornerstone/includes/i18n/app.php:853
msgid "Sample"
msgstr ""

#: cornerstone/includes/i18n/app.php:854
msgid "Sample (No Dropdowns)"
msgstr ""

#: cornerstone/includes/i18n/app.php:855
msgid "Sample (Split #1)"
msgstr ""

#: cornerstone/includes/i18n/app.php:856
msgid "Sample (Split #2)"
msgstr ""

#: cornerstone/includes/i18n/app.php:864
msgid "A-Z"
msgstr ""

#: cornerstone/includes/i18n/app.php:865
msgid "Z-A"
msgstr ""

#: cornerstone/includes/i18n/app.php:866
msgid "Popular"
msgstr ""

#: cornerstone/includes/i18n/app.php:873
msgid "Duplicate"
msgstr ""

#: cornerstone/includes/i18n/app.php:875
msgid "Really Delete?"
msgstr ""

#: cornerstone/includes/i18n/app.php:876
msgid "Erase"
msgstr ""

#: cornerstone/includes/i18n/app.php:877
msgid "Really Erase?"
msgstr ""

#: cornerstone/includes/i18n/app.php:878
msgid "Manage Layout"
msgstr ""

#: cornerstone/includes/i18n/app.php:879
msgid "Show / Hide"
msgstr ""

#: cornerstone/includes/i18n/app.php:885
msgid "No suitable preview area found. This is most common when you are using a \"No Header\" page template or layout. Try changing the page template, or assigning this header to a context where the template allows the header output."
msgstr ""

#: cornerstone/includes/i18n/app.php:886
msgid "No suitable preview area found. This is most common when you are using a \"No Footer\" page template or layout. Try changing the page template, or assigning this footer to a context where the template allows the footer output."
msgstr ""

#: cornerstone/includes/i18n/app.php:887
msgid "No suitable preview area found. This could happen when a third party plugin is overriding the content area or \"The Content\" is not being output by the current layout."
msgstr ""

#: cornerstone/includes/i18n/app.php:888
msgid "No suitable preview area found. For layouts this can happen when a third party plugin is overidding the content area or the page you are viewing is not a valid type. Example you are working on an Archive, but viewing a single layout in the preview."
msgstr ""

#: cornerstone/includes/i18n/app.php:890
msgid "The preview could not load. This is most often related to a plugin conflict or aggressive page caching. Checking the developer console for errors could indicate what went wrong."
msgstr ""

#: cornerstone/includes/i18n/app.php:891
msgid "The preview could not load due to a http/https mismatch. Please check that HTTPS is properly configured on your site."
msgstr ""

#: cornerstone/includes/i18n/app.php:892
msgid "The preview could not load due to misconfigured URLs. This could happen if you are using multiple environments and the site URL was not updated after migrating."
msgstr ""

#: cornerstone/includes/i18n/app.php:893
msgid "The preview could not load due to the iframe response being incomplete. This is most often related to a plugin conflict, or customizations introducing a PHP error."
msgstr ""

#: cornerstone/includes/i18n/app.php:894
msgid "The preview was unresponsive after loading. This is most often related to a plugin conflict or aggressive page caching."
msgstr ""

#: cornerstone/includes/i18n/app.php:896
msgid "The preview HTML did not include a closing </html>; tag. It may fail to load or work properly."
msgstr ""

#: cornerstone/includes/i18n/app.php:898
msgid "Preview Reload"
msgstr ""

#: cornerstone/includes/i18n/app.php:904, cornerstone/includes/i18n/common.php:31
msgid "Preferences"
msgstr ""

#: cornerstone/includes/i18n/app.php:905
msgid "Configure how Cornerstone should look and behave. These settings are specific to your user account."
msgstr ""

#: cornerstone/includes/i18n/app.php:906
msgid "The WordPress toolbar will be shown on next reload."
msgstr ""

#: cornerstone/includes/i18n/app.php:907
msgid "The WordPress toolbar will be hidden on next reload."
msgstr ""

#: cornerstone/includes/i18n/app.php:919, cornerstone/includes/integration/QueryBuilder/Controls/OrderBy.php:57
msgid "Meta Key"
msgstr ""

#: cornerstone/includes/i18n/app.php:922, cornerstone/includes/classes/Services/Conditionals.php:994
msgid "Index"
msgstr ""

#: cornerstone/includes/i18n/app.php:923
msgid "Fallback"
msgstr ""

#: cornerstone/includes/i18n/app.php:927
msgid "Redirect"
msgstr ""

#: cornerstone/includes/i18n/app.php:928
msgid "Total Count"
msgstr ""

#: cornerstone/includes/i18n/app.php:931, cornerstone/includes/classes/Services/Conditionals.php:17, cornerstone/includes/classes/Services/Conditionals.php:69, cornerstone/includes/classes/Services/ElementLibrary.php:33
msgid "Archive"
msgstr ""

#: cornerstone/includes/i18n/app.php:934, cornerstone/includes/i18n/app.php:1139
msgid "Parameters"
msgstr ""

#: cornerstone/includes/i18n/app.php:935
msgid "Url"
msgstr ""

#: cornerstone/includes/i18n/app.php:936
msgid "User"
msgstr ""

#: cornerstone/includes/i18n/app.php:938
msgid "ACF"
msgstr ""

#: cornerstone/includes/i18n/app.php:939
msgid "Toolset"
msgstr ""

#: cornerstone/includes/i18n/app.php:941, cornerstone/includes/classes/Services/Conditionals.php:41
msgid "Looper"
msgstr ""

#: cornerstone/includes/i18n/app.php:942
msgid "Query"
msgstr ""

#: cornerstone/includes/i18n/app.php:943
msgid "Rivet"
msgstr ""

#: cornerstone/includes/i18n/app.php:947
msgid "Home URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:948
msgid "Blog URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:949
msgid "Admin URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:950
msgid "Login URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:951
msgid "Logout URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:952
msgid "Registration URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:953
msgid "Privacy Page URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:954
msgid "Current Date"
msgstr ""

#: cornerstone/includes/i18n/app.php:955
msgid "Current Time"
msgstr ""

#: cornerstone/includes/i18n/app.php:958
msgid "Query String Parameter"
msgstr ""

#: cornerstone/includes/i18n/app.php:959
msgid "Segment"
msgstr ""

#: cornerstone/includes/i18n/app.php:960
msgid "Full Path"
msgstr ""

#: cornerstone/includes/i18n/app.php:962
msgid "Display Name"
msgstr ""

#: cornerstone/includes/i18n/app.php:963
msgid "Email Address"
msgstr ""

#: cornerstone/includes/i18n/app.php:964
msgid "Gravatar URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:965
msgid "Registration Date"
msgstr ""

#: cornerstone/includes/i18n/app.php:966
msgid "Registration Time"
msgstr ""

#: cornerstone/includes/i18n/app.php:967
msgid "Author URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:968
msgid "Website URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:969
msgid "Biographical Info"
msgstr ""

#: cornerstone/includes/i18n/app.php:970
msgid "Usermeta"
msgstr ""

#: cornerstone/includes/i18n/app.php:972
msgid "Page Title"
msgstr ""

#: cornerstone/includes/i18n/app.php:973
msgid "Shop URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:974
msgid "Cart URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:975
msgid "Checkout URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:976
msgid "Account URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:977
msgid "Terms URL"
msgstr ""

#: cornerstone/includes/i18n/app.php:978
msgid "Fallback Image"
msgstr ""

#: cornerstone/includes/i18n/app.php:979
msgid "Cart Item Count (Raw)"
msgstr ""

#: cornerstone/includes/i18n/app.php:980
msgid "Cart Item Count (Realtime)"
msgstr ""

#: cornerstone/includes/i18n/app.php:981
msgid "Cart Total (Raw)"
msgstr ""

#: cornerstone/includes/i18n/app.php:982
msgid "Cart Total (Realtime)"
msgstr ""

#: cornerstone/includes/i18n/app.php:983
msgid "Cart Subtotal (Raw)"
msgstr ""

#: cornerstone/includes/i18n/app.php:984
msgid "Cart Subtotal (Realtime)"
msgstr ""

#: cornerstone/includes/i18n/app.php:985
msgid "Currency Symbol"
msgstr ""

#: cornerstone/includes/i18n/app.php:987
msgid "Product Price HTML"
msgstr ""

#: cornerstone/includes/i18n/app.php:988
msgid "Product Regular Price"
msgstr ""

#: cornerstone/includes/i18n/app.php:989
msgid "Product Sale Price"
msgstr ""

#: cornerstone/includes/i18n/app.php:990
msgid "Product Sale Percentage Off"
msgstr ""

#: cornerstone/includes/i18n/app.php:991
msgid "Product ID"
msgstr ""

#: cornerstone/includes/i18n/app.php:992
msgid "Product Class"
msgstr ""

#: cornerstone/includes/i18n/app.php:993
msgid "Product SKU"
msgstr ""

#: cornerstone/includes/i18n/app.php:995
msgid "Product Url"
msgstr ""

#: cornerstone/includes/i18n/app.php:998
msgid "Product Image Url"
msgstr ""

#: cornerstone/includes/i18n/app.php:999
msgid "Product Gallery IDs"
msgstr ""

#: cornerstone/includes/i18n/app.php:1000
msgid "Product Stock"
msgstr ""

#: cornerstone/includes/i18n/app.php:1001
msgid "Product Rating Count"
msgstr ""

#: cornerstone/includes/i18n/app.php:1002
msgid "Product Average Rating"
msgstr ""

#: cornerstone/includes/i18n/app.php:1003
msgid "Product Review Count"
msgstr ""

#: cornerstone/includes/i18n/app.php:1004
msgid "Product Description"
msgstr ""

#: cornerstone/includes/i18n/app.php:1008
msgid "Product Weight"
msgstr ""

#: cornerstone/includes/i18n/app.php:1009
msgid "Product Length"
msgstr ""

#: cornerstone/includes/i18n/app.php:1010
msgid "Product Width"
msgstr ""

#: cornerstone/includes/i18n/app.php:1011
msgid "Product Height"
msgstr ""

#: cornerstone/includes/i18n/app.php:1012
msgid "Product Dimensions"
msgstr ""

#: cornerstone/includes/i18n/app.php:1013
msgid "Product Shipping Class"
msgstr ""

#: cornerstone/includes/i18n/app.php:1014
msgid "Product Shipping Class Slug"
msgstr ""

#: cornerstone/includes/i18n/app.php:1015, cornerstone/includes/integration/woocommerce.php:269
msgid "Product Type"
msgstr ""

#: cornerstone/includes/i18n/app.php:1018
msgid "Current Item"
msgstr ""

#: cornerstone/includes/i18n/app.php:1019
msgid "Current Item Index"
msgstr ""

#: cornerstone/includes/i18n/app.php:1020
msgid "Total Item Count"
msgstr ""

#: cornerstone/includes/i18n/app.php:1021
msgid "Debug Provider"
msgstr ""

#: cornerstone/includes/i18n/app.php:1022
msgid "Debug Consumer"
msgstr ""

#: cornerstone/includes/i18n/app.php:1023
msgid "Custom Key"
msgstr ""

#: cornerstone/includes/i18n/app.php:1025
msgid "Current Page Number"
msgstr ""

#: cornerstone/includes/i18n/app.php:1026
msgid "Total Posts Found"
msgstr ""

#: cornerstone/includes/i18n/app.php:1027
msgid "Total Pages"
msgstr ""

#: cornerstone/includes/i18n/app.php:1028
msgid "Search Query"
msgstr ""

#: cornerstone/includes/i18n/app.php:1029
msgid "Query Var"
msgstr ""

#: cornerstone/includes/i18n/app.php:1031
msgid "Element Parameters"
msgstr ""

#: cornerstone/includes/i18n/app.php:1036
msgid "Global Block Name"
msgstr ""

#: cornerstone/includes/i18n/app.php:1037
msgid "Click + to create your first Component set."
msgstr ""

#: cornerstone/includes/i18n/app.php:1038
msgid "No Preview Available"
msgstr ""

#: cornerstone/includes/i18n/app.php:1039
msgid "Click {{icon}} to edit."
msgstr ""

#: cornerstone/includes/i18n/app.php:1041
msgid "Choose a valid Component"
msgstr ""

#: cornerstone/includes/i18n/app.php:1042
msgid "Missing Name"
msgstr ""

#: cornerstone/includes/i18n/app.php:1043
msgid "Component could not be resolved"
msgstr ""

#: cornerstone/includes/i18n/app.php:1045
msgid "Parameters can not be added to pass through components."
msgstr ""

#: cornerstone/includes/i18n/app.php:1046
msgid "Parameters can not be modified on locked elements."
msgstr ""

#: cornerstone/includes/i18n/app.php:1047
msgid ""
"Virtual component elements like Slots and Pass Through\n"
" components can not be locked"
msgstr ""

#: cornerstone/includes/i18n/app.php:1051
msgid ""
"Abstract away complexity by adding custom parameters to your Element and\n"
" simplifying your interface."
msgstr ""

#: cornerstone/includes/i18n/app.php:1052
msgid "Locked Elements are closed off to internal edits and cannot be duplicated or deleted."
msgstr ""

#: cornerstone/includes/i18n/app.php:1058
msgid "Translate"
msgstr ""

#: cornerstone/includes/i18n/app.php:1059
msgid "Translation"
msgstr ""

#: cornerstone/includes/i18n/app.php:1060
msgid "This content has not been translated into the active language."
msgstr ""

#: cornerstone/includes/i18n/app.php:1061
msgid "Start Blank"
msgstr ""

#: cornerstone/includes/i18n/app.php:1062
msgid "or"
msgstr ""

#: cornerstone/includes/i18n/app.php:1063
msgid "This <strong>{{context}}</strong> does not have a <strong>{{lang}}</strong> translation. Start with a blank slate or choose an existing translation to begin with. Please save before you do this."
msgstr ""

#: cornerstone/includes/i18n/app.php:1075
msgid "This Region has no Bars."
msgstr ""

#: cornerstone/includes/i18n/app.php:1076
msgid "This Region only supports a single Bar."
msgstr ""

#: cornerstone/includes/i18n/app.php:1082
msgid "History"
msgstr ""

#: cornerstone/includes/i18n/app.php:1083
msgid "Builder Loaded"
msgstr ""

#: cornerstone/includes/i18n/app.php:1084
msgid "Column Layout Updated"
msgstr ""

#: cornerstone/includes/i18n/app.php:1085
msgid "{{context}} Contents Deleted"
msgstr ""

#: cornerstone/includes/i18n/app.php:1086
msgid "{{context}} Spacing Removed"
msgstr ""

#: cornerstone/includes/i18n/app.php:1087
msgid "{{context}} Style Reset"
msgstr ""

#: cornerstone/includes/i18n/app.php:1088
msgid "{{context}} Renamed"
msgstr ""

#: cornerstone/includes/i18n/app.php:1089
msgid "Row Layout Created"
msgstr ""

#: cornerstone/includes/i18n/app.php:1090
msgid "Grid Layout Created"
msgstr ""

#: cornerstone/includes/i18n/app.php:1095, cornerstone/includes/classes/Services/Conditionals.php:929
msgid "is"
msgstr ""

#: cornerstone/includes/i18n/app.php:1096, cornerstone/includes/classes/Services/Conditionals.php:930
msgid "is not"
msgstr ""

#: cornerstone/includes/i18n/app.php:1097
msgid "is %s"
msgstr ""

#: cornerstone/includes/i18n/app.php:1098
msgid "is not %s"
msgstr ""

#: cornerstone/includes/i18n/app.php:1099
msgid "before"
msgstr ""

#: cornerstone/includes/i18n/app.php:1100
msgid "after"
msgstr ""

#: cornerstone/includes/i18n/app.php:1101
msgid "being viewed"
msgstr ""

#: cornerstone/includes/i18n/app.php:1102
msgid "initial"
msgstr ""

#: cornerstone/includes/i18n/app.php:1103
msgid "empty"
msgstr ""

#: cornerstone/includes/i18n/app.php:1104
msgid "repeated"
msgstr ""

#: cornerstone/includes/i18n/app.php:1105
msgid "set"
msgstr ""

#: cornerstone/includes/i18n/app.php:1106
msgid "true"
msgstr ""

#: cornerstone/includes/i18n/app.php:1107, cornerstone/includes/classes/Services/Conditionals.php:620
msgid "logged in"
msgstr ""

#: cornerstone/includes/i18n/app.php:1108
msgid "Required"
msgstr ""

#: cornerstone/includes/i18n/app.php:1113
msgid "Before: {{date}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:1114
msgid "After: {{date}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:1115
msgid "{{before}} - {{after}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:1119
msgid "Media Library"
msgstr ""

#: cornerstone/includes/i18n/app.php:1125
msgid "Component Export"
msgstr ""

#: cornerstone/includes/i18n/app.php:1126
msgid "Component Slot"
msgstr ""

#: cornerstone/includes/i18n/app.php:1127
msgid "Component Pass Through"
msgstr ""

#: cornerstone/includes/i18n/app.php:1135
msgid "Scroll Effects"
msgstr ""

#: cornerstone/includes/i18n/app.php:1136
msgid "Interaction Effects"
msgstr ""

#: cornerstone/includes/i18n/app.php:1138
msgid "Label: {{label}}"
msgstr ""

#: cornerstone/includes/i18n/app.php:1144
msgid "This element is exported as a Component to be used in other builders and thru shortcode. Right click to copy the text."
msgstr ""

#: cornerstone/includes/i18n/app.php:1145
msgid "This element is part of a Component and provides a location to place child elements."
msgstr ""

#: cornerstone/includes/i18n/app.php:1146
msgid "Provides a new data source (Posts, custom data, etc.) to be used by Looper Consumers."
msgstr ""

#: cornerstone/includes/i18n/app.php:1147
msgid "Iterates over one or more items of a Looper Provider data source, making the item available via Dynamic Content."
msgstr ""

#: cornerstone/includes/i18n/app.php:1148
msgid "Unless the given rules apply, this element is not output and its HTML is omitted from the DOM."
msgstr ""

#: cornerstone/includes/i18n/app.php:1149
msgid "The root HTML tag of this element includes additional attributes."
msgstr ""

#: cornerstone/includes/i18n/app.php:1150
msgid "Additional CSS scoped to the element's root HTML tag is being applied."
msgstr ""

#: cornerstone/includes/i18n/app.php:1151
msgid "On selected screen sizes, visually hide this element."
msgstr ""

#: cornerstone/includes/i18n/app.php:1152
msgid "The content of this element is being retrieved from a data source."
msgstr ""

#: cornerstone/includes/i18n/app.php:1153
msgid "This element modifies its presentation based on being present in the viewport or not."
msgstr ""

#: cornerstone/includes/i18n/app.php:1154
msgid "Interacting (hover, focus, etc.) with this element will trigger."
msgstr ""

#: cornerstone/includes/i18n/app.php:1155
msgid "Interacting with this element, will cause Interaction Effects on descendants to be triggered."
msgstr ""

#: cornerstone/includes/i18n/app.php:1156
msgid "This element has custom parameters created for it."
msgstr ""

#: cornerstone/includes/i18n/app.php:1160
msgid "For slower page loads, Elements will remember the CSS generated when last saved. This is automatically cleared when Cornerstone is updated. It may be useful to clear the cache manually if any Elements are missing styling."
msgstr ""

#: cornerstone/includes/i18n/app.php:1161
msgid "Cache"
msgstr ""

#: cornerstone/includes/i18n/app.php:1162
msgid "Clear Cache"
msgstr ""

#: cornerstone/includes/i18n/app.php:1163
msgid "Cache Cleared"
msgstr ""

#: cornerstone/includes/i18n/app.php:1165
msgid "Activate"
msgstr ""

#: cornerstone/includes/i18n/app.php:1166
msgid "License Successfully Validate"
msgstr ""

#: cornerstone/includes/i18n/app.php:1167
msgid "Revoke"
msgstr ""

#: cornerstone/includes/i18n/app.php:1168
msgid "License Revoked"
msgstr ""

#: cornerstone/includes/i18n/common.php:7
msgid "Untitled %s"
msgstr ""

#: cornerstone/includes/i18n/common.php:8
msgid "%s (%s)"
msgstr ""

#: cornerstone/includes/i18n/common.php:9
msgid "Classic %s"
msgstr ""

#: cornerstone/includes/i18n/common.php:10
msgid "Blank"
msgstr ""

#: cornerstone/includes/i18n/common.php:11
msgid "Edit %s"
msgstr ""

#: cornerstone/includes/i18n/common.php:12
msgid "Edit %s (%s)"
msgstr ""

#: cornerstone/includes/i18n/common.php:14
msgid "{{index}} {{label}}"
msgstr ""

#: cornerstone/includes/i18n/common.php:16
msgid "{{prefix}}: {{content}}"
msgstr ""

#: cornerstone/includes/i18n/common.php:20
msgid "Header Builder"
msgstr ""

#: cornerstone/includes/i18n/common.php:21
msgid "Page Builder"
msgstr ""

#: cornerstone/includes/i18n/common.php:22
msgid "Footer Builder"
msgstr ""

#: cornerstone/includes/i18n/common.php:23
msgid "Layout Builder"
msgstr ""

#: cornerstone/includes/i18n/common.php:25
msgid "Component Builder"
msgstr ""

#: cornerstone/includes/i18n/common.php:35
msgctxt "WordPress toolbar edit link"
msgid "Edit %s"
msgstr ""

#: cornerstone/includes/i18n/common.php:38
msgid "Cornerstone Document"
msgstr ""

#: cornerstone/includes/i18n/common.php:39
msgid "Cornerstone Documents"
msgstr ""

#: cornerstone/includes/i18n/common.php:45, cornerstone/includes/classes/Services/Components.php:43, cornerstone/includes/classes/Services/Components.php:74, cornerstone/includes/elements/definitions/component-direct.php:186, cornerstone/includes/elements/definitions/component-direct.php:218
msgid "Component"
msgstr ""

#: cornerstone/includes/i18n/common.php:46
msgid "Components"
msgstr ""

#: cornerstone/includes/i18n/common.php:51
msgid "Single Layout"
msgstr ""

#: cornerstone/includes/i18n/common.php:52
msgid "Single Layouts"
msgstr ""

#: cornerstone/includes/i18n/common.php:53
msgid "Archive Layout"
msgstr ""

#: cornerstone/includes/i18n/common.php:54
msgid "Archive Layouts"
msgstr ""

#: cornerstone/includes/i18n/common.php:55
msgid "Layout (CS Legacy)"
msgstr ""

#: cornerstone/includes/i18n/common.php:56
msgid "Layouts (CS Legacy)"
msgstr ""

#: cornerstone/includes/i18n/common.php:58
msgid "{{context}} Layout"
msgstr ""

#: cornerstone/includes/i18n/common.php:59
msgid "{{context}} Layouts"
msgstr ""

#: cornerstone/includes/i18n/common.php:61
msgid "Slot"
msgstr ""

#: cornerstone/includes/i18n/common.php:62
msgid "Slots"
msgstr ""

#: cornerstone/includes/i18n/common.php:64
msgid "This element could not render because its definition is missing. You might need to activate a plugin."
msgstr ""

#: cornerstone/includes/i18n/common.php:65
msgid ""
"The id ({{component_id}}) for this Component could not be resolved.\n"
"Element ID ({{id}})"
msgstr ""

#: cornerstone/includes/i18n/common.php:66
msgid "No Preview Available (<a target=\"_blank\" href=\"{{url}}\">View Live</a>)"
msgstr ""

#: cornerstone/includes/i18n/common.php:67
msgid "This element could not render due to invalid markup. You may have unclosed HTML tags in your content."
msgstr ""

#: cornerstone/includes/i18n/common.php:68
msgid "This element failed to render. Additional error information may be found in your browsers developer tools."
msgstr ""

#: cornerstone/includes/i18n/common.php:70
msgid "Create a Menu"
msgstr ""

#: cornerstone/includes/i18n/common.php:72
msgctxt "Translate to: (greek, cyrillic, vietnamese) to add an additional font subset."
msgid "cs-no-subset"
msgstr ""

#: cornerstone/includes/i18n/common.php:74
msgid "Insert Image"
msgstr ""

#: cornerstone/includes/i18n/common.php:78
msgid "Choose Layout"
msgstr ""

#: cornerstone/includes/i18n/common.php:82
msgid "Region"
msgstr ""

#: cornerstone/includes/i18n/common.php:87
msgid "Multiple Assignments"
msgstr ""

#: cornerstone/includes/integration/codemirror.php:45
msgid "Code Editor"
msgstr ""

#: cornerstone/includes/integration/codemirror.php:62
msgid "Line Wrap"
msgstr ""

#: cornerstone/includes/integration/codemirror.php:63
msgid "Instead of going off screen to a scroll bar, wrap the text inside the code editor frame"
msgstr ""

#: cornerstone/includes/integration/codemirror.php:70
msgid "Key Map"
msgstr ""

#: cornerstone/includes/integration/csv.php:24, cornerstone/includes/integration/Api/ApiCSV.php:8
msgid "CSV"
msgstr ""

#: cornerstone/includes/integration/html-storage.php:189
msgid "Manage how content is stored on your website. <a href=\"https://theme.co/docs/content-storage\" target=\"_blank\" rel=\"noopener noreferrer\">Learn more</a>."
msgstr ""

#: cornerstone/includes/integration/theme-color-header.php:16
msgid "Theme Color"
msgstr ""

#: cornerstone/includes/integration/theme-color-header.php:17
msgid "This will add a meta tag that will change the browser header color. This is used on mobile platforms and certain browsers. HTTPS is required."
msgstr ""

#: cornerstone/includes/integration/tinymce.php:24
msgid "Use Global Colors"
msgstr ""

#: cornerstone/includes/integration/tinymce.php:25
msgid "Uses your Theme global colors in the text color picker. This plugin can only use Hex colors and will convert your colors to that if it can. Usage of this does not change if you change your colors"
msgstr ""

#: cornerstone/includes/integration/tinymce.php:32
msgid "Preserve and add P tags for new lines. Turning this off is useful if you use HTML mode and dont want the text editor to add P tags"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:190, cornerstone/includes/integration/woocommerce.php:201
msgid "WooCommerce Single"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:191, cornerstone/includes/integration/woocommerce.php:202
msgid "WooCommerce Archive"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:224
msgid "Product (is)"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:229
msgid "Downloadable"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:231
msgid "In Stock"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:232
msgid "On Backorder"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:233
msgid "On Sale"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:234
msgid "Purchasable"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:235
msgid "Shipping Taxable"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:236
msgid "Sold Individually"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:237
msgid "Taxable"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:238
msgid "Virtual"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:240
msgid "Variable"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:245
msgid "Product (has)"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:249, cornerstone/includes/classes/Services/Conditionals.php:725, cornerstone/includes/classes/Services/Conditionals.php:741, cornerstone/includes/classes/Services/Conditionals.php:806, cornerstone/includes/classes/Services/Conditionals.php:819
msgid "has"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:250, cornerstone/includes/classes/Services/Conditionals.php:726, cornerstone/includes/classes/Services/Conditionals.php:742, cornerstone/includes/classes/Services/Conditionals.php:807, cornerstone/includes/classes/Services/Conditionals.php:820
msgid "has not"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:257
msgid "Gallery"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:258
msgid "Reviews"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:259
msgid "Attributes"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:260
msgid "Child"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:261
msgid "Default Attributes"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:339, cornerstone/includes/classes/Services/Conditionals.php:317, cornerstone/includes/classes/Services/Conditionals.php:710
msgid "%s (Specific)"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:358, cornerstone/includes/integration/woocommerce.php:498, cornerstone/includes/integration/woocommerce.php:579, cornerstone/includes/classes/Services/Conditionals.php:221, cornerstone/includes/classes/Services/Conditionals.php:336, cornerstone/includes/classes/Services/Conditionals.php:533
msgctxt "[Post Type] [Post Taxonomy]"
msgid "%s %s"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:371, cornerstone/includes/classes/Services/Conditionals.php:349
msgid "%s Parent"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:381, cornerstone/includes/classes/Services/Conditionals.php:359
msgid "%s Ancestor"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:392, cornerstone/includes/classes/Services/Conditionals.php:370
msgid "%s Template"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:405, cornerstone/includes/classes/Services/Conditionals.php:383
msgid "%s Format"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:416, cornerstone/includes/classes/Services/Conditionals.php:394
msgid "%s Publish Date"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:426, cornerstone/includes/classes/Services/Conditionals.php:404
msgid "%s Status"
msgstr ""

#: cornerstone/includes/integration/woocommerce.php:508, cornerstone/includes/classes/Services/Conditionals.php:544
msgctxt "cornerstone"
msgid "%s Taxonomy"
msgstr ""

#: cornerstone/includes/integration/yoast.php:171
msgid "Yoast"
msgstr ""

#: cornerstone/includes/integration/yoast.php:177
msgid "Ignore in Yoast"
msgstr ""

#: cornerstone/includes/loopers/breadcrumbs.php:43, cornerstone/includes/loopers/breadcrumbs.php:49
msgid "Breadcrumbs (Elements)"
msgstr ""

#: cornerstone/includes/loopers/range.php:10
msgid "Range"
msgstr ""

#: cornerstone/includes/loopers/range.php:25
msgid "The start of the range. Can be a letter like a or A as well"
msgstr ""

#: cornerstone/includes/loopers/range.php:33
msgid "The end of the range. Can be a letter like z or Z as well"
msgstr ""

#: cornerstone/includes/loopers/range.php:40
msgid "Steps"
msgstr ""

#: cornerstone/includes/loopers/range.php:41
msgid "The amount to increment the number by when going from min to max. If you are using letters you will not be able to use decimals"
msgstr ""

#: cornerstone/includes/shortcodes/accordion.php:54, cornerstone/includes/shortcodes/tabs.php:61
msgid "Make Sure to Set a Title"
msgstr ""

#: cornerstone/includes/shortcodes/audio-player.php:82
msgid "Audio source missing"
msgstr ""

#: cornerstone/includes/shortcodes/author.php:18, cornerstone/includes/elements/classic/_alternate/author.php:26
msgid "About the Author"
msgstr ""

#: cornerstone/includes/shortcodes/author.php:26
msgid "Visit the Facebook Profile for %s"
msgstr ""

#: cornerstone/includes/shortcodes/author.php:27
msgid "Visit the Twitter Profile for %s"
msgstr ""

#: cornerstone/includes/shortcodes/callout.php:36, cornerstone/includes/shortcodes/callout.php:38, cornerstone/includes/shortcodes/prompt.php:26, cornerstone/includes/shortcodes/prompt.php:28, cornerstone/includes/elements/classic/_alternate/callout.php:47
msgid "Enter Your Text"
msgstr ""

#: cornerstone/includes/shortcodes/callout.php:37, cornerstone/includes/shortcodes/prompt.php:27
msgid "Don&apos;t forget to enter in your text."
msgstr ""

#: cornerstone/includes/shortcodes/card.php:43, cornerstone/includes/elements/classic/_alternate/card.php:64, cornerstone/includes/elements/classic/_alternate/card.php:66
msgid "Front Title"
msgstr ""

#: cornerstone/includes/shortcodes/card.php:44
msgid "This is where the text for the front of your card should go. It&apos;s best to keep it short and sweet."
msgstr ""

#: cornerstone/includes/shortcodes/card.php:46, cornerstone/includes/elements/classic/_alternate/card.php:227
msgid "Back Title"
msgstr ""

#: cornerstone/includes/shortcodes/card.php:47
msgid "This is where the text for the back of your card should go."
msgstr ""

#: cornerstone/includes/shortcodes/card.php:48, cornerstone/includes/elements/classic/_alternate/button.php:26, cornerstone/includes/elements/classic/_alternate/card.php:306, cornerstone/includes/elements/classic/_alternate/prompt.php:47
msgid "Click Me!"
msgstr ""

#: cornerstone/includes/shortcodes/creative-cta.php:31
msgid "Place Your<br>Text Here"
msgstr ""

#: cornerstone/includes/shortcodes/protect.php:23, cornerstone/includes/elements/classic/_alternate/protect.php:30
msgid "Restricted Content Login"
msgstr ""

#: cornerstone/includes/shortcodes/protect.php:27, cornerstone/includes/views/partials/login.php:65, cornerstone/includes/_classes/dynamic-content/class-dynamic-content-user.php:33
msgid "Username"
msgstr ""

#: cornerstone/includes/shortcodes/protect.php:28, cornerstone/includes/views/partials/login.php:69
msgid "Password"
msgstr ""

#: cornerstone/includes/shortcodes/protect.php:29, cornerstone/includes/views/partials/login.php:17
msgid "Login"
msgstr ""

#: cornerstone/includes/shortcodes/recent-posts.php:67
msgid "Permalink to: \"%s\""
msgstr ""

#: cornerstone/includes/shortcodes/share.php:58, cornerstone/includes/elements/classic/_alternate/social-sharing.php:24
msgid "Share this Post"
msgstr ""

#: cornerstone/includes/shortcodes/share.php:60
msgid "Share on X"
msgstr ""

#: cornerstone/includes/shortcodes/toc.php:33, cornerstone/includes/elements/classic/_alternate/toc.php:8
msgid "Table of Contents"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:106
msgid "Load Type"
msgstr ""

#: cornerstone/includes/theme-options/fontawesome.php:125
msgid "Webfont"
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:273
msgid "Initializing..."
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:334
msgid "Revolution Slider downloaded..."
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:352, cornerstone/includes/_classes/class-controller-design-cloud.php:385
msgid "Downloading images..."
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:419
msgid "Preparing taxonomies..."
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:471
msgid "Attaching thumbnails..."
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:545
msgid "Creating posts..."
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:626
msgid "Adding post data..."
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:675
msgid "Creating menus..."
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:831
msgid "Setting Theme Options..."
msgstr ""

#: cornerstone/includes/_classes/class-controller-design-cloud.php:872
msgid "Assigning menus..."
msgstr ""

#: cornerstone/includes/classes/Documents/Component.php:319, cornerstone/includes/classes/Documents/Content.php:583, cornerstone/includes/classes/Documents/GlobalBlock.php:115, cornerstone/includes/classes/Documents/Layout.php:151
msgid "General"
msgstr ""

#: cornerstone/includes/classes/Documents/Component.php:333
msgid "Element Library Exports"
msgstr ""

#: cornerstone/includes/classes/Documents/Content.php:534
msgid "Allow Comments"
msgstr ""

#: cornerstone/includes/classes/Documents/Content.php:542
msgid "Manual Excerpt"
msgstr ""

#: cornerstone/includes/classes/Documents/Content.php:544
msgid "(Optional) An excerpt will be derived from any paragraphs in your content. You can override this by crafting your own excerpt here, or in the WordPress post editor."
msgstr ""

#: cornerstone/includes/classes/Documents/Content.php:553
msgid "Parent %s"
msgstr ""

#: cornerstone/includes/classes/Documents/Content.php:590
msgid "Edit in Wordpress"
msgstr ""

#: cornerstone/includes/classes/Documents/Content.php:600
msgid "Layout Assignments"
msgstr ""

#: cornerstone/includes/classes/Documents/Content.php:605, cornerstone/includes/classes/Services/Conditionals.php:16, cornerstone/includes/classes/Services/Conditionals.php:68
msgid "Single"
msgstr ""

#: cornerstone/includes/classes/Documents/Document.php:191
msgid "Failed to trash"
msgstr ""

#: cornerstone/includes/classes/Documents/Header.php:33
msgid "Multi Region"
msgstr ""

#: cornerstone/includes/classes/Documents/Header.php:34
msgid "Allows for more advanced layout options, such as left, right, and bottom Bar positioning. After selecting, go back to the \"Outline\" tab to see these new regions."
msgstr ""

#: cornerstone/includes/classes/Documents/Layout.php:163
msgid "Assignment"
msgstr ""

#: cornerstone/includes/classes/Documents/Layout.php:164
msgid "Set the conditions for when this Layout will be displayed. If there are multiple matches the one with the lowest priority will be used."
msgstr ""

#: cornerstone/includes/classes/Documents/Layout.php:174
msgid "Priority"
msgstr ""

#: cornerstone/includes/classes/Services/Breadcrumbs.php:36, cornerstone/includes/classes/Services/Conditionals.php:237, cornerstone/includes/classes/Services/Conditionals.php:577
msgid "Search Results"
msgstr ""

#: cornerstone/includes/classes/Services/Breadcrumbs.php:37
msgid "404 (Page Not Found)"
msgstr ""

#: cornerstone/includes/classes/Services/Breadcrumbs.php:38
msgid "Archives"
msgstr ""

#: cornerstone/includes/classes/Services/Breadcrumbs.php:92
msgid "You Are Here"
msgstr ""

#: cornerstone/includes/classes/Services/Breadcrumbs.php:185
msgid "Posts by "
msgstr ""

#: cornerstone/includes/classes/Services/Components.php:69
msgid "This Component does not have any custom parameters mapped to it."
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:38, cornerstone/includes/classes/Services/Conditionals.php:71
msgid "Expression"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:39
msgid "Current Post"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:40
msgid "Global Query"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:131, cornerstone/includes/classes/Services/Conditionals.php:290, cornerstone/includes/classes/Services/Conditionals.php:454, cornerstone/includes/classes/Services/Conditionals.php:887
msgid "Front Page"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:153, cornerstone/includes/classes/Services/Conditionals.php:424
msgid "404 Page"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:180, cornerstone/includes/classes/Services/Conditionals.php:168
msgid "Blog Page"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:262, cornerstone/includes/classes/Services/Conditionals.php:588
msgid "Archive Term"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:276
msgid "Entire Site"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:286
msgid "All Singular"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:416
msgid "Single Term"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:465
msgid "All Archives"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:476, cornerstone/includes/classes/Services/Conditionals.php:898
msgid "First Page"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:487, cornerstone/includes/classes/Services/Conditionals.php:909
msgid "Date Archive"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:493, cornerstone/includes/classes/Services/Conditionals.php:915
msgid "Year"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:494, cornerstone/includes/classes/Services/Conditionals.php:916, cornerstone/includes/elements/control-partials/seconds-select.php:26
msgid "Month"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:496, cornerstone/includes/classes/Services/Conditionals.php:918
msgid "Time"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:502
msgid "Has Posts"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:558
msgid "Any Author"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:569
msgid "Specific Author"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:602
msgid "Today"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:610
msgid "Current User (logged in)"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:625
msgid "Current User Role"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:650
msgid "Post Format"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:660
msgid "Post Status"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:693, cornerstone/includes/_classes/dynamic-content/class-dynamic-content-post.php:120
msgid "Is Sticky"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:721
msgid "Post (has taxonomy)"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:737
msgid "Post (has term)"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:753
msgid "Parent"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:763
msgid "Ancestor"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:789
msgid "Comment Count"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:793
msgid "Not Empty"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:802
msgid "Next Post"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:815
msgid "Previous Post"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:828
msgid "Publish Date"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:854
msgid "404"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:931
msgid "in"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:932
msgid "not in"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:947
msgid "Day Name Equal To"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:948
msgid "Day Number Equal To"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:949
msgid "Date Equal To"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:950
msgid "Month Equal To"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:951
msgid "Year Equal To"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:977
msgid "Datetime"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:999
msgid "First"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:1000
msgid "Last"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:1001
msgid "Odd"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:1002
msgid "Even"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:1007
msgid "Provider Output"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:1018
msgid "Consumer Output"
msgstr ""

#: cornerstone/includes/classes/Services/Conditionals.php:1071
msgid "Assigned"
msgstr ""

#: cornerstone/includes/classes/Services/ElementLibrary.php:21
msgid "Deprecated"
msgstr ""

#: cornerstone/includes/classes/Services/ElementLibrary.php:25
msgid "Structure"
msgstr ""

#: cornerstone/includes/classes/Services/ElementLibrary.php:28
msgid "Interactive"
msgstr ""

#: cornerstone/includes/classes/Services/ElementLibrary.php:30
msgid "Navigation"
msgstr ""

#: cornerstone/includes/classes/Services/ElementLibrary.php:32
msgid "Dynamic"
msgstr ""

#: cornerstone/includes/classes/Services/ElementLibrary.php:36
msgid "Classic"
msgstr ""

#: cornerstone/includes/classes/Services/FontAwesome.php:108
msgid "Sharp Light"
msgstr ""

#: cornerstone/includes/classes/Services/FontAwesome.php:109
msgid "Sharp Regular"
msgstr ""

#: cornerstone/includes/classes/Services/FontAwesome.php:110
msgid "Sharp Solid"
msgstr ""

#: cornerstone/includes/classes/Services/Settings.php:41
msgid "Custom Path"
msgstr ""

#: cornerstone/includes/classes/Services/Settings.php:42
msgid "Change the path used to load the main interface."
msgstr ""

#: cornerstone/includes/classes/Services/ThemeManagement.php:130
msgid "Cornerstone Themeless mode is enabled."
msgstr ""

#: cornerstone/includes/classes/Services/Validation.php:152
msgid "No purchase code specified"
msgstr ""

#: cornerstone/includes/classes/Templating/Export.php:436
msgid "Export not supported please install php-zip."
msgstr ""

#: cornerstone/includes/classes/Templating/Export.php:443
msgid "Unable to open export file (archive) for writing."
msgstr ""

#: cornerstone/includes/elements/control-partials/anchor.php:474, cornerstone/includes/elements/classic/_alternate/text-type.php:57
msgid "Tag"
msgstr ""

#: cornerstone/includes/elements/control-partials/anchor.php:493
msgid "Button Type"
msgstr ""

#: cornerstone/includes/elements/control-partials/aspect-ratio.php:10
msgid "Aspect Ratio for the size of this element to follow"
msgstr ""

#: cornerstone/includes/elements/control-partials/bg-video.php:50
msgid "Pause out of View"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:124
msgid "Top Center"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:142
msgid "Bottom Center"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:166
msgid "The amount of time in miliseconds to wait till the next dropdown is opened"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:176, cornerstone/includes/integration/Api/ApiControls.php:94
msgid "Timeout"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:177
msgid "The amount of time in miliseconds to wait till closing itself"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:187
msgid "Sensitivity"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:188
msgid "Higher sensitivity means more mouse movements will trigger the opening"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:201
msgid "Display the content of the dropdown inline with the button, helps to prevent flickering if your dropdown is in a moving area like a sticky bar"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:207
msgid "Inline Fixed"
msgstr ""

#: cornerstone/includes/elements/control-partials/dropdown.php:209
msgid "Use position 'fixed' to position the dropdown. Can remove issues with percentage based widths"
msgstr ""

#: cornerstone/includes/elements/control-partials/dynamic-rendering.php:14
msgid "Will reset the state of video and other supported elements inside the element"
msgstr ""

#: cornerstone/includes/elements/control-partials/effects.php:816
msgid ""
"Provide a comma-separated list of CSS images:\n"
"\n"
"e.g.\n"
"\n"
"url(...),\n"
"linear-gradient(...)"
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-consumer.php:40
msgid "Rewind"
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-consumer.php:41
msgid "After this consumer is finished looping, rewind the data for the NEXT consumer to use from the start. Not always needed depending on the provider type"
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:83
msgid "By Page"
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:184
msgid "Leaving empty will use the WordPress default which is usually just publish"
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:323
msgid "Where to start the user query loop. 'By Page' calculates the offset based on the count and the value of the ?paged query var"
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:332
msgid "Query vars through JSON. Certain plugins check custom query vars"
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:577
msgid "Loop Keys"
msgstr ""

#: cornerstone/includes/elements/control-partials/looper-provider.php:578
msgid "If true, this will loop over each key using the key as the index"
msgstr ""

#: cornerstone/includes/elements/control-partials/modal.php:111
msgid "Ignore Side Padding"
msgstr ""

#: cornerstone/includes/elements/control-partials/modal.php:112
msgid "Ignore padding that would inset the modal between the close button"
msgstr ""

#: cornerstone/includes/elements/control-partials/pagination.php:140
msgid "Numbers will be hidden on the selected screen size and smaller"
msgstr ""

#: cornerstone/includes/elements/control-partials/rating.php:259
msgid "Author Type"
msgstr ""

#: cornerstone/includes/elements/control-partials/rating.php:265
msgid "Person"
msgstr ""

#: cornerstone/includes/elements/control-partials/rating.php:270
msgid "Organization"
msgstr ""

#: cornerstone/includes/elements/control-partials/search.php:106
msgid "Display Last Query"
msgstr ""

#: cornerstone/includes/elements/control-partials/search.php:113
msgid "Autofocus"
msgstr ""

#: cornerstone/includes/elements/control-partials/search.php:114
msgid "When opening a modal or toggleable, autofocus this input"
msgstr ""

#: cornerstone/includes/elements/control-partials/seconds-select.php:14, cornerstone/includes/elements/control-partials/seconds-select.php:122
msgid "Seconds"
msgstr ""

#: cornerstone/includes/elements/control-partials/seconds-select.php:17
msgid "Minutes"
msgstr ""

#: cornerstone/includes/elements/control-partials/seconds-select.php:20
msgid "Hours"
msgstr ""

#: cornerstone/includes/elements/control-partials/seconds-select.php:24
msgid "Week"
msgstr ""

#: cornerstone/includes/elements/control-partials/seconds-select.php:31
msgid "Never"
msgstr ""

#: cornerstone/includes/elements/control-partials/toggleable.php:17
msgid "Esc Key Close"
msgstr ""

#: cornerstone/includes/elements/control-partials/toggleable.php:18
msgid "When the toggleable is open, pressing the esc key will close the last opened toggleable"
msgstr ""

#: cornerstone/includes/elements/control-partials/toggleable.php:24
msgid "Direct Close"
msgstr ""

#: cornerstone/includes/elements/control-partials/toggleable.php:25
msgid "Clicking or touching outside of the content area will close the toggleable automatically"
msgstr ""

#: cornerstone/includes/elements/definitions/accordion-item-elements.php:13
msgid "Accordion Item Elements"
msgstr ""

#: cornerstone/includes/elements/definitions/accordion-item-elements.php:214
msgid "Accordion Item (Elements)"
msgstr ""

#: cornerstone/includes/elements/definitions/accordion-item-elements.php:233
msgid "This is accordion content!"
msgstr ""

#: cornerstone/includes/elements/definitions/accordion-item.php:212
msgid "Accordion Item (HTML Content)"
msgstr ""

#: cornerstone/includes/elements/definitions/accordion.php:327
msgid "Scroll With"
msgstr ""

#: cornerstone/includes/elements/definitions/accordion.php:328
msgid "When this accordion opens, it will control the scroll of the page to make this the top of the screen"
msgstr ""

#: cornerstone/includes/elements/definitions/accordion.php:359
msgid "FAQ Schema"
msgstr ""

#: cornerstone/includes/elements/definitions/accordion.php:360
msgid "Generate an FAQPage schema based on the accordion header titles and content"
msgstr ""

#: cornerstone/includes/elements/definitions/breadcrumbs.php:133
msgid "Breadcrumb Navigation"
msgstr ""

#: cornerstone/includes/elements/definitions/counter.php:240
msgid "Comma Seperated Decimal"
msgstr ""

#: cornerstone/includes/elements/definitions/counter.php:241
msgid "Will count in 200.000,00 style and parse your numbers the same way"
msgstr ""

#: cornerstone/includes/elements/definitions/deprecated-content-area-off-canvas.php:59, cornerstone/includes/elements/definitions/deprecated-tp-wc-cart-off-canvas.php:75, cornerstone/includes/elements/definitions/layout-off-canvas.php:60, cornerstone/includes/elements/definitions/nav-collapsed.php:105, cornerstone/includes/elements/definitions/nav-layered.php:110
msgid "Toggle Off Canvas Content"
msgstr ""

#: cornerstone/includes/elements/definitions/form-input.php:210
msgid "Form Input"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:104
msgid "%s not active"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:105
msgid "Select a form (%s)"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:113, cornerstone/includes/elements/definitions/form-integration.php:227, cornerstone/includes/elements/definitions/form-integration.php:275
msgid "WPForms"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:137, cornerstone/includes/elements/definitions/form-integration.php:228, cornerstone/includes/elements/definitions/form-integration.php:276, cornerstone/includes/elements/classic/_alternate/contact-form-7.php:8
msgid "Contact Form 7"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:171, cornerstone/includes/elements/definitions/form-integration.php:229, cornerstone/includes/elements/definitions/form-integration.php:277
msgid "Gravity Forms"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:224
msgid "Activate Plugin"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:225
msgid "%s must be installed and activated to use this form type."
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:444
msgid "Ajax"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:454
msgid "Tab Index"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:456
msgid "Starting tab index"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:462
msgid "Field Values"
msgstr ""

#: cornerstone/includes/elements/definitions/form-integration.php:464
msgid "Prefill field values"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-div.php:452
msgid "Absolute Zero"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-row.php:345
msgid "The direction the columns should be displayed in. When using parameters the values need to be `row` or `row-reverse`. There is also a parameter control type called `row-direction`"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:368
msgid "Starting Slide"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:369
msgid "On load which slide should be displayed first. For inline, moving to page 2 would mean your starting slide would be a slide on page 2"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:379
msgid "Pause on Hover"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:380
msgid "When hovering over the slide container, pause the autoplay ability till the user is no longer hovering over the slider"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:495
msgid "Forward"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:794
msgid "Inline, 1 Slide Per Page"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:808
msgid "Inline, 2 Slides Per Page"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:835
msgid "Inline, 3 Slides Per Page"
msgstr ""

#: cornerstone/includes/elements/definitions/layout-slide-container.php:862
msgid "Inline, 4 Slides Per Page"
msgstr ""

#: cornerstone/includes/elements/definitions/lottie.php:231
msgid "Renderer"
msgstr ""

#: cornerstone/includes/elements/definitions/lottie.php:232
msgid "SVG will scale the best, Canvas will have the best performance and for complex animations, and HTML for very simple animations and shapes."
msgstr ""

#: cornerstone/includes/elements/definitions/lottie.php:237
msgid "Canvas"
msgstr ""

#: cornerstone/includes/elements/definitions/lottie.php:302
msgid "-1 is infinite except in scroll mode where it is also a 0. All other numbers are the number of times to loop"
msgstr ""

#: cornerstone/includes/elements/definitions/lottie.php:315
msgid "These percentages are based on the screen height"
msgstr ""

#: cornerstone/includes/elements/definitions/lottie.php:508
msgid "Lottie"
msgstr ""

#: cornerstone/includes/elements/definitions/map.php:271
msgid "Map ID"
msgstr ""

#: cornerstone/includes/elements/definitions/map.php:272
msgid "This is controlled in the Google Map Console under maps. Using a custom Map ID will let you customize the Map Styles in the Google Console"
msgstr ""

#: cornerstone/includes/elements/definitions/quote.php:24, cornerstone/includes/elements/definitions/testimonial.php:24
msgid "You are never too old to set another goal or to dream a new dream."
msgstr ""

#: cornerstone/includes/elements/definitions/quote.php:25, cornerstone/includes/elements/definitions/testimonial.php:25
msgid "C.S. Lewis"
msgstr ""

#: cornerstone/includes/elements/definitions/raw-content.php:92
msgid "This will render blocks when this element is rendered, as opposed to after all rendering has occurred"
msgstr ""

#: cornerstone/includes/elements/definitions/raw-content.php:93
msgid "Render Blocks"
msgstr ""

#: cornerstone/includes/elements/definitions/tab-elements.php:117
msgid "Tab (Elements)"
msgstr ""

#: cornerstone/includes/elements/definitions/tab-elements.php:136
msgid "This is tab content!"
msgstr ""

#: cornerstone/includes/elements/definitions/tab.php:105
msgid "Tab (HTML Content)"
msgstr ""

#: cornerstone/includes/elements/definitions/tp-bbp-dropdown.php:81
msgid "Forums Search"
msgstr ""

#: cornerstone/includes/elements/definitions/tp-bbp-dropdown.php:85
msgid "Subscriptions"
msgstr ""

#: cornerstone/includes/elements/definitions/tp-bbp-dropdown.php:92, cornerstone/includes/elements/definitions/tp-bp-dropdown.php:119
msgid "Profile"
msgstr ""

#: cornerstone/includes/elements/definitions/tp-bbp-dropdown.php:90, cornerstone/includes/elements/definitions/tp-bp-dropdown.php:117
msgid "Log in"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:192
msgid "\"Dense\" packing algorithm attempts to fill in holes earlier in the grid, if smaller items come up later. This may cause items to appear out-of-order, when doing so would fill in holes left by larger items."
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:410
msgid "2 Columns"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:454
msgid "3 Columns"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:508
msgid "4 Columns"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:572
msgid "Domino (2fr 1fr)"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:647
msgid "Domino (1fr 2fr)"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:722
msgid "Domino (2fr 1fr 1fr)"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:807
msgid "Domino (1fr 1fr 2fr)"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:892
msgid "Cell Overlap (Standard)"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:966
msgid "Cell Overlap (Reversed)"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:1040
msgid "Minmax (Fill Space)"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:1085
msgid "Minmax (Max Width)"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:1131
msgid "Offset Masonry"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:1506
msgid "Pyramid"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:1776
msgid "Board Game"
msgstr ""

#: cornerstone/includes/elements/definitions-pro/layout-grid.php:2334
msgid "Mosaic"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:43
msgid "Graphic Display"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:48
msgid "Keep Enabled for this Menu Item"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:49
msgid "Disable for this Menu Item"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:54
msgid "Icon Primary"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:61
msgid "Icon Secondary"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:68
msgid "Image Primary"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:75
msgid "Image Secondary"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:82
msgid "Image Primary Alt Text"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:89
msgid "Image Secondary Alt Text"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:96
msgid "Image Width (Required)"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:99
msgid "Input the unitless pixel width. E.G. If your image is 300px wide, write &ldquo;300&rdquo; in the input."
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:103
msgid "Image Height (Required)"
msgstr ""

#: cornerstone/includes/extend/menu-item-custom-fields/menu-item-custom-fields-map.php:106
msgid "Input the unitless pixel height. E.G. If your image is 150px tall, write &ldquo;150&rdquo; in the input."
msgstr ""

#: cornerstone/includes/integration/Api/AdminNotices.php:12
msgid "External REST API XML parser has added a breaking change and you are running legacy mode. If you are not using any XML in your External API, go into the Theme Options and disable \"XML Legacy Mode\". Please see the guide here on how to migrate your usage of XML <a href=\"https://theme.co/docs/external-api-xml-change\" target=\"_blank\" rel=\"noreferrer noopener\">here</a>"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:17
msgid "If you are not ready to run the endpoint uncheck this. This will help to not incur API charges, or do something by accident"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:25
msgid "Endpoint"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:26
msgid "URL including the protocol. Can include a path as well"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:38
msgid "Method"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:44, cornerstone/includes/integration/Api/ApiExtension.php:222
msgid "Default (Global)"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:79
msgid "Cache Time"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:80
msgid "Time in seconds to store results of API result. Leave empty to never cache"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:86
msgid "Follow Redirect"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:87
msgid "If the API sends you to another URL, follow that and get content from the redirect"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:95
msgid "Time in seconds to wait before giving up on any given request"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:102
msgid "HTTP Timeout"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:103
msgid "Time in seconds to wait before giving up on receiving the headers of an HTTP endpoint"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:115
msgid "Debug"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:116
msgid "When in debug, will send all response status info in the 'info' key and the actual response in the 'response' key. This requires setting up another Dynamic Content Looper after this one"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:156
msgid "API Global"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:157
msgid "Controls will merge or overwrite with your Global in Theme Options depending on the value type"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:160
msgid "Select a Global"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:172
msgid "Added directly to the endpoint. Not required if using full path in the endpoint"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:180
msgid "Data Key"
msgstr ""

#: cornerstone/includes/integration/Api/ApiControls.php:181
msgid "If sent an object from your API, which key in that object would you like to use. Leave empty to ignore"
msgstr ""

#: cornerstone/includes/integration/Api/ApiExtension.php:104
msgid "Return Type"
msgstr ""

#: cornerstone/includes/integration/Api/ApiExtension.php:143
msgid "Request"
msgstr ""

#: cornerstone/includes/integration/Api/ApiExtension.php:145
msgid "The GET args for a GET request, or the form body on other methods"
msgstr ""

#: cornerstone/includes/integration/Api/ApiFileReturn.php:8
msgid "File Return"
msgstr ""

#: cornerstone/includes/integration/Api/ApiFileReturn.php:14
msgid "File Name"
msgstr ""

#: cornerstone/includes/integration/Api/ApiFileReturn.php:15
msgid "In the uploads directory, what is the name to return on the frontend"
msgstr ""

#: cornerstone/includes/integration/Api/ApiFileReturn.php:22
msgid "Local File"
msgstr ""

#: cornerstone/includes/integration/Api/ApiFileReturn.php:23
msgid "Return a local file path as your server would use it internally, or return a URL an end user could use. Both are placed in the uploads directory"
msgstr ""

#: cornerstone/includes/integration/Api/ApiFileReturn.php:42
msgid "No return file passed"
msgstr ""

#: cornerstone/includes/integration/Api/ApiGlobalLooper.php:19
msgid "External API Global"
msgstr ""

#: cornerstone/includes/integration/Api/ApiGraphQL.php:8
msgid "GraphQL"
msgstr ""

#: cornerstone/includes/integration/Api/ApiGraphQL.php:13
msgid "Operation"
msgstr ""

#: cornerstone/includes/integration/Api/ApiGraphQL.php:14
msgid "Query or Mutation name. This is not always required, but can help debug on the other servers end"
msgstr ""

#: cornerstone/includes/integration/Api/ApiGraphQL.php:27
msgid "GraphQL Query"
msgstr ""

#: cornerstone/includes/integration/Api/ApiGraphQL.php:40
msgid "Variables JSON"
msgstr ""

#: cornerstone/includes/integration/Api/ApiTesterPrefab.php:15
msgid "API Tester"
msgstr ""

#: cornerstone/includes/integration/Api/ApiThemeOptions.php:41
msgid "API"
msgstr ""

#: cornerstone/includes/integration/Api/ApiThemeOptions.php:51
msgid "XML Legacy Mode"
msgstr ""

#: cornerstone/includes/integration/Api/ApiThemeOptions.php:52
msgid "Parses the XML the same way Cornerstone 7.4 did. Added to ease the upgrade process. This is being removed eventually, do not enable on a new site. See https://theme.co/docs/external-api-xml-change"
msgstr ""

#: cornerstone/includes/integration/Api/ApiThemeOptions.php:58
msgid "Global Endpoints"
msgstr ""

#: cornerstone/includes/integration/Api/ApiThemeOptions.php:66
msgid "My Endpoint"
msgstr ""

#: cornerstone/includes/integration/Api/ApiXML.php:10
msgid "XML"
msgstr ""

#: cornerstone/includes/integration/Api/ApiYaml.php:7
msgid "Yaml"
msgstr ""

#: cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:35, cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:198
msgid "Built with Cornerstone"
msgstr ""

#: cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:36
msgid "Share the love and display a custom badge on your website showing it was built with Cornerstone."
msgstr ""

#: cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:56, cornerstone/includes/elements/classic/icon-list-item/controls.php:26, cornerstone/includes/elements/classic/_alternate/card.php:176, cornerstone/includes/elements/classic/_alternate/feature-headline.php:82, cornerstone/includes/elements/classic/_alternate/icon.php:29
msgid "Icon Color"
msgstr ""

#: cornerstone/includes/integration/BuiltWithCornerstone/BuiltWithCornerstone.php:101, cornerstone/includes/_classes/classic/class-control-mixins.php:172
msgid "Border Color"
msgstr ""

#: cornerstone/includes/integration/csv/controls.php:14
msgid "Has Header"
msgstr ""

#: cornerstone/includes/integration/csv/controls.php:15
msgid "Is the first row a list of header / column titles? Will use integers as the keys of the CSV if not, and the first row will be looped over"
msgstr ""

#: cornerstone/includes/integration/csv/controls.php:23
msgid "Which character delimits each column of data. This is typically a comma (,) or pipe (|)"
msgstr ""

#: cornerstone/includes/integration/csv/controls.php:47, cornerstone/includes/integration/csv/controls.php:61
msgid "File"
msgstr ""

#: cornerstone/includes/integration/DesandroMasonry/DesandroMasonry.php:59
msgid "Masonry"
msgstr ""

#: cornerstone/includes/integration/DesandroMasonry/DesandroMasonry.php:80
msgid "Origin Left"
msgstr ""

#: cornerstone/includes/integration/DesandroMasonry/DesandroMasonry.php:81
msgid "When disabled the bricks will be aligned from right to left"
msgstr ""

#: cornerstone/includes/integration/DesandroMasonry/DesandroMasonry.php:88
msgid "Origin Top"
msgstr ""

#: cornerstone/includes/integration/DesandroMasonry/DesandroMasonry.php:89
msgid "When disabled the bricks will be aligned from the bottom to the top. Sometimes leaving empty space at the top of the container"
msgstr ""

#: cornerstone/includes/integration/DesandroMasonry/DesandroMasonry.php:96
msgid "Horizontal Order"
msgstr ""

#: cornerstone/includes/integration/DesandroMasonry/DesandroMasonry.php:97
msgid "When disabled the order of the bricks will not preserve their order horziontally"
msgstr ""

#: cornerstone/includes/integration/DesandroMasonry/DesandroMasonry.php:103
msgid "Stagger"
msgstr ""

#: cornerstone/includes/integration/DesandroMasonry/DesandroMasonry.php:104
msgid "Amount of time in milliseconds before creating the layout. If you have a third party JS in one of your bricks this can help to make sure the layout sizing happens after that element has created itself"
msgstr ""

#: cornerstone/includes/integration/Forminator/Forminator.php:15, cornerstone/includes/integration/Forminator/Forminator.php:32
msgid "Forminator"
msgstr ""

#: cornerstone/includes/integration/Forminator/Forminator.php:59
msgid "Forminator is not active"
msgstr ""

#: cornerstone/includes/integration/Forminator/Forminator.php:70, cornerstone/includes/elements/classic/_alternate/gravity-forms.php:33
msgid "Select a Form"
msgstr ""

#: cornerstone/includes/integration/ScrollProgress/ScrollProgress.php:21, cornerstone/includes/integration/ScrollProgress/ScrollProgress.php:35
msgid "Scroll Progress"
msgstr ""

#: cornerstone/includes/integration/ScrollProgress/ScrollProgress.php:40
msgid "Will control the width (or height in vertical mode) of the line element based on the percentage the user has scrolled through the page"
msgstr ""

#: cornerstone/includes/integration/WordPress/post-password.php:12
msgid "Post Password"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:4
msgid "Automatic Updates"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:39
msgid "Admin Notifications"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:40
msgid "Get updates in WordPress."
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:46
msgid "Stay Up to Date"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:47
msgid "Use the latest features right away."
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:53
msgid "Manual No More"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:54
msgid "Say goodbye to your FTP client."
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:58
msgid "Setup Now"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:9, plugins/cornerstone-charts/extension/Admin/PluginsPage.php:11
msgid "Changelog"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:15
msgid "Installed Version"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:22
msgid "Latest Version Available"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:29
msgid "Checked Every 12 Hours"
msgstr ""

#: cornerstone/includes/views/admin/home-box-auto-updates.php:30
msgid "Check Now"
msgstr ""

#: cornerstone/includes/views/admin/home-box-support.php:11
msgid "Real People"
msgstr ""

#: cornerstone/includes/views/admin/home-box-support.php:12
msgid "A professional and courteous staff."
msgstr ""

#: cornerstone/includes/views/admin/home-box-support.php:18
msgid "Around the Clock"
msgstr ""

#: cornerstone/includes/views/admin/home-box-support.php:19
msgid "Get help at any time, day or night."
msgstr ""

#: cornerstone/includes/views/admin/home-box-support.php:26
msgid "Dozens of articles and videos."
msgstr ""

#: cornerstone/includes/views/admin/home-box-support.php:35
msgid "Get World-Class Support"
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:36
msgid "Professionally Designed"
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:37
msgid "Replace with your own content."
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:43
msgid "Pages and Blocks"
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:44
msgid "Create layouts quickly and easily."
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:50, cornerstone/includes/views/admin/home-box-templates.php:26
msgid "More to Come"
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:51, cornerstone/includes/views/admin/home-box-templates.php:27
msgid "Keep your eyes peeled."
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:55
msgid "Unlock Templates"
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:12
msgid "Now Available"
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:13
msgid "Professional Themeco designs."
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:19
msgid "Access in Cornerstone"
msgstr ""

#: cornerstone/includes/views/admin/home-box-templates.php:20
msgid "Locate under Layout &rarr; Templates."
msgstr ""

#: cornerstone/includes/views/admin/home-sidebar.php:7
msgid "A separate license is required for each site."
msgstr ""

#: cornerstone/includes/views/admin/home-sidebar.php:14
msgid "Your site is validated. <a href=\"#\" data-tco-module-target=\"revoke\">Revoke validation</a>."
msgstr ""

#: cornerstone/includes/views/admin/home-validate-overlay.php:14
msgid "How do I unlock this feature?"
msgstr ""

#: cornerstone/includes/views/admin/home-validate-overlay.php:15
msgid "If you have already purchased Cornerstone from CodeCanyon, you can find your purchase code <a href=\"https://theme.co/apex/images/find-cornerstone-purchase-code.png\" target=\"_blank\">here</a>. If you do not have a license or need to get another, you can <a href=\"https://theme.co/go/join-validation-cs.php\" target=\"_blank\">purchase</a> one."
msgstr ""

#: cornerstone/includes/views/admin/home-validate-overlay.php:16
msgid "Where do I enter my purchase code?"
msgstr ""

#: cornerstone/includes/views/admin/home-validate-overlay.php:17
msgid "Once you have a purchase code you can <a %s href=\"#\">enter</a> it in the input at the top of this page."
msgstr ""

#: cornerstone/includes/views/admin/home-validation.php:14
msgid "You&apos;re almost finished!"
msgstr ""

#: cornerstone/includes/views/admin/home-validation.php:15
msgid "Your license is <strong class=\"tco-c-nope\">not validated</strong>. Place your Envato purchase code or Themeco license for instant access to automatic updates, support, and custom templates. <a href=\"https://theme.co/docs/product-validation/\" target=\"_blank\">Learn more</a> about product validation or <a href=\"https://theme.co/account/dashboard/\" target=\"_blank\">manage licenses</a> directly in your Themeco account."
msgstr ""

#: cornerstone/includes/views/admin/home-validation.php:29
msgid "Input Code and Hit Enter"
msgstr ""

#: cornerstone/includes/views/partials/mini-cart.php:54
msgid "The shopping cart currently unavailable."
msgstr ""

#: cornerstone/includes/views/partials/modal.php:76
msgid "Close Modal Content"
msgstr ""

#: cornerstone/includes/views/partials/modal.php:89
msgid "Modal Content"
msgstr ""

#: cornerstone/includes/views/partials/off-canvas.php:74
msgid "Close Off Canvas Content"
msgstr ""

#: cornerstone/includes/views/partials/off-canvas.php:85
msgid "Off Canvas Content"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:151
msgid "Border Style"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:185
msgid "Border Dimensions"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:202, cornerstone/includes/elements/classic/_alternate/image.php:62
msgid "Href"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:210, cornerstone/includes/_classes/classic/class-control-mixins.php:763, cornerstone/includes/elements/classic/_alternate/image.php:76
msgid "Link Title Attribute"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:218, cornerstone/includes/elements/classic/_alternate/creative-cta.php:140, cornerstone/includes/elements/classic/_alternate/image.php:89
msgid "Open Link in New Window"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:230, cornerstone/includes/_classes/classic/class-control-mixins.php:677
msgid "Hide based on screen width"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:285, cornerstone/includes/_classes/classic/class-control-mixins.php:807, cornerstone/includes/elements/classic/_alternate/feature-list.php:199
msgid "Animation Offset (%)"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:298, cornerstone/includes/_classes/classic/class-control-mixins.php:821
msgid "Animation Delay (ms)"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:329
msgid "Jello"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:330
msgid "Pulse"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:331
msgid "Rubber Band"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:332
msgid "Swing"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:333
msgid "Tada"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:334
msgid "Wobble"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:335
msgid "Flip"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:336
msgid "Flip In X"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:337
msgid "Flip In Y"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:338
msgid "Fade In"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:339
msgid "Fade In Up"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:340
msgid "Fade In Down"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:341
msgid "Fade In Left"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:342
msgid "Fade In Right"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:343
msgid "Bounce In"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:344
msgid "Bounce In Up"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:345
msgid "Bounce In Down"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:346
msgid "Bounce In Left"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:347
msgid "Bounce In Right"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:348
msgid "Rotate In"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:349
msgid "Rotate In Up Left"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:350
msgid "Rotate In Up Right"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:351
msgid "Rotate In Down Left"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:352
msgid "Rotate In Down Right"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:353
msgid "Zoom In"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:354
msgid "Zoom In Up"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:355
msgid "Zoom In Down"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:356
msgid "Zoom In Left"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:357
msgid "Zoom In Right"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:752
msgid "Enter a destination URL for when this is clicked."
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:774
msgid "Open Link in New Tab"
msgstr ""

#: cornerstone/includes/_classes/classic/class-control-mixins.php:836, cornerstone/includes/elements/classic/_alternate/creative-cta.php:156
msgid "Background Color"
msgstr ""

#: cornerstone/includes/_classes/dynamic-content/class-dynamic-content-looper.php:38
msgid "Index (Name)"
msgstr ""

#: cornerstone/includes/_classes/dynamic-content/class-dynamic-content-looper.php:46
msgid "Index (Number)"
msgstr ""

#: cornerstone/includes/_classes/dynamic-content/class-dynamic-content-looper.php:54
msgid "Index (Zero)"
msgstr ""

#: cornerstone/includes/_classes/dynamic-content/class-dynamic-content-post.php:201
msgid "Parent ID"
msgstr ""

#: cornerstone/includes/_classes/dynamic-content/class-dynamic-content-query.php:25
msgid "Current Page (Zero Index)"
msgstr ""

#: cornerstone/includes/_classes/dynamic-content/class-dynamic-content-query.php:31
msgid "Default Posts Per Page"
msgstr ""

#: cornerstone/includes/_classes/dynamic-content/class-dynamic-content-user.php:108
msgid "Current User Logged In"
msgstr ""

#: cornerstone/includes/_classes/dynamic-content/class-dynamic-content-user.php:126
msgid "All Roles"
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:12, cornerstone/includes/elements/classic/_alternate/skill-bar.php:25
msgid "Heading"
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:13
msgid "Text for your alert heading"
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:16
msgid "Alert Title"
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:23
msgid "Text for your alert content."
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:26
msgid "Click to inspect, then edit as needed."
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:33
msgid "There are multiple alert types for different situations. Select the one that best suits your needs."
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:39
msgid "Success"
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:41
msgid "Warning"
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:42
msgid "Danger"
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:50
msgid "Close Button"
msgstr ""

#: cornerstone/includes/elements/classic/alert/controls.php:51
msgid "Enabling the close button will make the alert dismissible, allowing your users to remove it if desired."
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/controls.php:12
msgid "Block Grid Items"
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/controls.php:13
msgid "Add a new item to your Block Grid."
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/controls.php:18
msgid "Block Grid Item %s"
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/controls.php:23
msgid "Block Grid Item 1"
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/controls.php:24
msgid "Block Grid Item 2"
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/controls.php:33
msgid "Select how many columns of items should be displayed on larger screens. These will update responsively based on screen size."
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/controls.php:37, cornerstone/includes/elements/classic/_alternate/recent-posts.php:52
msgid "2"
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/controls.php:38, cornerstone/includes/elements/classic/_alternate/recent-posts.php:53
msgid "3"
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/controls.php:39, cornerstone/includes/elements/classic/_alternate/recent-posts.php:54
msgid "4"
msgstr ""

#: cornerstone/includes/elements/classic/block-grid/definition.php:10
msgid "Block Grid"
msgstr ""

#: cornerstone/includes/elements/classic/block-grid-item/controls.php:12, cornerstone/includes/elements/classic/block-grid-item/definition.php:11
msgid "Block Grid Item"
msgstr ""

#: cornerstone/includes/elements/classic/block-grid-item/controls.php:19
msgid "Include your desired content for your Block Grid Item here."
msgstr ""

#: cornerstone/includes/elements/classic/block-grid-item/controls.php:22
msgid "Add some content to your block grid item here. The block grid responds a little differently than traditional columns, allowing you to mix and match for cool effects."
msgstr ""

#: cornerstone/includes/elements/classic/column/controls.php:21
msgid "Enable Fade Effect"
msgstr ""

#: cornerstone/includes/elements/classic/column/controls.php:22
msgid "Activating will make this column fade into view when the user scrolls to it for the first time."
msgstr ""

#: cornerstone/includes/elements/classic/column/controls.php:29
msgid "Fade Direction"
msgstr ""

#: cornerstone/includes/elements/classic/column/controls.php:30
msgid "Choose a direction to fade from. \"None\" will allow the column to fade in without coming from a particular direction."
msgstr ""

#: cornerstone/includes/elements/classic/column/controls.php:49
msgid "Determines how drastic the fade effect will be."
msgstr ""

#: cornerstone/includes/elements/classic/column/controls.php:61
msgid "Determines how long the fade effect will be."
msgstr ""

#: cornerstone/includes/elements/classic/column/definition.php:11
msgid "Column (v1)"
msgstr ""

#: cornerstone/includes/elements/classic/icon-list/controls.php:20
msgid "Icon List Item 1"
msgstr ""

#: cornerstone/includes/elements/classic/icon-list/controls.php:21
msgid "Icon List Item 2"
msgstr ""

#: cornerstone/includes/elements/classic/icon-list/controls.php:22
msgid "Icon List Item 3"
msgstr ""

#: cornerstone/includes/elements/classic/icon-list/definition.php:11
msgid "Icon List"
msgstr ""

#: cornerstone/includes/elements/classic/icon-list-item/controls.php:12
msgid "New Item"
msgstr ""

#: cornerstone/includes/elements/classic/icon-list-item/controls.php:19
msgid "Specify the icon you would like to use as the bullet for your Icon List Item."
msgstr ""

#: cornerstone/includes/elements/classic/icon-list-item/controls.php:27
msgid "Choose a custom color for your Icon List Item's icon."
msgstr ""

#: cornerstone/includes/elements/classic/icon-list-item/controls.php:40
msgid "Add a link to the text for this item."
msgstr ""

#: cornerstone/includes/elements/classic/icon-list-item/definition.php:11
msgid "Icon List Item"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table/controls.php:12
msgid "Pricing Table Columns"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table/controls.php:13
msgid "Add your pricing table columns here."
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table/controls.php:18
msgid "Column %s"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table/controls.php:24
msgid "Basic"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table/controls.php:24, cornerstone/includes/elements/classic/pricing-table-column/controls.php:22
msgid ""
"[cs_icon_list]\n"
"    [cs_icon_list_item type=\"check\"]First Feature[/cs_icon_list_item]\n"
"    [cs_icon_list_item type=\"times\"]Second Feature[/cs_icon_list_item]\n"
"    [cs_icon_list_item type=\"times\"]Third Feature[/cs_icon_list_item]\n"
"[/cs_icon_list]\n"
"\n"
"[x_button href=\"#\" size=\"large\"]Buy Now![/x_button]"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table/controls.php:25
msgid ""
"[cs_icon_list]\n"
"    [cs_icon_list_item type=\"check\"]First Feature[/cs_icon_list_item]\n"
"    [cs_icon_list_item type=\"check\"]Second Feature[/cs_icon_list_item]\n"
"    [cs_icon_list_item type=\"times\"]Third Feature[/cs_icon_list_item]\n"
"[/cs_icon_list]\n"
"\n"
"[x_button href=\"#\" size=\"large\"]Buy Now![/x_button]"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table/controls.php:26
msgid "Pro"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table/controls.php:26
msgid ""
"[cs_icon_list]\n"
"    [cs_icon_list_item type=\"check\"]First Feature[/cs_icon_list_item]\n"
"    [cs_icon_list_item type=\"check\"]Second Feature[/cs_icon_list_item]\n"
"    [cs_icon_list_item type=\"check\"]Third Feature[/cs_icon_list_item]\n"
"[/cs_icon_list]\n"
"\n"
"[x_button href=\"#\" size=\"large\"]Buy Now![/x_button]"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table/definition.php:11
msgid "Pricing Table"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/controls.php:19
msgid "Specify your pricing column content."
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/controls.php:28
msgid "Featured Column"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/controls.php:29
msgid "Enable to specify this column as your featured item."
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/controls.php:36
msgid "Featured Subheading"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/controls.php:37
msgid "Enter text for your featured column subheading here."
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/controls.php:47
msgid "Currency"
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/controls.php:48
msgid "Enter your desired currency symbol here."
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/controls.php:56
msgid "Enter the price for this column."
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/controls.php:64
msgid "Enter the duration for this payment (e.g. \"Weekly,\" \"Per Year,\" et cetera)."
msgstr ""

#: cornerstone/includes/elements/classic/pricing-table-column/definition.php:11
msgid "Pricing Table Column"
msgstr ""

#: cornerstone/includes/elements/classic/row/controls.php:22
msgid "Column Container"
msgstr ""

#: cornerstone/includes/elements/classic/row/controls.php:23
msgid "Disabling this control will allow your columns to be as wide as the browser window."
msgstr ""

#: cornerstone/includes/elements/classic/row/controls.php:30
msgid "Marginless Columns"
msgstr ""

#: cornerstone/includes/elements/classic/row/controls.php:31
msgid "This will remove the margin around your columns, allowing their borders to be flush with one another. This is often used to create block or grid layouts."
msgstr ""

#: cornerstone/includes/elements/classic/row/definition.php:11
msgid "Row (v1)"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:18
msgid "Classic Row"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:20
msgid "Classic Row %s"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:28
msgid "Background Type"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:29
msgid "Configure the background appearance for this Section."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:53
msgid "Background Pattern"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:54
msgid "Background patterns will tile and repeat across your Section."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:67
msgid "Background images are resized to fill the entire Section, regardless of screen size. Keep this in mind when using images that are already cropped."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:79
msgid "Switch how the image is applied to the background."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:88
msgid "Activates the parallax effect with background patterns and images."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:96
msgid "Background Video URL"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:97
msgid "Include your video URL(s) here. If using multiple sources, separate them using the pipe character (|) and place fallbacks towards the end (i.e. .webm then .mp4 then .ogv). For performance reasons, videos are not loaded into the editor but are shown live."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:101, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:27
msgid "video.mp4"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:109
msgid "Background Poster Image"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:110
msgid "Set an image to appear while the video is loading."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:118
msgid "Top Separator Type"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:119, cornerstone/includes/elements/classic/section/controls.php:179
msgid "Adds a style separator to your section to create visual interest. Can only be utilized with solid color backgrounds."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:125, cornerstone/includes/elements/classic/section/controls.php:185
msgid "Angle In"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:126, cornerstone/includes/elements/classic/section/controls.php:186
msgid "Angle Out"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:127, cornerstone/includes/elements/classic/section/controls.php:187
msgid "Curve In"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:128, cornerstone/includes/elements/classic/section/controls.php:188
msgid "Curve Out"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:136
msgid "Top Separator Height"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:137
msgid "Specify the height of your top separator."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:150
msgid "Top Separator Inset"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:151
msgid "Specify the inset value of your top separator if you notice any gaps with your settings."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:164
msgid "Top Angle Point"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:165, cornerstone/includes/elements/classic/section/controls.php:225
msgid "Specify a number between 0 and 100 to move the angle point."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:178
msgid "Bottom Separator Type"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:196
msgid "Bottom Separator Height"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:197
msgid "Specify the height of your bottom separator."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:210
msgid "Bottom Separator Inset"
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:211
msgid "Specify the inset value of your bottom separator if you notice any gaps with your settings."
msgstr ""

#: cornerstone/includes/elements/classic/section/controls.php:224
msgid "Bottom Angle Point"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion-item.php:10
msgid "Accordion Item description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion-item.php:34
msgid "Include your desired content for your Accordion Item here."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion-item.php:42
msgid "If the Accordion Items are linked, only one can start open."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion.php:10
msgid "Accordion description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion.php:21
msgid "Accordion Items"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion.php:22
msgid "Add a new item to your accordion."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion.php:24, cornerstone/includes/elements/classic/_alternate/accordion.php:25
msgid "Add some content to your accordion item here."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion.php:30
msgid "Accordion Item %s"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion.php:38
msgid "Link Items"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/accordion.php:39
msgid "This will make opening one item close the others."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/author.php:10
msgid "Author description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/author.php:25
msgid "Enter in a title for your author information."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/author.php:32
msgid "Author ID"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/author.php:33
msgid "By default the author of the post or page will be output by leaving this input blank. If you would like to output the information of another author, enter in their user ID here."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/blockquote.php:8
msgid "Blockquote"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/blockquote.php:10
msgid "Block Quote shortcode."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/blockquote.php:27
msgid "Enter the content of your quote."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/blockquote.php:28
msgid "Input your quotation here. Also, you can cite your quotes if you would like."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/blockquote.php:38
msgid "Include an optional citation to appear with the quote."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/blockquote.php:39
msgid "Mr. WordPress"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/blockquote.php:46
msgid "Select the alignment of the blockquote."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:10
msgid "Button description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:25
msgid "Enter your text."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:35
msgid "Select the button type."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:39, cornerstone/includes/elements/classic/_alternate/button.php:55, cornerstone/includes/elements/classic/_alternate/button.php:71
msgid "Global Setting"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:40
msgid "Real"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:41
msgid "Flat"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:42
msgid "Transparent"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:51
msgid "Select the button shape."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:57, cornerstone/includes/elements/classic/_alternate/feature-box.php:166, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:132, cornerstone/includes/elements/classic/_alternate/image.php:29
msgid "Rounded"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:58
msgid "Pill"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:67
msgid "Select the button size."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:72
msgid "Mini"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:76
msgid "X-Large"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:77
msgid "Jumbo"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:87
msgid "Select to make your button go fullwidth."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:94, cornerstone/includes/elements/classic/_alternate/callout.php:61, cornerstone/includes/elements/classic/_alternate/prompt.php:61
msgid "Marketing Circle"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:95, cornerstone/includes/elements/classic/_alternate/callout.php:62
msgid "Select to include a marketing circle around your button."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:102
msgid "Enable Icon"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:103
msgid "Select if you would like to add an icon to your button"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:110
msgid "Icon Placement"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:111
msgid "Place the icon before or after the button text, or even override the button text."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:116
msgid "Icon Only"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:127
msgid "Icon to be displayed inside your button."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:138
msgid "Select whether or not you want to add a popover or tooltip to your button."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:144, cornerstone/includes/elements/classic/_alternate/image.php:109
msgid "Tooltip"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:152, cornerstone/includes/elements/classic/_alternate/image.php:120
msgid "Info Placement"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:153, cornerstone/includes/elements/classic/_alternate/image.php:121
msgid "Select where you want your popover or tooltip to appear."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:172, cornerstone/includes/elements/classic/_alternate/image.php:141
msgid "Info Trigger"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:173, cornerstone/includes/elements/classic/_alternate/image.php:142
msgid "Select what actions you want to trigger the popover or tooltip."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:190, cornerstone/includes/elements/classic/_alternate/image.php:160
msgid "Info Content"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/button.php:191, cornerstone/includes/elements/classic/_alternate/image.php:161
msgid "Extra content for the popover."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:8
msgid "Callout"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:10
msgid "Callout description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:27
msgid "Enter the title for your Callout."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:28
msgid "Callout Title"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:35
msgid "Enter the message for your Callout."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:36
msgid "The message text for your Callout goes here."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:45, cornerstone/includes/elements/classic/_alternate/prompt.php:45
msgid "Button Text"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:46
msgid "Enter the text for your Callout button."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:53, cornerstone/includes/elements/classic/_alternate/prompt.php:53
msgid "Button Icon"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:54, cornerstone/includes/elements/classic/_alternate/prompt.php:54
msgid "Optionally enter the button icon."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/callout.php:72
msgid "Select the alignment for your Callout."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:10
msgid "Card description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:27
msgid "Specify the animation you would like to use for you Card while it flips."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:43
msgid "Center Vertically"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:44
msgid "Enabling this control ensures that all of your content is centered vertically in the Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:52
msgid "Specify the padding you would like to use for both sides of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:65
msgid "Set the title for the front of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:73
msgid "Set the content for the front of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:74
msgid "This is the content for the front of your Card. You can put anything you like here! Make sure it&apos;s something not too long though. As Shakespeare once said, &ldquo;Brevity is the soul of wit.&rdquo;"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:88
msgid "Front Border"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:114
msgid "Front Text Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:115
msgid "Select the text color for the front of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:122
msgid "Front Background Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:123
msgid "Select the background color for the front of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:130
msgid "Front Graphic"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:131
msgid "Choose between an icon and a custom image for your front graphic."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:151
msgid "Specify the icon you would like to use for your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:163, cornerstone/includes/elements/classic/_alternate/creative-cta.php:76, cornerstone/includes/elements/classic/_alternate/icon.php:45
msgid "Icon Size"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:164, cornerstone/includes/elements/classic/_alternate/creative-cta.php:77
msgid "Specify the size of your icon."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:177
msgid "Specify the color of your icon."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:195
msgid "Specify the image you would like to use for your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:207, cornerstone/includes/elements/classic/_alternate/creative-cta.php:102
msgid "Image Width"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:208, cornerstone/includes/elements/classic/_alternate/creative-cta.php:103
msgid "Specify the width of your image."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:225
msgid "Back Title "
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:226
msgid "Set the title for the back of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:234
msgid "Set the content for the back of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:235
msgid "This is the content for the back of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:249
msgid "Back Border"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:275
msgid "Back Text Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:276
msgid "Select the text color for the back of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:283
msgid "Back Background Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:284
msgid "Select the background color for the back of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:297
msgid "This will show a button on the back of the card, which you can link anywhere you like."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:304
msgid "Back Button Text"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:305
msgid "Specify the title and content for the back of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:317
msgid "Back Button Link"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:318
msgid "Specify the URL for the button on the back of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:330
msgid "Back Button Text Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:331
msgid "Select the text color for button on the back of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:343
msgid "Back Button Background Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/card.php:344
msgid "Select the background color for button on the back of your Card."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/clear.php:10
msgid "Clear description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/code.php:8
msgid "Code Snippet"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/code.php:10
msgid "Code Snippet description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/code.php:25
msgid "The content you want output. Keep in mind that this shortcode is meant to display code snippets, not output functioning code."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/code.php:26
msgid "This shortcode is great for outputting code snippets or preformatted text."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/columnize.php:8
msgid "Columnize"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/columnize.php:10
msgid "Columnize description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/columnize.php:26
msgid "Set the content you would like broken out into columns."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/contact-form-7.php:10
msgid "Contact Form 7 description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/contact-form-7.php:12
msgid "This element can not render because Contact Form 7 is not active."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/contact-form-7.php:31, cornerstone/includes/elements/classic/_alternate/gravity-forms.php:49, cornerstone/includes/elements/classic/_alternate/mailchimp.php:31
msgid "No Forms available"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/contact-form-7.php:37
msgid "Select Contact Form"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/contact-form-7.php:38
msgid "Select a previously created form."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:10
msgid "Counter description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:25
msgid "Starting Number"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:26
msgid "Enter in the number that you would like your counter to start from."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:33
msgid "Ending Number"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:34
msgid "Enter in the number that you would like your counter to end at. This must be higher than your starting number."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:41
msgid "Counter Speed"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:42
msgid "The amount of time to transition between numbers in milliseconds."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:49
msgid "Number Prefix"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:50
msgid "Prefix your number with a symbol or text."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:57
msgid "Number Suffix"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:58
msgid "Suffix your number with a symbol or text."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:65
msgid "Number Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:66
msgid "Select the color of your number."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:73
msgid "Text Above"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:74
msgid "Optionally include text above your number."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:75
msgid "There Are"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:81
msgid "Text Below"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:82
msgid "Optionally include text below your number."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/counter.php:90
msgid "Select the color of your text above and below the number if you have include any."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:10
msgid "Creative CTA description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:25
msgid "Specify the padding you would like to use for your Creative CTA."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:33
msgid "Specify the text for your Creative CTA."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:34
msgid "Click Here<br>To Learn More!"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:40
msgid "Text Size"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:41
msgid "Specify the size of your text."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:49, cornerstone/includes/elements/classic/_alternate/feature-box.php:64
msgid "Choose between an icon and a custom image for your graphic."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:64
msgid "Specify the icon you would like to use for your Creative CTA."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:90
msgid "Specify the image you would like to use for your Creative CTA."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:116
msgid "Specify the animation you would like to use for you Creative CTA."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:133
msgid "Specify the URL for your Creative CTA."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:141, cornerstone/includes/elements/classic/_alternate/image.php:90
msgid "Select to open your link in a new window."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:149
msgid "Select the text color for your Creative CTA."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:157
msgid "Select the background color for your Creative CTA."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:164
msgid "Background Color Hover"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/creative-cta.php:165
msgid "Select the background color hover for your Creative CTA."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:8, cornerstone/includes/elements/classic/_alternate/custom-headline.php:36
msgid "Custom Headline"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:10
msgid "Custom Headline description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:23, cornerstone/includes/elements/classic/_alternate/feature-headline.php:23, cornerstone/includes/elements/classic/_alternate/text-type.php:62, cornerstone/includes/elements/classic/_alternate/text-type.php:85
msgid "h1"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:24, cornerstone/includes/elements/classic/_alternate/feature-headline.php:24, cornerstone/includes/elements/classic/_alternate/text-type.php:63, cornerstone/includes/elements/classic/_alternate/text-type.php:86
msgid "h2"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:25, cornerstone/includes/elements/classic/_alternate/feature-headline.php:25, cornerstone/includes/elements/classic/_alternate/text-type.php:64, cornerstone/includes/elements/classic/_alternate/text-type.php:87
msgid "h3"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:26, cornerstone/includes/elements/classic/_alternate/feature-headline.php:26, cornerstone/includes/elements/classic/_alternate/text-type.php:65, cornerstone/includes/elements/classic/_alternate/text-type.php:88
msgid "h4"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:27, cornerstone/includes/elements/classic/_alternate/feature-headline.php:27, cornerstone/includes/elements/classic/_alternate/text-type.php:66, cornerstone/includes/elements/classic/_alternate/text-type.php:89
msgid "h5"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:28, cornerstone/includes/elements/classic/_alternate/feature-headline.php:28, cornerstone/includes/elements/classic/_alternate/text-type.php:67, cornerstone/includes/elements/classic/_alternate/text-type.php:90
msgid "h6"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:35, cornerstone/includes/elements/classic/_alternate/feature-headline.php:35
msgid "Text to be placed inside the heading element."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:42, cornerstone/includes/elements/classic/_alternate/feature-headline.php:42
msgid "Heading Level"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:43, cornerstone/includes/elements/classic/_alternate/feature-headline.php:43
msgid "Determines which heading level should be used in the actual HTML."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:55, cornerstone/includes/elements/classic/_alternate/feature-headline.php:55, cornerstone/includes/elements/classic/_alternate/text-type.php:81
msgid "Allows you to alter the appearance of the heading, while still outputting it as a different HTML tag."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:67, cornerstone/includes/elements/classic/_alternate/feature-headline.php:67
msgid "Choose a specific color for the headline text. Reset the color picker to inherit a color."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:74
msgid "Accent"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/custom-headline.php:75
msgid "Select to activate the heading accent."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-audio.php:8
msgid "Embedded Audio"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-audio.php:10
msgid "Embedded Audio description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-audio.php:22, cornerstone/includes/elements/classic/_alternate/embedded-video.php:22, cornerstone/includes/elements/classic/_alternate/map-embed.php:22
msgid "Embed Code"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-audio.php:23, cornerstone/includes/elements/classic/_alternate/embedded-video.php:23, cornerstone/includes/elements/classic/_alternate/map-embed.php:23
msgid "Input your &lt;iframe&gt; or &lt;embed&gt; code from a third party service."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:8
msgid "Embedded Video"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:10
msgid "Embedded Video description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:31, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:59
msgid "Select your aspect ratio."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:35, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:63
msgid "16:9"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:36, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:64
msgid "5:3"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:37, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:65
msgid "5:4"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:38, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:66
msgid "4:3"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:39, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:67
msgid "3:2"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:47, cornerstone/includes/elements/classic/_alternate/google-map.php:100, cornerstone/includes/elements/classic/_alternate/map-embed.php:30, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:91, cornerstone/includes/elements/classic/_alternate/slider.php:117
msgid "No Container"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/embedded-video.php:48, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:92
msgid "Select to remove the container around the video."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/envira-gallery.php:8
msgid "Envira Gallery"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/envira-gallery.php:10
msgid "Place an Envira Gallery element into your content."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/envira-gallery.php:13
msgid "This element can not render because Envira Gallery is not active."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/envira-gallery.php:49
msgid "No Galleries Available"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/envira-gallery.php:58
msgid "Select Gallery"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/envira-gallery.php:59
msgid "Choose from Envira Gallery elements that have already been created."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/essential-grid.php:8
msgid "Essential Grid"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/essential-grid.php:10
msgid "Place an Essential Grid element into your content."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/essential-grid.php:13
msgid "This element can not render because Essential Grid is not active."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/essential-grid.php:41
msgid "No Grids Available"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/essential-grid.php:50
msgid "Select Grid"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/essential-grid.php:51
msgid "Choose from Essential Grid elements that have already been created."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:8
msgid "Feature Box"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:10
msgid "Feature Box description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:27
msgid "Set the title for your Feature Box."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:28
msgid "Feature Box Title"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:34
msgid "Title &amp; Content"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:35
msgid "Set the content for your Feature Box."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:36
msgid "This is where the text for your Feature Box should go. It&apos;s best to keep it short and sweet."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:42, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:45
msgid "Title Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:43, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:46
msgid "Optionally specify colors for your title."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:50, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:53
msgid "Content Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:51, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:54
msgid "Optionally specify colors for your content."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:83, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:67
msgid "Specify the icon you would like to use for your Feature List Item."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:96, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:80
msgid "Specify the image you would like to use for your Feature List Item."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:108, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:87
msgid "Graphic Alt Text"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:126, cornerstone/includes/elements/classic/_alternate/feature-list.php:79
msgid "Graphic Size"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:127, cornerstone/includes/elements/classic/_alternate/feature-list.php:80
msgid "Specify the size of your graphic."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:139, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:105
msgid "Graphic Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:140, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:106
msgid "Specify the color of your graphic."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:147, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:113
msgid "Graphic Background Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:148, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:114
msgid "Specify the background color of your graphic."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:160, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:126
msgid "Graphic Shape"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:161
msgid "Choose a shape for your Feature Box graphic."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:168
msgid "Hexagon (Icon Only)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:169
msgid "Badge (Icon Only)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:182, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:148
msgid "Graphic Border"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:218, cornerstone/includes/elements/classic/_alternate/feature-list.php:177
msgid "Graphic Animation"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:222
msgid "Graphic Animation Offset (%)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:231
msgid "Graphic Animation Delay (ms)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:248, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:184
msgid "Link Text"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:249
msgid "Enter the text for your Feature Box link. Leave blank to remove."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:258, cornerstone/includes/elements/classic/_alternate/feature-list-item.php:194
msgid "Link Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:259
msgid "Specify a custom color for your Feature Box link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:271, cornerstone/includes/elements/classic/_alternate/feature-list.php:92
msgid "Horizontal Alignment"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:272
msgid "Select the horizontal alignment of the Feature Box."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:286, cornerstone/includes/elements/classic/_alternate/feature-list.php:106
msgid "Vertical Alignment"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:287
msgid "Select the vertical alignment of the Feature Box."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:292, cornerstone/includes/elements/classic/_alternate/feature-list.php:112
msgid "Middle"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:303, cornerstone/includes/elements/classic/_alternate/feature-list.php:120
msgid "Graphic Spacing"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:304, cornerstone/includes/elements/classic/_alternate/feature-list.php:121
msgid "Specify an amount of spacing you want between your side graphic and the content."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-box.php:317
msgid "Enter in a max width for your Feature Box if desired. This will keep your Feature Box from stretching out too far on smaller breakpoints."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-headline.php:8, cornerstone/includes/elements/classic/_alternate/feature-headline.php:36
msgid "Feature Headline"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-headline.php:10
msgid "Feature Headline description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-headline.php:75
msgid "Icon to be displayed next to the Feature Headline."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-headline.php:83
msgid "Choose a specific color for the icon. Reset the color picker to inherit a color."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-headline.php:90, cornerstone/includes/elements/classic/_alternate/icon.php:37
msgid "Icon Background Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-headline.php:91
msgid "Choose a specific background color for the icon. Reset the color picker to inherit a color."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list-item.php:8, cornerstone/includes/elements/classic/_alternate/feature-list.php:25, cornerstone/includes/elements/classic/_alternate/feature-list.php:33, cornerstone/includes/elements/classic/_alternate/feature-list.php:41
msgid "Feature List Item"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list-item.php:10
msgid "Feature List Item description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list-item.php:38
msgid "Specify the content for your Feature List Item."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list-item.php:39
msgid "This is where the text for your Feature List Item should go. It&apos;s best to keep it short and sweet."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list-item.php:127
msgid "Choose a shape for your Feature List Item graphic."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list-item.php:134
msgid "Hexagon (Icon and Numbers Only)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list-item.php:135
msgid "Badge (Icon and Numbers Only)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list-item.php:185
msgid "Enter the text for your Feature List Item link. Leave blank to remove."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list-item.php:195
msgid "Specify a custom color for your Feature List Item link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:8
msgid "Feature List"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:10
msgid "Feature List description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:21
msgid "Feature List Items"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:22
msgid "Add your Feature List Items here."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:26, cornerstone/includes/elements/classic/_alternate/feature-list.php:34, cornerstone/includes/elements/classic/_alternate/feature-list.php:42
msgid "This is an Feature List Item that is part of an Feature List. Notice the connector between the three graphics to show that they are related."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:51
msgid "Feature List Item %s"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:65
msgid "Choose between an icon, a custom image, or incremental numbers for your graphic."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:71
msgid "Numbers"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:93
msgid "Select the horizontal alignment of the Feature List Item."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:107
msgid "Select the vertical alignment of the Feature List Item."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:129
msgid "Enter in a max width for your Feature List Item if desired. This will keep your Feature List Item from stretching out too far on smaller breakpoints."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:141
msgid "Connector Style"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:142
msgid "Specify the style of the connector between graphics."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:156
msgid "Connector Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:157
msgid "Specify the color of the connector between graphics."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:164
msgid "Connector Width"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:165
msgid "Specify the width of the connector between graphics."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:178, cornerstone/includes/elements/classic/_alternate/feature-list.php:189
msgid "Optionally add animation to your element as users scroll down the page."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:188
msgid "Connector Animation"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:200
msgid "Specify a percentage value where the element should appear on screen for the animation to take place."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:213
msgid "Animation Initial Delay (ms)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:214
msgid "Specify an amount of time before the graphic animation starts in milliseconds."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:227
msgid "Animation Delay Between (ms)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/feature-list.php:228
msgid "Specify an amount of time between graphic animations in milliseconds."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/gap.php:10
msgid "Gap description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/gap.php:26
msgid "Enter in the size of your gap. Pixels, ems, and percentages are all valid units of measurement."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/gap.php:29
msgid "50px (accepts CSS units)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map-marker.php:8
msgid "Google Map Marker"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map-marker.php:10
msgid "Google Map Marker description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map-marker.php:32
msgid "Enter the latitude for your map marker."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map-marker.php:40
msgid "Enter the longitude for your map marker."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map-marker.php:48
msgid "Enter in optional text to appear if your map marker is hovered over."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map-marker.php:55
msgid "Start Open"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map-marker.php:56
msgid "Toggle if the marker info should be open by default."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map-marker.php:64
msgid "Upload an optional alternate image to use in place of the standard map marker."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:8
msgid "Google Map"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:10
msgid "Google Map description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:23
msgid "Optionally include markers to your map to specify certain locations."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:28
msgid "Map Marker %s"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:35
msgid "Google API Key"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:36
msgid "Optionally provide a Browser key from your Google developer console."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:44
msgid "Enter the latitude for the center of your map."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:52
msgid "Enter the longitude for the center of your map."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:60
msgid "Specify a number between 1 and 18 for the zoom level of your map."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:67
msgid "Zoom Control"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:68
msgid "Enable to display the zoom controls for your map."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:76
msgid "Enable to make your map draggable."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:84
msgid "Specify a custom height for your map if desired. You may use pixels, ems, or percentages."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:91
msgid "Map Hue"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:92
msgid "Specifying a hexadecimal map hue will give your map a different color palette."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/google-map.php:101, cornerstone/includes/elements/classic/_alternate/map-embed.php:31
msgid "Select to remove the container around the map."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/gravity-forms.php:14
msgid "Display issues?"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/gravity-forms.php:15
msgid "<strong>Gravity Forms</strong> uses its own dynamic process to render forms, which could result in visual differences in the preview area. Be sure to test by viewing the true front end of this page."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/gravity-forms.php:18
msgid "This element can not render because Gravity Forms is not active."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/gravity-forms.php:90
msgid "Tabindex ID"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/gravity-forms.php:91
msgid "Specify the starting tab index for the fields of this Gravity Form."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/gravity-forms.php:98
msgid "Default Values"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/icon.php:10
msgid "Icon description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/icon.php:22
msgid "Specify the icon you would like to use."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/icon.php:30
msgid "Specify a custom color for your icon if desired."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/icon.php:38
msgid "Specify a custom background color for your icon if desired."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/icon.php:46, cornerstone/includes/elements/classic/_alternate/icon.php:54
msgid "Specify custom dimensions for your icon for use in situations other than inline."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/icon.php:53
msgid "Background Size"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/icon.php:61
msgid "Background Border Radius"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/icon.php:62
msgid "Give your icon's background a custom border radius."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:10
msgid "Image description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:23
msgid "Select the image style."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:38
msgid "Src"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:39
msgid "Enter your image."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:46
msgid "Alt"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:47
msgid "Enter in the alt text for your image"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:55
msgid "Select to wrap your image in an anchor tag."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:63
msgid "Enter in the URL you want to link to."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:77
msgid "Enter in the title attribute you want for your link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/image.php:103
msgid "Select whether or not you want to add a popover or tooltip to your image."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/layerslider.php:8
msgid "LayerSlider"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/layerslider.php:10
msgid "Place a LayerSlider element into your content."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/layerslider.php:13
msgid "This element can not render because Layer Slider is not active."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/layerslider.php:39, cornerstone/includes/elements/classic/_alternate/revolution-slider.php:40
msgid "No Slider Available"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/layerslider.php:48, cornerstone/includes/elements/classic/_alternate/revolution-slider.php:49, cornerstone/includes/elements/classic/_alternate/soliloquy.php:51
msgid "Select Slider"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/layerslider.php:49
msgid "Choose from LayerSlider elements that have already been created."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/line.php:10
msgid "Line description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/line.php:21
msgid "Choose a specific color for this line. Reset the color picker to inherit a color."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/line.php:29
msgid "Specify a height for this line."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/mailchimp.php:8
msgid "Mailchimp"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/mailchimp.php:10
msgid "Mailchimp description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/mailchimp.php:37
msgid "Select Email Form"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/mailchimp.php:38
msgid "Select a previously setup email form."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/map-embed.php:8
msgid "Map Embed"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/map-embed.php:10
msgid "Map Embed description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/promo.php:8
msgid "Promo"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/promo.php:10
msgid "Promo description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/promo.php:26
msgid "Enter your Promo content."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/promo.php:33
msgid "Promo Image"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/promo.php:34
msgid "Include an image for your Promo element."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/promo.php:43
msgid "Set the alt text for your included Promo image."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:8
msgid "Prompt"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:10
msgid "Prompt description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:26
msgid "Title "
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:27
msgid "Enter the title for your Prompt."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:28
msgid "Prompt Title"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:35
msgid "Enter the content for your Prompt."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:36
msgid "This is where the main content for your Prompt can go."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:46
msgid "Enter the text for your Prompt button."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:62
msgid "Select to include a marketing circle around your button"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/prompt.php:72
msgid "Select the alignment of your Prompt."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/protect.php:8
msgid "Protect"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/protect.php:10
msgid "Protect description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/protect.php:12
msgid "How does this work?"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/protect.php:13
msgid "This element offers simple protection based on being logged in. Logged out users will be prompted to login before viewing the content."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/protect.php:26
msgid "Login Heading"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/protect.php:27
msgid "Edit the heading promting users to login."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/protect.php:38
msgid "Enter the text to go inside your Protect shortcode. This will only be visible to users who are logged in."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/raw-content.php:10
msgid "Raw Content description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/raw-content.php:12
msgid "Using Javascript?"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/raw-content.php:13
msgid "We recommend using <strong>Custom JS</strong> in <strong class=\"glue\">%s Settings</strong>. Be sure to test on the front end, as it may not work as expected in the preview."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/raw-content.php:27
msgid "Accepts shortcodes and no special formatting is applied to this output. Keep in mind if your markup is empty or styled in a way that you cannot see it and you click away, you will not be able to get back to this element."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:10
msgid "Recent Posts description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:35
msgid "Choose between standard posts or portfolio posts."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:46
msgid "Post Count"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:47
msgid "Select how many posts to display."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:51
msgid "1"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:63
msgid "Enter a number to offset initial starting post of your Recent Posts."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:70
msgid "Category"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:71
msgid "To filter your posts by category, enter in the slug of your desired category. To filter by multiple categories, enter in your slugs separated by a comma."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:79
msgid "Select the orientation or your Recent Posts."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:92
msgid "Ignore Sticky Posts"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:93
msgid "Select to ignore sticky posts."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:100
msgid "Remove Featured Image"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:101
msgid "Select to remove the featured image."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:108
msgid "Fade Effect"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/recent-posts.php:109
msgid "Select to activate the fade effect."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/revolution-slider.php:8
msgid "Revolution Slider"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/revolution-slider.php:10
msgid "Place a Revolution Slider element into your content."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/revolution-slider.php:13
msgid "This element can not render because Revolution Slider is not active."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/revolution-slider.php:50
msgid "Choose from Revolution Slider elements that have already been created."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/search.php:10
msgid "Search description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:8
msgid "Audio Player"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:10
msgid "Audio Player description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:21
msgid "Audio Src URL"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:22
msgid "Include your audio URL(s) here. If using multiple sources, separate them using the pipe character (|) and place fallbacks towards the end (i.e. .mp3 then .ogg)."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:26
msgid "audio.mp3"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:49, cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:114
msgid "Advanced Controls"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:50
msgid "Enable audio player's advanced controls."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:58
msgid "Specifies if and how the audio should be loaded when the page loads. \"None\" means the audio is not loaded when the page loads, \"Auto\" loads the audio entirely, and \"Metadata\" loads only metadata."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:73
msgid "Enable audio player's autoplay."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-audio.php:81
msgid "Enable audio player's loop."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:8
msgid "Video Player"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:10
msgid "Video Player description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:22
msgid "Video Src URL"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:23
msgid "Include your video URL(s) here. If using multiple sources, separate them using the pipe character (|) and place fallbacks towards the end (i.e. .webm then .mp4 then .ogv)."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:50
msgid "Video Poster Image"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:51
msgid "Include a poster image to appear before the video is played."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:75
msgid "Hide Controls"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:76
msgid "Select to hide the controls on your self-hosted video."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:84
msgid "Select to automatically play your self-hosted video."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:100
msgid "Specifies if and how the video should be loaded when the page loads. \"None\" means the video is not loaded when the page loads, \"Auto\" loads the video entirely, and \"Metadata\" loads only metadata."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:115
msgid "Enable video player's advanced controls."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:123
msgid "Mute video player's audio."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/self-hosted-video.php:131
msgid "Enable looping of video playback."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:8
msgid "Skill Bar"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:10
msgid "Skill Bar description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:26
msgid "Enter the heading of your Skill Bar."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:27
msgid "Skill Bar Title"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:33
msgid "Percent"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:34
msgid "Enter the percentage of your skill and be sure to include the percentage sign (e.g. 90%)."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:41
msgid "Bar Text"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:42
msgid "Enter in some alternate text in place of the percentage inside the Skill Bar."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:49
msgid "Bar Background Color"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/skill-bar.php:50
msgid "Select the background color of your Skill Bar."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slide.php:10
msgid "Slide description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slide.php:33
msgid "Include your desired content for your Slide here."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:10
msgid "Slider description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:22
msgid "Add a new slide to your slider."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:24
msgid "Slide 1"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:25
msgid "Slide 2"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:29
msgid "Slide %s"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:38
msgid "Choose between a fade and a slide animation."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:51
msgid "Animation Speed"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:52
msgid "The amount of time in milliseconds the transition between each slide should take."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:59
msgid "Slideshow"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:60
msgid "Enabling this control will have your slider automatically cycle through like a slideshow."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:67
msgid "Slide Duration"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:68
msgid "The amount of time in milliseconds each slide should remain visible before transitioning to the next one."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:80
msgid "Pause On Hover"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:81
msgid "Pause the transition delay when the mouse is over the slider."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:94
msgid "Select to have your slider appear in a random order each time the page loads."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:101
msgid "Control Navigation"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:102
msgid "Select to enable the control navigation, which displays how many slides you have in your slider."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:109
msgid "Prev/Next Navigation"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:110
msgid "Select to enable the prev/next navigation, which displays two arrows for you to cycle through the slides in your slider."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:118
msgid "Select to remove the container around the slider."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:125
msgid "Touch Navigation"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:126
msgid "Allow touch devices to navigate with a swipe guesture."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:133
msgid "Reverse (Left to Right)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/slider.php:134
msgid "Reverse the animation direction."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:8
msgid "Social Sharing"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:10
msgid "Social Sharing description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:23
msgid "Enter in a title for your social links."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:30
msgid "Share Title"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:31
msgid "Enter in the title that displays when you share the page. The default is the Page title."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:39
msgid "Select to activate the Facebook sharing link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:47
msgid "Select to activate the Twitter sharing link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:55
msgid "Select to activate the LinkedIn sharing link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:63
msgid "Select to activate the Pinterest sharing link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:71
msgid "Select to activate the Reddit sharing link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:79
msgid "Select to activate the email sharing link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:86
msgid "Email Subject"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/social-sharing.php:87
msgid "Enter the email subject when sharing the link."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/soliloquy.php:8
msgid "Soliloquy"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/soliloquy.php:10
msgid "Place an Soliloquy element into your content."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/soliloquy.php:13
msgid "This element can not render because Soliloquy is not active."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/soliloquy.php:42
msgid "No Sliders Available"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/soliloquy.php:52
msgid "Choose from Soliloquy elements that have already been created."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tab.php:10
msgid "Tab description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tab.php:33
msgid "Include your desired content for your Tab here."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tab.php:40
msgid "Initial Active Tab"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tab.php:41
msgid "Only one tab must be specified as the initial active Tab. If no active Tab or multiple active Tabs are specified, there will be layout errors."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tabs.php:10
msgid "Tabs description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tabs.php:22
msgid "Add a new tab."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tabs.php:24, cornerstone/includes/elements/classic/_alternate/tabs.php:25
msgid "The content for your Tab goes here. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque pretium, nisi ut volutpat mollis, leo risus interdum arcu, eget facilisis quam felis id mauris. Ut convallis, lacus nec ornare volutpat, velit turpis scelerisque purus, quis mollis velit purus ac massa. Fusce quis urna metus. Donec et lacus et sem lacinia cursus."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tabs.php:30
msgid "Tab %s"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tabs.php:39
msgid "Navigation Position"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/tabs.php:40
msgid "Choose the positioning of your navigation for your tabs."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:8
msgid "Text Type"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:10
msgid "Text Type description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:31
msgid "Enter a prefix to appear before the animating text."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:32
msgid "This is the "
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:38
msgid "Strings"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:39
msgid "Enter strings to be animated and separate them by a new line."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:50
msgid "Enter a suffix to appear after the animating text."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:51
msgid " of the sentence."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:58
msgid "Specify the HTML tag you would like to use to output this shortcode."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:68
msgid "p"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:69
msgid "div"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:70
msgid "span"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:107
msgid "Type Speed (ms)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:108
msgid "How fast in milliseconds each character should appear."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:115
msgid "Start Delay (ms)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:116
msgid "How long in milliseconds until typing should start."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:123
msgid "Back Speed (ms)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:124
msgid "How fast in milliseconds each character should be deleted."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:131
msgid "Back Delay (ms)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:132
msgid "How long in milliseconds each string should remain visible."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:145
msgid "Enable to have the typing effect loop continuously."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:153
msgid "Enable to display a cursor for your typing effect."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/text-type.php:161
msgid "Specify the character you would like to use for your cursor."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/toc-item.php:8
msgid "TOC Item"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/visibility.php:8
msgid "Visibility"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/widget-area.php:10
msgid "Widget Area description."
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/widget-area.php:35
msgid "(No widgets specified.)"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/widget-area.php:47
msgid "Registered Sidebar"
msgstr ""

#: cornerstone/includes/elements/classic/_alternate/widget-area.php:48
msgid "Sidebars with no widgets will appear disabled."
msgstr ""

#: cornerstone/includes/integration/Max/views/page-home-box-max.php:51
msgid "Get instant access to premium <b>Courses</b>, <b>Expansion Packs</b>, and <b>Plugins</b>. New to Max?"
msgstr ""

#: cornerstone/includes/integration/Max/views/page-home-box-max.php:53
msgid "Recently purchased?"
msgstr ""

#: cornerstone/includes/integration/Twig/src/ThemeOptions.php:24, cornerstone/includes/integration/Twig/src/ThemeOptions.php:80
msgid "Twig"
msgstr ""

#: cornerstone/includes/integration/woocommerce/Controls/ProductSelect.php:11
msgid "Product"
msgstr ""

#: cornerstone/includes/integration/woocommerce/Controls/ProductSelect.php:15
msgid "Current Product"
msgstr ""

#: plugins/cornerstone-ai/Anthropic/Anthropic.php:75, plugins/cornerstone-ai/OpenAI/ChatControls.php:23, plugins/cornerstone-ai/OpenAI/TextToImageControls.php:21
msgid "Model"
msgstr ""

#: plugins/cornerstone-ai/Anthropic/Anthropic.php:83, plugins/cornerstone-ai/OpenAI/ChatControls.php:44
msgid "Max Tokens"
msgstr ""

#: plugins/cornerstone-ai/Anthropic/Anthropic.php:93, plugins/cornerstone-ai/OpenAI/ChatControls.php:64
msgid "Temperature"
msgstr ""

#: plugins/cornerstone-ai/Cornerstone/Keybindings.php:9
msgid "Open CSAI"
msgstr ""

#: plugins/cornerstone-ai/GPT4All/GPT4All.php:36, plugins/cornerstone-ai/OpenAI/ChatControls.php:27, plugins/cornerstone-ai/OpenAI/TextToImageControls.php:24
msgid "Select a Model"
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:161
msgid "Negative Prompt"
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:162
msgid "One or several prompt to guide what NOT to include in video generation."
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:166
msgid "Number of Frames"
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:170
msgid "Determines how many video frames are generated. Certain models ignore this."
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:174
msgid "Guidance Scale"
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:178
msgid "A higher guidance scale value encourages the model to generate videos closely linked to the text prompt, but values too high may cause saturation and other artifacts."
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:182
msgid "Inference Steps"
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:186
msgid "The number of denoising steps. More denoising steps usually lead to a higher quality video at the expense of slower inference."
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:191
msgid "Seed"
msgstr ""

#: plugins/cornerstone-ai/HuggingFace/HuggingFace.php:192
msgid "Seed for the random number generator. Negative values will use a random seed."
msgstr ""

#: plugins/cornerstone-ai/OpenAI/ChatControls.php:34
msgid "Frequency Penalty"
msgstr ""

#: plugins/cornerstone-ai/OpenAI/ChatControls.php:54
msgid "Presence Penalty"
msgstr ""

#: plugins/cornerstone-ai/OpenAI/ChatControls.php:75
msgid "Top P"
msgstr ""

#: plugins/cornerstone-ai/OpenAI/ChatControls.php:86
msgid "Number of Chats"
msgstr ""

#: plugins/cornerstone-ai/OpenAI/ImageToImageControls.php:41
msgid "DALL-E 2"
msgstr ""

#: plugins/cornerstone-ai/OpenAI/ImageToImageControls.php:45
msgid "GPT Image 1"
msgstr ""

#: plugins/cornerstone-ai/OpenAI/TextToImageControls.php:48
msgid "Number of Images"
msgstr ""

#: plugins/cornerstone-ai/OpenAI/TextToSpeechControls.php:32
msgid "Voice"
msgstr ""

#: plugins/cornerstone-ai/OpenAI/TextToSpeechControls.php:35
msgid "Select a Voice"
msgstr ""

#: plugins/cornerstone-ai/PromptLibrary/Controls.php:6
msgid "Prompts"
msgstr ""

#: plugins/cornerstone-ai/Settings/Controls.php:59
msgid "System Prompt"
msgstr ""

#: plugins/cornerstone-ai/Settings/Controls.php:60
msgid "Add an additional system prompt onto all text responses."
msgstr ""

#: plugins/cornerstone-ai/Settings/Controls.php:77
msgid "Optimize"
msgstr ""

#: plugins/cornerstone-ai/Settings/Controls.php:78
msgid "This requires having pngquant and optipng installed on your server to work properly."
msgstr ""

#: plugins/cornerstone-charts/extension/Elements/Data.php:28
msgid "Data"
msgstr ""

#: plugins/cornerstone-charts/extension/Elements/Data.php:300
msgid "JSON"
msgstr ""

#: plugins/cornerstone-charts/extension/Elements/Dataset.php:51
msgid "Dataset"
msgstr ""

#: plugins/cornerstone-charts/extension/Elements/Labelset.php:29
msgid "Labelset"
msgstr ""

#: plugins/cornerstone-charts/extension/Prefabs/Prefabs.php:77
msgid "Labelset Days of Week"
msgstr ""

#: plugins/cornerstone-charts/extension/Prefabs/Prefabs.php:83
msgid "Dataset Days of Week"
msgstr ""

#: plugins/cornerstone-charts/extension/Prefabs/Prefabs.php:93
msgid "Labelset Months of Year"
msgstr ""

#: plugins/cornerstone-charts/extension/Prefabs/Prefabs.php:99
msgid "Dataset Months of Year"
msgstr ""

(()=>{var c={strings:[],typeSpeed:0,startDelay:0,backSpeed:0,shuffle:!1,backDelay:500,loop:!1,loopCount:!1,showCursor:!0,cursorChar:"|",attr:null,contentType:"html",callback:function(){},preStringTyped:function(){},onStringTyped:function(){},resetCallback:function(){}},h=function(e,s){this.el=e,this.options={...c,...s},this.isInput=this.el.matches("input"),this.attr=this.options.attr,this.showCursor=this.isInput?!1:this.options.showCursor,this.elContent=this.attr?this.el.getAttribute(this.attr):this.el.textContent,this.contentType=this.options.contentType,this.typeSpeed=this.options.typeSpeed,this.startDelay=this.options.startDelay,this.backSpeed=this.options.backSpeed,this.backDelay=this.options.backDelay,this.strings=this.options.strings,this.strPos=0,this.arrayPos=0,this.stopNum=0,this.loop=this.options.loop,this.loopCount=this.options.loopCount,this.curLoop=0,this.stop=!1,this.cursorChar=this.options.cursorChar,this.shuffle=this.options.shuffle,this.sequence=[],this.timeouts=[],this.build()};h.prototype={constructor:h,init:function(){var e=this;e.addTimeout(function(){for(var s=0;s<e.strings.length;++s)e.sequence[s]=s;e.shuffle&&(e.sequence=e.shuffleArray(e.sequence)),e.typewrite(e.strings[e.sequence[e.arrayPos]],e.strPos)},e.startDelay)},build:function(){var e=this;e.addTimeout(()=>{let s=e.el.parentNode.querySelectorAll(".x-typed-cursor");for(var i=0;i<s.length;++i)s[i].remove();this.showCursor===!0&&(this.cursor=document.createElement("span"),this.cursor.classList.add("x-typed-cursor"),this.cursor.innerText=this.cursorChar,this.el.insertAdjacentElement("afterend",this.cursor)),e.stop=!1,e.init()},500)},typewrite:function(e,s){if(this.stop!==!0){var i=Math.round(Math.random()*(100-30))+this.typeSpeed,t=this;t.addTimeout(function(){var n=0,o=e.substr(s);if(o.charAt(0)==="^"){var u=1;/^\^\d+/.test(o)&&(o=/\d+/.exec(o)[0],u+=o.length,n=parseInt(o)),e=e.substring(0,s)+e.substring(s+u)}if(t.contentType==="html"){var l=e.substr(s).charAt(0);if(l==="<"||l==="&"){var f="",a="";for(l==="<"?a=">":a=";";e.substr(s).charAt(0)!==a;)f+=e.substr(s).charAt(0),s++;s++,f+=a}}t.addTimeout(function(){if(s===e.length){if(t.options.onStringTyped(t.arrayPos),t.arrayPos===t.strings.length-1&&(t.options.callback(),t.curLoop++,t.loop===!1||t.curLoop===t.loopCount)){t.stop=!0,t.clearTimeouts();return}t.addTimeout(function(){t.backspace(e,s)},t.backDelay)}else{s===0&&t.options.preStringTyped(t.arrayPos);var r=e.substr(0,s+1);t.attr?t.el.getAttribute(t.attr,r):t.isInput?t.el.setAttribute("value",r):t.contentType==="html"?t.el.innerHTML=r:t.el.textContent=r,s++,t.typewrite(e,s)}},n)},i)}},backspace:function(e,s){if(this.stop!==!0){var i=Math.round(Math.random()*(100-30))+this.backSpeed,t=this;t.addTimeout(function(){if(t.contentType==="html"&&e.substr(s).charAt(0)===">"){for(var n="";e.substr(s).charAt(0)!=="<";)n-=e.substr(s).charAt(0),s--;s--,n+="<"}var o=e.substr(0,s);t.attr?t.el.attr(t.attr,o):t.isInput?t.el.val(o):t.contentType==="html"?t.el.innerHTML=o:t.el.textContent=o,s>t.stopNum?(s--,t.addTimeout(()=>{t.backspace(e,s)},i)):s<=t.stopNum&&(t.arrayPos++,t.arrayPos===t.strings.length?(t.arrayPos=0,t.shuffle&&(t.sequence=t.shuffleArray(t.sequence)),t.init()):t.typewrite(t.strings[t.sequence[t.arrayPos]],s))},i)}},shuffleArray:function(e){var s,i,t=e.length;if(t)for(;--t;)i=Math.floor(Math.random()*(t+1)),s=e[i],e[i]=e[t],e[t]=s;return e},addTimeout:function(e,s){if(this.clearTimeouts(),this.stop)return;let i,t=setTimeout(()=>{e(),this.timeouts.splice(i,1)},s);i=this.timeouts.push(t)},clearTimeouts:function(){this.timeouts.map((e,s)=>{clearTimeout(e),this.timeouts.splice(s,1)})},reset:function(){var e=this;e.stop=!0,this.clearTimeouts();var s=this.el.getAttribute("id");let i=document.createElement("span");i.setAttribute("id",s),this.el.insertAdjacentElement("afterend",i),this.el.remove(),typeof this.cursor!="undefined"&&this.cursor.remove(),e.options.resetCallback()}};function p(e,s={}){let i=new h(e,s);return()=>{i.reset()}}var{attach:d}=window.csGlobal.rivet;d("[data-x-element-text-type]",(e,s)=>p(e.querySelector(".text, .x-text-typing"),{strings:s.strings,typeSpeed:s.type_speed,startDelay:s.start_delay,backSpeed:s.back_speed,backDelay:s.back_delay,loop:s.loop,showCursor:s.show_cursor,cursorChar:s.cursor}));})();
